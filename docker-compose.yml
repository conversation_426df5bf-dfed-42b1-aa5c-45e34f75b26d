version: "2.2"
services:

  # PostgreSQL Database
  postgresql-postgresql:
    image: postgres:13
    volumes:
      - dbdata1:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=search
    mem_limit: '512M'
    networks:
      - app-network

  # Legacy Elasticsearch 6.x (existing)
  elasticsearch-legacy:
    image: elasticsearch:6.8.17
    environment:
      - cluster.name=elasticsearch-legacy
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
    volumes:
      - esdata_legacy:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
      - "9300:9300"
    mem_limit: '2048M'
    networks:
      - app-network
      - elastic

  # New Elasticsearch 8.x (latest)
  elasticsearch-new:
    image: elasticsearch:8.11.3
    environment:
      - cluster.name=elasticsearch-new
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
    volumes:
      - esdata_new:/usr/share/elasticsearch/data
    ports:
      - "9400:9200"
      - "9500:9300"
    mem_limit: '2048M'
    networks:
      - app-network
      - elastic

  # RabbitMQ Message Queue
  rabbitmq:
    image: rabbitmq:3.9-management
    environment:
      - RABBITMQ_DEFAULT_USER=test
      - RABBITMQ_DEFAULT_PASS=test
      - RABBITMQ_DEFAULT_VHOST=sling-sales
    ports:
      - "5672:5672"
      - "15672:15672"
    mem_limit: '512M'
    networks:
      - app-network

  # Memcached for caching
  memcached:
    image: memcached:1.6
    ports:
      - "11211:11211"
    mem_limit: '128M'
    networks:
      - app-network

#  new-elasticsearch-master:
#    image: elasticsearch:6.6.0
#    environment:
#      - cluster.name=new-elasticsearch-master
#      - discovery.type=single-node
#      - http.port=9400
#    volumes:
#      - esdata5:/usr/share/elasticsearch/data
#    ports:
#      - "9400:9400"
#    mem_limit: '2048M'

#  rabbitmq:
#    image: amura/rabbitmq:3.7
#    build: ./local-deploy/queue
#    environment:
#      - ERLANG_COOKIE=abcdefg
#    ports:
#      - "5672:5672"
#      - "15672:15672"
#    mem_limit: '200M'
#  iam:
#    image: nexus.sling-dev.com:8023/sling/sd-iam:latest
#    entrypoint: java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=8001 -Xmx410m -XX:+UseSerialGC -Dmanagement.health.defaults.enabled=false -Dmanagement.endpoint.health.show-details=ALWAYS -Delasticsearch.host=elasticsearch-master -Dspring.datasource.url=************************************************ -Dclient.entity.basePath=http://config:8086 -Dcore.rabbitmq.host=rabbitmq -Dup.config.dir=/usr/src/myapp/config/  -jar app.jar
#    volumes:
#      - ./iam/:/usr/src/app/config/
#    ports:
#      - "8081:8081"
#      - "8001:8001"
#    depends_on:
#      - rabbitmq
#      - postgresql-postgresql
#      - elasticsearch-master
#    mem_limit: '600M'
volumes:
  dbdata1:
  esdata_legacy:
  esdata_new:

networks:
  app-network:
    driver: bridge
  elastic:
    driver: bridge