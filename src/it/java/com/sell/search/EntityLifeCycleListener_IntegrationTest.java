package com.sell.search;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.deleteRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.putRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static org.springframework.test.context.support.TestPropertySourceUtils.addInlinedPropertiesToEnvironment;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.stubbing.ServeEvent;
import com.sell.search.EntityLifeCycleListener_IntegrationTest.TestDatabaseInitializer;
import com.sell.search.EntityLifeCycleListener_IntegrationTest.TestEnvironmentSetup;
import com.sell.search.domain.retry.RetryEntity;
import com.sell.search.domain.retry.ErrorMessageRecoveryFacade;
import com.sell.search.domain.retry.RetryEntityRepository;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import javax.persistence.criteria.Predicate;
import javax.sql.DataSource;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.json.JSONException;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit4.SpringRunner;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.RabbitMQContainer;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT, classes = SearchApplication.class)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestDatabaseInitializer.class, TestEnvironmentSetup.class})
@TestPropertySource(
    properties = {
        "client.entity.basePath=http://localhost:9090",
        "client.iam.basePath=http://localhost:9090",
        "newElasticsearch.host=localhost",
        "newElasticsearch.port=7071"
    })
public class EntityLifeCycleListener_IntegrationTest {

  public static WireMockServer wireMockNewEsHost = new WireMockServer(7071);
  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private AmqpAdmin rabbitAdmin;
  @Autowired
  private RabbitTemplate rabbitTemplate;
  @Autowired
  private ResourceLoader resourceLoader;
  @Autowired
  private RetryEntityRepository retryEntityRepository;
  @Autowired
  private ErrorMessageRecoveryFacade errorMessageRecoveryFacade;


  private static RabbitMQContainer rabbitMQContainer =
      new RabbitMQContainer("rabbitmq:3.7-management-alpine");

  private static final String SEARCH_QUEUE = "q.search";
  private static final String DEAL_EXCHANGE = "ex.deal";
  public static final String DEAL_DELETED_EVENT = "deal.deleted";
  public static final String DEAL_CREATED_EVENT = "deal.created";
  public static final String DEAL_UPDATED_EVENT = "deal.updated";
  private static final String COMPANY_EXCHANGE = "ex.company";
  private static final String SALES_EXCHANGE = "ex.sales";
  public static final String COMPANY_UPDATED_EVENT = "company.updated";
  public static final String COMPANY_CREATED_EVENT = "company.created";
  public static final String CONTACT_CREATED_EVENT = "sales.contact.created";
  public static final String LEAD_CREATED_EVENT = "sales.lead.created";
  public static final String LEAD_UPDATED_EVENT = "sales.lead.updated";
  public static final String LEAD_METAINFO_UPDATED_EVENT = "sales.lead.metainfo.updated";
  public static final String DEAL_METAINFO_UPDATED_EVENT = "deal.metainfo.updated";
  public static final String COMPANY_DELETED_EVENT = "company.deleted";
  public static final String LEAD_DELETED_EVENT = "sales.lead.deleted";
  public static final String CONTACT_DELETED_EVENT = "sales.contact.deleted";

  @BeforeClass
  public static void setUp() {
    wireMockNewEsHost.start();
  }

  @Before
  public void beforeEach() {
    wireMockNewEsHost.resetAll();
  }

  @AfterClass
  public static void tearDown() {
    rabbitMQContainer.stop();
    wireMockNewEsHost.stop();
  }

  @Test
  public void givenDealDeletedCommand_shouldDeleteDocumentFromIndex() throws InterruptedException, IOException {
    //given
    CountDownLatch latch = new CountDownLatch(1);
    long dealId = 1L;
    long tenantId = 10L;
    final HashMap<String, Object> dealDeleteEvent = new HashMap<String, Object>() {{
      put("id", dealId);
      put("version", 1);
      put("tenantId", tenantId);
    }};
    wireMockNewEsHost.stubFor(
        WireMock.delete(urlEqualTo("/10-deal/deal/1?timeout=1m"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString("classpath:contracts/es/response/delete-index-response-sample.json"))));
    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DEAL_DELETED_EVENT, dealDeleteEvent);
    latch.await(3, TimeUnit.SECONDS);

    //then
    wireMockNewEsHost.verify(deleteRequestedFor(
        urlEqualTo("/10-deal/deal/1?timeout=1m")
    ));
  }

  @Test
  public void givenLeadDeletedCommand_shouldDeleteDocumentFromIndex() throws InterruptedException, IOException {
    //given
    CountDownLatch latch = new CountDownLatch(1);
    long leadId = 1L;
    long tenantId = 10L;
    final HashMap<String, Object> leadDeletedEvent = new HashMap<String, Object>() {{
      put("id", leadId);
      put("version", 1);
      put("tenantId", tenantId);
    }};

    wireMockNewEsHost.stubFor(
        WireMock.delete(urlEqualTo("/10-lead/lead/1?timeout=1m"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString("classpath:contracts/es/response/delete-index-response-sample-lead.json"))));
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LEAD_DELETED_EVENT, leadDeletedEvent);
    latch.await(3, TimeUnit.SECONDS);

    //then
    wireMockNewEsHost.verify(deleteRequestedFor(
        urlEqualTo("/10-lead/lead/1?timeout=1m")
    ));
  }

  @Test
  public void givenContactDeletedCommand_shouldDeleteDocumentFromIndex() throws InterruptedException, IOException {
    //given
    CountDownLatch latch = new CountDownLatch(1);
    long contactId = 1L;
    long tenantId = 10L;
    final HashMap<String, Object> contactDeletedEvent = new HashMap<String, Object>() {{
      put("id", contactId);
      put("version", 1);
      put("tenantId", tenantId);
    }};
    wireMockNewEsHost.stubFor(
        WireMock.delete(urlEqualTo("/10-contact/contact/1?timeout=1m"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString("classpath:contracts/es/response/delete-index-response-sample-contact.json"))));
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, CONTACT_DELETED_EVENT, contactDeletedEvent);
    latch.await(3, TimeUnit.SECONDS);

    //then
    wireMockNewEsHost.verify(deleteRequestedFor(
        urlEqualTo("/10-contact/contact/1?timeout=1m")
    ));
  }

  @Test
  public void givenCompanyDeletedCommand_shouldDeleteDocumentFromIndex() throws InterruptedException, IOException {
    //given
    CountDownLatch latch = new CountDownLatch(1);
    long companyId = 1L;
    long tenantId = 10L;
    final HashMap<String, Object> companyDeleteEvent = new HashMap<String, Object>() {{
      put("id", companyId);
      put("version", 1);
      put("tenantId", tenantId);
    }};

    wireMockNewEsHost.stubFor(
        WireMock.delete(urlEqualTo("/10-company/company/1?timeout=1m"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString("classpath:contracts/es/response/delete-index-response-sample.json"))));

    // when
    rabbitTemplate.convertAndSend(COMPANY_EXCHANGE, COMPANY_DELETED_EVENT, companyDeleteEvent);
    latch.await(3, TimeUnit.SECONDS);
    //then

    wireMockNewEsHost.verify(deleteRequestedFor(
        urlEqualTo("/10-company/company/1?timeout=1m")
    ));

  }

  @Test
  @Sql("/test-scripts/create-and-map-custom-field-and-es-field-deal.sql")
  public void givenDealCreateCommand_shouldCreateDocumentInIndex() throws InterruptedException, IOException {
    //given
    CountDownLatch latch = new CountDownLatch(1);
    String dealCreatedEventPayloadString = getResourceAsString("classpath:contracts/deal/deal-create-payload.json");
    TypeReference<HashMap<String, Object>> dealPayloadToHasMap = new TypeReference<HashMap<String, Object>>() {
    };
    HashMap<String, Object> dealCreatedEventPayload = new ObjectMapper().readValue(dealCreatedEventPayloadString, dealPayloadToHasMap);
    wireMockNewEsHost.stubFor(WireMock.put(urlEqualTo("/1-deal/deal/1?version_type=external&version=1&timeout=1m"))
        .willReturn(
            aResponse()
                .withStatus(201)
                .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody(
                    getResourceAsString(
                        "classpath:contracts/es/response/create-index-response-sample.json"))));

    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DEAL_CREATED_EVENT, dealCreatedEventPayload);
    latch.await(3, TimeUnit.SECONDS);

    //then
    wireMockNewEsHost
        .verify(1,
            putRequestedFor(
                urlEqualTo("/1-deal/deal/1?version_type=external&version=1&timeout=1m")
            )
                .withRequestBody(equalToJson(getResourceAsString("classpath:contracts/deal-created-index-request.json")))
        );
  }

  @Test
  public void givenDealUpdateCommand_shouldUpdateDocumentInIndex() throws InterruptedException, IOException {
    //given
    CountDownLatch latch = new CountDownLatch(1);
    long dealId = 1L;
    long tenantId = 10L;
    final HashMap<String, Object> dealUpdateEvent = new HashMap<String, Object>() {{
      put("id", dealId);
      put("version", 1);
      put("tenantId", tenantId);
      put("name", "Updated Deal Name");
    }};

    wireMockNewEsHost.stubFor(WireMock.put(urlEqualTo("/10-deal/deal/1?version_type=external&version=1&timeout=1m"))
        .willReturn(
            aResponse()
                .withStatus(201)
                .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody(
                    getResourceAsString(
                        "classpath:contracts/es/response/create-index-response-sample.json"))));

    wireMockNewEsHost.stubFor(WireMock.head(urlEqualTo("/10-deal/deal/1"))
        .willReturn(
            aResponse()
                .withStatus(200)));

    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DEAL_UPDATED_EVENT, dealUpdateEvent);
    latch.await(3, TimeUnit.SECONDS);

    //then
    wireMockNewEsHost
        .verify(
            putRequestedFor(
                urlEqualTo("/10-deal/deal/1?version_type=external&version=1&timeout=1m")
            )
                .withRequestBody(equalToJson(getResourceAsString("classpath:contracts/deal-updated-index-request.json")))
        );
  }

  @Test
  public void givenEntityUpdatedEvent_shouldSaveEventToTableOnException() throws InterruptedException, IOException {
    //given
    CountDownLatch latch = new CountDownLatch(1);
    final HashMap<String, Object> dealUpdatedEvent = new HashMap<String, Object>() {{
      put("id", 1);
      put("version", 1);
      put("tenantId", 10);
      put("name", "Updated Deal Name");
    }};

    wireMockNewEsHost.stubFor(WireMock.head(urlEqualTo("/10-deal/deal/1"))
        .willReturn(
            aResponse()
                .withStatus(404)));

    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DEAL_UPDATED_EVENT, dealUpdatedEvent);
    latch.await(3, TimeUnit.SECONDS);

    //then
    Optional<RetryEntity> deal = retryEntityRepository.findByEntityTypeAndEntityId("deal", 1L);
    Assertions.assertThat(deal.isPresent()).isTrue();
    Assertions.assertThat(deal.get().getErrorMessage()).isEqualTo("entity.not.found");
    Assertions.assertThat(deal.get().getRetryCount()).isEqualTo(0L);
    Assertions.assertThat(deal.get().getTenantId()).isEqualTo(10L);
    Assertions.assertThat(deal.get().getEntityPayload()).containsExactlyInAnyOrderEntriesOf(dealUpdatedEvent);

    wireMockNewEsHost.verify(0, putRequestedFor(
        urlEqualTo("/10-deal/deal/1?version_type=external&version=1&timeout=1m")).withRequestBody(
        equalToJson(getResourceAsString("classpath:contracts/deal-updated-index-request.json"))));
  }


  @Test
  @Sql("/test-scripts/create-and-map-custom-field-and-es-field-company.sql")
  public void givenCompanyCreateCommand_shouldCreateDocumentInIndex() throws InterruptedException, IOException {
    //given
    CountDownLatch latch = new CountDownLatch(1);
    String companyPayloadString = getResourceAsString("classpath:contracts/company/company-create-payload.json");
    TypeReference<HashMap<String, Object>> companyPayloadToHasMap = new TypeReference<HashMap<String, Object>>() {
    };
    HashMap<String, Object> companyCreatedEvent = new ObjectMapper().readValue(companyPayloadString, companyPayloadToHasMap);
    wireMockNewEsHost.stubFor(WireMock.put(urlEqualTo("/1-company/company/1?version_type=external&version=1&timeout=1m"))
        .willReturn(
            aResponse()
                .withStatus(201)
                .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody(
                    getResourceAsString(
                        "classpath:contracts/es/response/create-index-response-sample.json"))));
    // when
    rabbitTemplate.convertAndSend(COMPANY_EXCHANGE, COMPANY_CREATED_EVENT, companyCreatedEvent);
    latch.await(3, TimeUnit.SECONDS);

    //then
    wireMockNewEsHost
        .verify(1,
            putRequestedFor(
                urlEqualTo("/1-company/company/1?version_type=external&version=1&timeout=1m")
            )
                .withRequestBody(equalToJson(getResourceAsString("classpath:contracts/company-created-index-request.json")))
        );
  }

  @Test
  public void givenCompanyUpdateCommand_shouldCreateDocumentInIndex() throws InterruptedException, IOException {
    //given
    CountDownLatch latch = new CountDownLatch(1);
    long companyId = 1L;
    long tenantId = 10L;
    final HashMap<String, Object> companyCreateEvent = new HashMap<String, Object>() {{
      put("id", companyId);
      put("version", 1);
      put("tenantId", tenantId);
      put("name", "Sling");
    }};

    wireMockNewEsHost.stubFor(WireMock.put(urlEqualTo("/10-company/company/1?version_type=external&version=1&timeout=1m"))
        .willReturn(
            aResponse()
                .withStatus(201)
                .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody(
                    getResourceAsString(
                        "classpath:contracts/es/response/create-index-response-sample.json"))));
    // when
    rabbitTemplate.convertAndSend(COMPANY_EXCHANGE, COMPANY_CREATED_EVENT, companyCreateEvent);
    latch.await(3, TimeUnit.SECONDS);

    //then

    wireMockNewEsHost
        .verify(
            putRequestedFor(
                urlEqualTo("/10-company/company/1?version_type=external&version=1&timeout=1m")
            )
                .withRequestBody(equalToJson(getResourceAsString("classpath:contracts/company-updated-index-request.json")))
        );
  }

  @Test
  @Sql("/test-scripts/map-custom-field-with-es-field-name.sql")
  public void givenContactCreatedCommand_shouldCreateDocumentInIndex() throws InterruptedException, IOException {
    //given
    CountDownLatch latch = new CountDownLatch(1);
    long contactId = 1L;
    long tenantId = 10L;

    final HashMap<String, Object> customFieldValues = new HashMap<String, Object>() {{
      put("date", "2021-09-22T06:30:00.000Z");
      put("pickList", 1046);
      put("customMultiPicklist", Arrays.asList(98L, 99L));
    }};

    Map<String, Object> idNameStore = new HashMap<String, Object>() {{
      put("company", Collections.emptyMap());
    }};

    final HashMap<String, Object> contactCreatedEvent = new HashMap<String, Object>() {{
      put("id", contactId);
      put("version", 1);
      put("tenantId", tenantId);
      put("department", "Product");
      put("customFieldValues", customFieldValues);
      put("idNameStore", idNameStore);
    }};

    wireMockNewEsHost.stubFor(WireMock.put(urlEqualTo("/10-contact/contact/1?version_type=external&version=1&timeout=1m"))
        .willReturn(
            aResponse()
                .withStatus(201)
                .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody(
                    getResourceAsString(
                        "classpath:contracts/es/response/create-index-response-sample.json"))));
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, CONTACT_CREATED_EVENT, contactCreatedEvent);
    latch.await(3, TimeUnit.SECONDS);

    //then

    wireMockNewEsHost
        .verify(1,
            putRequestedFor(
                urlEqualTo("/10-contact/contact/1?version_type=external&version=1&timeout=1m")
            )
                .withRequestBody(equalToJson(getResourceAsString("classpath:contracts/contact-created-index-request-newES.json"), true, false))
        );
  }

  @Test
  @Sql("/test-scripts/map-custom-field-with-es-field-name-lead.sql")
  public void givenLeadCreatedCommand_shouldCreateDocumentInIndex() throws InterruptedException, IOException {
    //given
    CountDownLatch latch = new CountDownLatch(1);
    long leadId = 1L;
    long tenantId = 10L;

    final HashMap<String, Object> customFieldValues = new HashMap<String, Object>() {{
      put("date", "2021-09-22T06:30:00.000Z");
      put("pickList", 1046);
      put("myMultiPicklist", Arrays.asList(2046, 2047));
    }};

    Map<String, Object> idNameStore = new HashMap<String, Object>() {{
      put("source", Collections.emptyMap());
    }};

    final HashMap<String, Object> leadCreatedEvent = new HashMap<String, Object>() {{
      put("id", leadId);
      put("version", 1);
      put("tenantId", tenantId);
      put("firstName", "Tony");
      put("lastName", "Stark");
      put("department", "Product");
      put("customFieldValues", customFieldValues);
      put("idNameStore", idNameStore);
    }};

    wireMockNewEsHost.stubFor(WireMock.put(urlEqualTo("/10-lead/lead/1?version_type=external&version=1&timeout=1m"))
        .willReturn(
            aResponse()
                .withStatus(201)
                .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody(
                    getResourceAsString(
                        "classpath:contracts/es/response/create-index-response-sample.json"))));
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LEAD_CREATED_EVENT, leadCreatedEvent);
    latch.await(3, TimeUnit.SECONDS);

    //then

    wireMockNewEsHost
        .verify(1,
            putRequestedFor(
                urlEqualTo("/10-lead/lead/1?version_type=external&version=1&timeout=1m")
            )
                .withRequestBody(equalToJson(getResourceAsString("classpath:contracts/entity-created-index-request-newES.json"), true, false))
        );
  }

  @Test
  @Sql("/test-scripts/map-custom-field-with-es-field-name-lead.sql")
  public void givenLeadUpdatedCommand_shouldIndexDocumentIntoMainAndGlobalSearchIndex() throws InterruptedException, IOException, JSONException {
    //given
    CountDownLatch latch = new CountDownLatch(1);
    long leadId = 1L;
    long tenantId = 10L;

    final HashMap<String, Object> customFieldValues = new HashMap<String, Object>() {{
      put("date", "2021-09-22T06:30:00.000Z");
      put("pickList", 1046);
      put("myMultiPicklist", Arrays.asList(2046, 2047));
    }};

    Map<String, Object> idNameStore = new HashMap<String, Object>() {{
      put("source", Collections.emptyMap());
    }};

    final HashMap<String, Object> leadEvent = new HashMap<String, Object>() {{
      put("id", leadId);
      put("version", 1);
      put("firstName", "Tony");
      put("lastName", "Stark");
      put("tenantId", tenantId);
      put("department", "Product");
      put("customFieldValues", customFieldValues);
      put("idNameStore", idNameStore);
    }};

    wireMockNewEsHost.stubFor(WireMock.put(urlEqualTo("/10-lead/lead/1?version_type=external&version=1&timeout=1m"))
        .willReturn(
            aResponse()
                .withStatus(201)
                .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody(
                    getResourceAsString(
                        "classpath:contracts/es/response/create-index-response-sample.json"))));

    wireMockNewEsHost.stubFor(WireMock.head(urlEqualTo("/10-lead/lead/1"))
        .willReturn(
            aResponse()
                .withStatus(200)));
    wireMockNewEsHost.stubFor(WireMock.put(urlEqualTo("/10-global-search/store/LEAD_1?version_type=external&version=1&timeout=1m"))
        .willReturn(
            aResponse()
                .withStatus(201)
                .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody(
                    getResourceAsString(
                        "classpath:contracts/es/response/create-index-response-sample.json"))));

    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LEAD_UPDATED_EVENT, leadEvent);
    latch.await(3, TimeUnit.SECONDS);

    //then

    wireMockNewEsHost
        .verify(1,
            putRequestedFor(
                urlEqualTo("/10-lead/lead/1?version_type=external&version=1&timeout=1m")
            )
            .withRequestBody(equalToJson(getResourceAsString("classpath:contracts/entity-created-index-request-newES.json"), true, false))
        );

    wireMockNewEsHost
        .verify(1,
            putRequestedFor(
                urlEqualTo("/10-global-search/store/LEAD_1?version_type=external&version=1&timeout=1m")
            )
            .withRequestBody(equalToJson(getResourceAsString("classpath:contracts/entity-update-index-into-lead-globalSearch-request-newES.json"), true, false))
        );
  }

  @Test
  @Sql("/test-scripts/map-custom-field-with-es-field-name-lead.sql")
  public void givenLeadMetainfoUpdatedCommand_shouldIndexDocumentIntoMainIndexOnly() throws Exception {
    //given
    CountDownLatch latch = new CountDownLatch(1);
    long leadId = 1L;
    long tenantId = 10L;

    final HashMap<String, Object> customFieldValues = new HashMap<String, Object>() {{
      put("date", "2021-09-22T06:30:00.000Z");
      put("pickList", 1046);
      put("myMultiPicklist", Arrays.asList(2046, 2047));
    }};

    Map<String, Object> idNameStore = new HashMap<String, Object>() {{
      put("source", Collections.emptyMap());
    }};

    final HashMap<String, Object> leadEvent = new HashMap<String, Object>() {{
      put("id", leadId);
      put("firstName", "Tony");
      put("lastName", "Stark");
      put("version", 1);
      put("tenantId", tenantId);
      put("department", "Product");
      put("customFieldValues", customFieldValues);
      put("idNameStore", idNameStore);
    }};

    wireMockNewEsHost.stubFor(WireMock.put(urlEqualTo("/10-lead/lead/1?version_type=external&version=1&timeout=1m"))
        .willReturn(
            aResponse()
                .withStatus(201)
                .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody(
                    getResourceAsString(
                        "classpath:contracts/es/response/create-index-response-sample.json"))));

    wireMockNewEsHost.stubFor(WireMock.head(urlEqualTo("/10-lead/lead/1"))
        .willReturn(
            aResponse()
                .withStatus(200)));

    wireMockNewEsHost.stubFor(WireMock.put(urlEqualTo("/10-global-search/lead/LEAD_1?version_type=external&version=1&timeout=1m"))
        .willReturn(
            aResponse()
                .withStatus(201)
                .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody(
                    getResourceAsString(
                        "classpath:contracts/es/response/create-index-response-sample.json"))));

    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LEAD_METAINFO_UPDATED_EVENT, leadEvent);
    latch.await(3, TimeUnit.SECONDS);

    //then

    wireMockNewEsHost
        .verify(1,
            putRequestedFor(
                urlEqualTo("/10-lead/lead/1?version_type=external&version=1&timeout=1m")
            )
                .withRequestBody(equalToJson(getResourceAsString("classpath:contracts/entity-created-index-request-newES.json"), true, false))
        );
  }


  @Test
  @Sql("/test-scripts/map-custom-field-and-es-field-deal.sql")
  public void givenDealMetainfoUpdatedCommand_shouldIndexDocumentIntoMainIndexOnly() throws Exception {
    //given
    CountDownLatch latch = new CountDownLatch(1);
    long dealId = 1L;
    long tenantId = 55L;

    final HashMap<String, Object> customFieldValues = new HashMap<String, Object>() {{
      put("myPicklist", 1046);
    }};

    Map<String, Object> idNameStore = new HashMap<String, Object>() {{
      put("source", Collections.emptyMap());
    }};

    final HashMap<String, Object> dealEvent = new HashMap<String, Object>() {{
      put("id", dealId);
      put("firstName", "Tony");
      put("lastName", "Stark");
      put("version", 1);
      put("tenantId", tenantId);
      put("department", "Product");
      put("customFieldValues", customFieldValues);
      put("idNameStore", idNameStore);
    }};

    wireMockNewEsHost.stubFor(WireMock.put(urlEqualTo("/55-deal/deal/1?version_type=external&version=1&timeout=1m"))
        .willReturn(
            aResponse()
                .withStatus(201)
                .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody(
                    getResourceAsString(
                        "classpath:contracts/es/response/create-index-response-sample.json"))));

    wireMockNewEsHost.stubFor(WireMock.head(urlEqualTo("/55-deal/deal/1"))
        .willReturn(
            aResponse()
                .withStatus(200)));

    wireMockNewEsHost.stubFor(WireMock.put(urlEqualTo("/55-global-search/deal/DEAL_1?version_type=external&version=1&timeout=1m"))
        .willReturn(
            aResponse()
                .withStatus(201)
                .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody(
                    getResourceAsString(
                        "classpath:contracts/es/response/create-index-response-sample.json"))));

    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DEAL_METAINFO_UPDATED_EVENT, dealEvent);
    latch.await(3, TimeUnit.SECONDS);

    //then

    wireMockNewEsHost
        .verify(1,
            putRequestedFor(
                urlEqualTo("/55-deal/deal/1?version_type=external&version=1&timeout=1m")
            )
                .withRequestBody(equalToJson(getResourceAsString("classpath:contracts/deal-created-index-request-newES.json"), true, false))
        );
  }

  @Test
  @Sql("/test-scripts/map-custom-field-with-es-field-name-lead.sql")
  public void givenLeadCreatedCommandWithCustomFieldAsNull_shouldCreateDocumentInIndex() throws InterruptedException, IOException {
    //given
    CountDownLatch latch = new CountDownLatch(1);
    long leadId = 1L;
    long tenantId = 10L;

    final HashMap<String, Object> leadCreatedEvent = new HashMap<String, Object>() {{
      put("id", leadId);
      put("version", 1);
      put("tenantId", tenantId);
      put("department", "Product");
      put("customFieldValues", null);
    }};

    wireMockNewEsHost.stubFor(WireMock.put(urlEqualTo("/10-lead/lead/1?version_type=external&version=1&timeout=1m"))
        .willReturn(
            aResponse()
                .withStatus(201)
                .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody(
                    getResourceAsString(
                        "classpath:contracts/es/response/create-index-response-sample.json"))));

    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LEAD_CREATED_EVENT, leadCreatedEvent);
    latch.await(3, TimeUnit.SECONDS);

    //then

    wireMockNewEsHost
        .verify(1,
            putRequestedFor(
                urlEqualTo("/10-lead/lead/1?version_type=external&version=1&timeout=1m")
            )
                .withRequestBody(
                    equalToJson(getResourceAsString("classpath:contracts/entity-created-index-request-without-customField.json"), true, false))
        );
  }

  @Test
  @Sql("/test-scripts/map-custom-field-with-es-field-name-lead.sql")
  public void givenLeadCreatedCommandWithNonMappedCustomField_shouldCreateDocumentInIndex() throws InterruptedException, IOException {
    //given
    CountDownLatch latch = new CountDownLatch(1);
    long leadId = 1L;
    long tenantId = 10L;

    final HashMap<String, Object> customFieldValues = new HashMap<String, Object>() {{
      put("youName", "Tony");
    }};

    final HashMap<String, Object> leadCreatedEvent = new HashMap<String, Object>() {{
      put("id", leadId);
      put("version", 1);
      put("tenantId", tenantId);
      put("department", "Product");
      put("customFieldValues", customFieldValues);
    }};

    wireMockNewEsHost.stubFor(WireMock.put(urlEqualTo("/10-lead/lead/1?version_type=external&version=1&timeout=1m"))
        .willReturn(
            aResponse()
                .withStatus(201)
                .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody(
                    getResourceAsString(
                        "classpath:contracts/es/response/create-index-response-sample.json"))));

    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LEAD_CREATED_EVENT, leadCreatedEvent);
    latch.await(3, TimeUnit.SECONDS);

    //then

    wireMockNewEsHost
        .verify(1,
            putRequestedFor(
                urlEqualTo("/10-lead/lead/1?version_type=external&version=1&timeout=1m")
            )
                .withRequestBody(
                    equalToJson(getResourceAsString("classpath:contracts/entity-created-index-request-without-customField.json"), true, false))
        );
  }

  @Test
  @Sql("/test-scripts/retry-entity-error-message-for-delete.sql")
  public void givenDealUpdateCommandWithOutdatedVersion_shouldFailAndShouldNotStoreErrorForRetry() throws InterruptedException, IOException {
    //given
    CountDownLatch latch = new CountDownLatch(1);
    long dealId = 1L;
    long tenantId = 10L;
    final HashMap<String, Object> dealUpdateEvent = new HashMap<String, Object>() {{
      put("id", dealId);
      put("version", 1);
      put("tenantId", tenantId);
      put("name", "Updated Deal Name");
    }};

    wireMockNewEsHost.stubFor(WireMock.put(urlEqualTo("/10-deal/deal/1?version_type=external&version=1&timeout=1m"))
        .willReturn(
            aResponse()
                .withStatus(409)));

    wireMockNewEsHost.stubFor(WireMock.head(urlEqualTo("/10-deal/deal/1"))
        .willReturn(
            aResponse()
                .withStatus(200)));

    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DEAL_UPDATED_EVENT, dealUpdateEvent);
    latch.await(3, TimeUnit.SECONDS);

    //then
    wireMockNewEsHost
        .verify(
            putRequestedFor(
                urlEqualTo("/10-deal/deal/1?version_type=external&version=1&timeout=1m")
            )
                .withRequestBody(equalToJson(getResourceAsString("classpath:contracts/deal-updated-index-request.json")))
        );
    Optional<RetryEntity> deal = retryEntityRepository.findByEntityTypeAndEntityId("deal", 1L);
    Assertions.assertThat(deal.isPresent()).isFalse();
  }

  @Test
  @Sql("/test-scripts/retry-entity-error-message-for-delete.sql")
  public void givenEntityDeletedCommand_shouldDeleteAndRemoveAllRetryEntriesFromTable() throws InterruptedException, IOException {
    //given
    CountDownLatch latch = new CountDownLatch(1);
    long dealId = 10L;
    long tenantId = 11L;
    final HashMap<String, Object> dealDeleteEvent = new HashMap<String, Object>() {{
      put("id", dealId);
      put("version", 1);
      put("tenantId", tenantId);
    }};
    wireMockNewEsHost.stubFor(
        WireMock.delete(urlEqualTo("/11-deal/deal/10?timeout=1m"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString("classpath:contracts/es/response/delete-index-response-sample.json"))));
    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DEAL_DELETED_EVENT, dealDeleteEvent);
    latch.await(3, TimeUnit.SECONDS);

    //then
    wireMockNewEsHost.verify(deleteRequestedFor(
        urlEqualTo("/11-deal/deal/10?timeout=1m")
    ));
    List<RetryEntity> all = retryEntityRepository.findAll((root, criteriaQuery, criteriaBuilder) -> {
      Predicate entityTypePredicate = criteriaBuilder.equal(root.get("entityType"), "deal");
      Predicate entityIdPredicate = criteriaBuilder.equal(root.get("entityId"), 10L);
      return criteriaBuilder.and(entityTypePredicate, entityIdPredicate);
    });
    Assertions.assertThat(all.size()).isEqualTo(0);
  }

  @Test
  public void givenEntityCreatedEvent_shouldRetryAndSaveRetriableEntityAfterLastAttempt() throws InterruptedException, IOException {
    // given
    CountDownLatch latch = new CountDownLatch(1);
    final HashMap<String, Object> leadCreatedEvent = new HashMap<String, Object>() {{
      put("id", 1);
      put("version", 1);
      put("tenantId", 10);
      put("department", "Product");
    }};
    wireMockNewEsHost.stubFor(WireMock.put(urlEqualTo("/10-lead/lead/1?version_type=external&version=1&timeout=1m"))
        .willReturn(
            aResponse()
                .withStatus(400)
                .withBody("{\n"
                    + "    \"error\": {\n"
                    + "        \"type\": \"mapper_parsing_exception\",\n"
                    + "        \"reason\": \"failed to parse field [updatedAt] of type [date]\",\n"
                    + "    },\n"
                    + "    \"status\": 400\n"
                    + "}")
        ));

    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LEAD_CREATED_EVENT, leadCreatedEvent);
    latch.await(5, TimeUnit.SECONDS);

    // then
    wireMockNewEsHost
        .verify(5,
            putRequestedFor(
                urlEqualTo("/10-lead/lead/1?version_type=external&version=1&timeout=1m")
            )
                .withRequestBody(
                    equalToJson(getResourceAsString("classpath:contracts/entity-created-index-request-without-customField.json"), true, false))
        );
    Optional<RetryEntity> deal = retryEntityRepository.findByEntityTypeAndEntityId("lead", 1L);
    Assertions.assertThat(deal.isPresent()).isTrue();
    Assertions.assertThat(deal.get().getRetryCount()).isEqualTo(0L);
    Assertions.assertThat(deal.get().getTenantId()).isEqualTo(10L);
    Assertions.assertThat(deal.get().getEntityPayload()).containsExactlyInAnyOrderEntriesOf(leadCreatedEvent);
    Assertions.assertThat(deal.get().getErrorMessage()).isEqualTo("Error while indexing : ElasticsearchStatusException[Unable to parse response body]; nested: ResponseException[method [PUT], host [http://localhost:7071], URI [/10-lead/lead/1?version_type=external&version=1&timeout=1m], status line [HTTP/1.1 400 Bad Request]\n"
        + "{\n"
        + "    \"error\": {\n"
        + "        \"type\": \"mapper_parsing_exception\",\n"
        + "        \"reason\": \"failed to parse field [updatedAt] of type [date]\",\n"
        + "    },\n"
        + "    \"status\": 400\n"
        + "}]; nested: ResponseException[method [PUT], host [http://localhost:7071], URI [/10-lead/lead/1?version_type=external&version=1&timeout=1m], status line [HTTP/1.1 400 Bad Request]\n"
        + "{\n"
        + "    \"error\": {\n"
        + "        \"type\": \"mapper_parsing_exception\",\n"
        + "        \"reason\": \"failed to parse field [updatedAt] of type [date]\",\n"
        + "    },\n"
        + "    \"status\": 400\n"
        + "}];");
  }

  @Test
  public void givenEntityUpdatedEvent_shouldRetryAndSaveRetriableEntityAfterLastAttempt() throws InterruptedException, IOException {
    // given
    CountDownLatch latch = new CountDownLatch(1);
    final HashMap<String, Object> dealUpdatedEvent = new HashMap<String, Object>() {{
      put("id", 111);
      put("version", 1);
      put("tenantId", 10);
      put("name", "test");
    }};
    wireMockNewEsHost.stubFor(WireMock.put(urlEqualTo("/10-deal/deal/111?version_type=external&version=1&timeout=1m"))
        .willReturn(
            aResponse()
                .withStatus(400)));

    wireMockNewEsHost.stubFor(WireMock.head(urlEqualTo("/10-deal/deal/111"))
        .willReturn(
            aResponse()
                .withStatus(200)));

    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DEAL_UPDATED_EVENT, dealUpdatedEvent);
    latch.await(5, TimeUnit.SECONDS);

    // then
    wireMockNewEsHost
        .verify(5,
            putRequestedFor(
                urlEqualTo("/10-deal/deal/111?version_type=external&version=1&timeout=1m")
            )
        );
    Optional<RetryEntity> deal = retryEntityRepository.findByEntityTypeAndEntityId("deal", 111L);
    Assertions.assertThat(deal.isPresent()).isTrue();
    Assertions.assertThat(deal.get().getRetryCount()).isEqualTo(0L);
    Assertions.assertThat(deal.get().getTenantId()).isEqualTo(10L);
    Assertions.assertThat(deal.get().getEntityPayload()).containsExactlyInAnyOrderEntriesOf(dealUpdatedEvent);
  }

  @Test
  public void givenEntityDeletedEvent_shouldRetryAndSaveRetriableEntityAfterLastAttempt() throws InterruptedException, IOException {
    // given
    CountDownLatch latch = new CountDownLatch(1);
    final HashMap<String, Object> contactDeletedEvent = new HashMap<String, Object>() {{
      put("id", 1);
      put("version", 1);
      put("tenantId", 10);
    }};
    wireMockNewEsHost.stubFor(
        WireMock.delete(urlEqualTo("/10-contact/contact/1?timeout=1m"))
            .willReturn(
                aResponse()
                    .withStatus(400)));
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, CONTACT_DELETED_EVENT, contactDeletedEvent);
    latch.await(5, TimeUnit.SECONDS);

    //then
    wireMockNewEsHost.verify(deleteRequestedFor(
        urlEqualTo("/10-contact/contact/1?timeout=1m")
    ));
    Optional<RetryEntity> deal = retryEntityRepository.findByEntityTypeAndEntityId("contact", 1L);
    Assertions.assertThat(deal.isPresent()).isTrue();
    Assertions.assertThat(deal.get().getRetryCount()).isEqualTo(0L);
    Assertions.assertThat(deal.get().getTenantId()).isEqualTo(10L);
    Assertions.assertThat(deal.get().getEntityPayload()).containsExactlyInAnyOrderEntriesOf(contactDeletedEvent);
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    Resource resource = resourceLoader.getResource(resourcePath);
    File file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  @TestConfiguration
  public static class TestEnvironmentSetup
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      rabbitMQContainer
          .withExchange(SALES_EXCHANGE, "topic")
          .withExchange(DEAL_EXCHANGE, "topic")
          .withExchange(COMPANY_EXCHANGE, "topic")
          .withQueue(SEARCH_QUEUE)
          .start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.port=" + rabbitMQContainer.getAmqpPort());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.host=" + rabbitMQContainer.getContainerIpAddress());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.username=" + rabbitMQContainer.getAdminUsername());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.password=" + rabbitMQContainer.getAdminPassword());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.virtualHost=" + "/");
    }
  }

  @TestConfiguration
  @EnableJpaRepositories
  public static class TestDatabaseInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    PostgreSQLContainer postgreSQLContainer = new PostgreSQLContainer("postgres:10.11")
        .withDatabaseName("search")
        .withUsername("test-user")
        .withPassword("test-password");

    @Bean
    public PostgreSQLContainer postgreSQLContainer() {
      postgreSQLContainer.start();
      return postgreSQLContainer;
    }

    @Bean
    @Primary
    public DataSource dataSource(PostgreSQLContainer container) {
      postgreSQLContainer.start();

      return DataSourceBuilder.create()
          .url(container.getJdbcUrl())
          .username(container.getUsername())
          .password(container.getPassword())
          .driverClassName(container.getDriverClassName())
          .build();
    }

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      postgreSQLContainer.start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.url=" + postgreSQLContainer.getJdbcUrl());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.username=" + postgreSQLContainer.getUsername());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.password=" + postgreSQLContainer.getPassword());
    }
  }
}
