package com.sell.search;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.sell.search.core.domain.Action;
import com.sell.search.core.domain.PermissionDTO;
import com.sell.search.util.FileUtil;
import com.sell.search.util.TestEntity;
import io.zonky.test.db.AutoConfigureEmbeddedDatabase;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.After;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

@SpringBootTest
@RunWith(SpringRunner.class)
@AutoConfigureEmbeddedDatabase
@AutoConfigureMockMvc
@TestPropertySource(
    properties = {
        "elasticsearch.port=9090",
        "newElasticsearch.port=7071",
        "client.entity.basePath=http://localhost:9090",
        "client.company.basePath=http://localhost:9090"
    })
public class CompanyControllerIntegrationTest {

  @Autowired
  private MockMvc mvc;

  @Rule
  public WireMockRule wireMockRule = new WireMockRule(9090);

  @Rule
  public WireMockRule newEsWireMockRule = new WireMockRule(7071);

  @After
  public void tearDown() {
    wireMockRule.stop();
    newEsWireMockRule.stop();
    SecurityContextHolder.getContext().setAuthentication(null);
  }

  @Test
  @Sql("/test-scripts/create-and-map-custom-field-and-es-field-company.sql")
  public void givenCompanySearchRequest_shouldReturnResponse() throws Exception {

    // given
    SecurityContextHolder.getContext().setAuthentication(null);
    Action companyAction = new Action();
    companyAction.read(true);
    PermissionDTO companyPermission = new PermissionDTO();
    companyPermission.setAction(companyAction);
    companyPermission.setName("company");
    Set<PermissionDTO> allowedPermissions =
        Stream.of(companyPermission).collect(Collectors.toSet());

    String companyESSearchResponse =
        FileUtil.getStringFrom("queries/es/response/company-es-name-reaponse.json");
    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/6-company/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(companyESSearchResponse)));
    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/companies/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));
    // when
    MvcResult mvcResult =
        mvc.perform(
                get("/v1/search/companies/lookup?q=companyName")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 6L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then
    JSONAssert.assertEquals(
        FileUtil.getStringFrom("contracts/company-name-response.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.LENIENT);
  }
}
