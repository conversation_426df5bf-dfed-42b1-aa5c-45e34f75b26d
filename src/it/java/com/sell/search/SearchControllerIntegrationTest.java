package com.sell.search;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.getRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.github.tomakehurst.wiremock.stubbing.ServeEvent;
import com.sell.search.core.domain.Action;
import com.sell.search.core.domain.PermissionDTO;
import com.sell.search.util.FileUtil;
import com.sell.search.util.TestEntity;
import io.zonky.test.db.AutoConfigureEmbeddedDatabase;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.After;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

@SpringBootTest
@RunWith(SpringRunner.class)
@AutoConfigureEmbeddedDatabase
@AutoConfigureMockMvc
@TestPropertySource(
    properties = {
      "elasticsearch.port=9090",
      "newElasticsearch.port=7071",
      "client.entity.basePath=http://localhost:9090",
        "client.company.basePath=http://localhost:9090",
        "client.deal.basePath=http://localhost:9090",
        "client.iam.basePath= http://localhost:9090"
    })
public class SearchControllerIntegrationTest {

  @Autowired private MockMvc mvc;

  @Rule public WireMockRule wireMockRule = new WireMockRule(9090);

  @Rule public WireMockRule newEsWireMockRule = new WireMockRule(7071);

  @After
  public void tearDown() {
    wireMockRule.stop();
    newEsWireMockRule.stop();
    SecurityContextHolder.getContext().setAuthentication(null);
  }

  @Test
  @Sql("/test-scripts/map-custom-field-with-es-field-name-lead-tenantId-6.sql")
  public void givenLookupForLead_shouldReturnResponse() throws Exception {

    // given
    Action leadAction = new Action();
    leadAction.readAll(true);
    PermissionDTO leadPermission = new PermissionDTO();
    leadPermission.setName("lead");
    leadPermission.setAction(leadAction);
    Set<PermissionDTO> allowedPermissions = Stream.of(leadPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/lead/fields?custom-only=true&tenantId=6"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/get-custom-field-response.json"))));

    String leadLookupResponse =
        FileUtil.getStringFrom("queries/es/response/lead-valid-response.json");
    newEsWireMockRule.stubFor(
        WireMock.post(urlMatching("/6-lead/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(leadLookupResponse)));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{}")));

    // when
    MvcResult mvcResult =
        mvc.perform(
                get("/v1/search/lead/lookup?converted=false&q=name:tony")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 6L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then
    JSONAssert.assertEquals(
        FileUtil.getStringFrom("contracts/lead-lookup-response.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.LENIENT);
  }

  @Test
  @Sql("/test-scripts/create-and-map-custom-field-and-es-field-lead.sql")
  public void givenLeadSearchRequest_shouldReturnResponse() throws Exception {

    // given
    Action leadAction = new Action();
    leadAction.readAll(true);
    PermissionDTO leadPermission = new PermissionDTO();
    leadPermission.setAction(leadAction);
    leadPermission.setName("lead");
    Set<PermissionDTO> allowedPermissions = Stream.of(leadPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/lead/fields?custom-only=true&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        FileUtil.getStringFrom("contracts/get-custom-field-response.json"))));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/get-custom-field-response.json"))));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{}")));

    String leadLookupResponse =
        FileUtil.getStringFrom("queries/es/response/lead-search-response.json");
    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/1-lead/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(leadLookupResponse)));

    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/1-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/id-name-search-es-response-for-contact.json"))));

    wireMockRule.stubFor(
        WireMock.post(urlEqualTo("/v1/users/search-for-id"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[1,2]")));

    // when
    String searchFilter = FileUtil.getStringFrom("queries/es/request/lead-filter.json");
    MvcResult mvcResult =
        mvc.perform(
                post("/v1/search/lead?sort=myAddress,desc&page=0&size=100")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(searchFilter)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 1L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then

    wireMockRule.verify(1, getRequestedFor(urlEqualTo("/v1/internal/share/access/LEAD/READ")));

    List<ServeEvent> allServeEvents = newEsWireMockRule.getAllServeEvents();
    ServeEvent idNameSearchEvent = allServeEvents.stream().filter(serveEvent -> serveEvent.getRequest().getUrl().startsWith("/1-id-name/_search"))
        .findFirst().get();
    ServeEvent contactSearchEvent = allServeEvents.stream().filter(serveEvent -> serveEvent.getRequest().getUrl().startsWith("/1-lead/_search"))
        .findFirst().get();

    JSONAssert.assertEquals(FileUtil.getStringFrom("contracts/lead-search-with-all-field-es-query-request.json"),
        contactSearchEvent.getRequest().getBodyAsString(), JSONCompareMode.NON_EXTENSIBLE);

    JSONAssert.assertEquals(FileUtil.getStringFrom("contracts/idName-query-for-lead-search-with-all-field-es-query-request.json"),
        idNameSearchEvent.getRequest().getBodyAsString(), JSONCompareMode.STRICT);

    wireMockRule.verify(
        1, getRequestedFor(urlEqualTo("/v1/entities/lead/fields?custom-only=true&tenantId=1")));
    JSONAssert.assertEquals(
        FileUtil.getStringFrom("contracts/lead-search-response.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.STRICT);
  }

  @Test
  @Sql("/test-scripts/create-and-map-custom-field-and-es-field-lead.sql")
  public void givenLeadSearchRequestWithIntegrationView_shouldReturnResponse() throws Exception {

    // given
    Action leadAction = new Action();
    leadAction.readAll(true);
    PermissionDTO leadPermission = new PermissionDTO();
    leadPermission.setAction(leadAction);
    leadPermission.setName("lead");
    Set<PermissionDTO> allowedPermissions = Stream.of(leadPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/lead/fields?custom-only=true&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        FileUtil.getStringFrom("contracts/get-lead-custom-field-response.json"))));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/get-lead-custom-field-response.json"))));

    String leadLookupResponse =
        FileUtil.getStringFrom("queries/es/response/lead-search-response.json");
    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/1-lead/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(leadLookupResponse)));

    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/1-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/id-name-search-es-response-for-contact.json"))));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{}")));


    // when
    MvcResult mvcResult =
        mvc.perform(
                get("/v2/search/lead?view=integration&text=&sort=updatedAt,desc&page=0&size=100")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 1L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then

    wireMockRule.verify(1, getRequestedFor(urlEqualTo("/v1/internal/share/access/LEAD/READ")));

    List<ServeEvent> allServeEvents = newEsWireMockRule.getAllServeEvents();
    ServeEvent idNameSearchEvent = allServeEvents.stream().filter(serveEvent -> serveEvent.getRequest().getUrl().startsWith("/1-id-name/_search"))
        .findFirst().get();
    ServeEvent contactSearchEvent = allServeEvents.stream().filter(serveEvent -> serveEvent.getRequest().getUrl().startsWith("/1-lead/_search"))
        .findFirst().get();

    JSONAssert.assertEquals(FileUtil.getStringFrom("contracts/lead-search-ea-query-for-integration-view.json"),
        contactSearchEvent.getRequest().getBodyAsString(), JSONCompareMode.STRICT);

    JSONAssert.assertEquals(FileUtil.getStringFrom("contracts/idName-query-for-lead-search-with-integration-viewall-field-es-query-request.json"),
        idNameSearchEvent.getRequest().getBodyAsString(), JSONCompareMode.STRICT);

    JSONAssert.assertEquals(
        FileUtil.getStringFrom("queries/es/response/lead-search-response-for-integration-view.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.STRICT);
  }

  @Test
  @Sql("/test-scripts/create-and-map-custom-field-and-es-field-deal.sql")
  public void givenDealSearchRequestV2WithIntegrationView_shouldReturnResponse() throws Exception {

    // given
    Action dealAction = new Action();
    dealAction.readAll(true);
    PermissionDTO dealPermission = new PermissionDTO();
    dealPermission.setAction(dealAction);
    dealPermission.setName("deal");
    Set<PermissionDTO> allowedPermissions = Stream.of(dealPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/deals/share-rule/access/read"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    String dealEsSearchResponse =
        FileUtil.getStringFrom("queries/es/response/deal-search-sample-response.json");
    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/6-deal/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)

                    .withBody(dealEsSearchResponse)));

    // when
    MvcResult mvcResult =
        mvc.perform(
                get("/v2/search/deal?view=integration&text=&sort=name,desc&page=0&size=100")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 6L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then

    wireMockRule.verify(1, getRequestedFor(urlEqualTo("/v1/deals/share-rule/access/read")));

    List<ServeEvent> allServeEvents = newEsWireMockRule.getAllServeEvents();

    ServeEvent dealSearchEvent = allServeEvents.stream().filter(serveEvent -> serveEvent.getRequest().getUrl().startsWith("/6-deal/_search"))
        .findFirst().get();

    JSONAssert.assertEquals(FileUtil.getStringFrom("contracts/deal-search-ea-query-for-integration-view.json"),
        dealSearchEvent.getRequest().getBodyAsString(), JSONCompareMode.STRICT);

    JSONAssert.assertEquals(
        FileUtil.getStringFrom("queries/es/response/deal-search-v2-response-for-integration-view.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.STRICT);
  }

  @Test
  @Sql("/test-scripts/create-and-map-custom-field-and-es-field-deal.sql")
  public void givenDealSearchRequest_shouldReturnResponse() throws Exception {

    // given
    SecurityContextHolder.getContext().setAuthentication(null);
    Action dealAction = new Action();
    dealAction.readAll(true);
    PermissionDTO dealPermission = new PermissionDTO();
    dealPermission.setAction(dealAction);
    dealPermission.setName("deal");
    Set<PermissionDTO> allowedPermissions = Stream.of(dealPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/deals/share-rule/access/read"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/deal/fields?custom-only=true"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));
    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/DEAL/fields?custom-only=false"))
            .willReturn(
                aResponse()
                    .withStatus(400)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    String dealEsSearchResponse =
        FileUtil.getStringFrom("queries/es/response/deal-es-search-raw-sample-response.json");
    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/6-deal/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(dealEsSearchResponse)));

    // when
    String searchFilter = FileUtil.getStringFrom("queries/es/request/by-multipicklist-field-filter.json");
    MvcResult mvcResult =
        mvc.perform(
                post("/v1/search/deal?sort=updatedAt,desc&page=0&size=100")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(searchFilter)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 6L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then
    wireMockRule.verify(1, getRequestedFor(urlEqualTo("/v1/deals/share-rule/access/read")));
    wireMockRule.verify(
        0, getRequestedFor(urlEqualTo("/v1/entities/deal/fields?custom-only=true")));

    String dealSearchWithCustomSort = FileUtil.getStringFrom("contracts/es-search-query-for-deal-with-sort-on-customField.json");

    newEsWireMockRule.verify(
        1, postRequestedFor(urlEqualTo("/6-deal/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .withRequestBody(equalToJson(dealSearchWithCustomSort))
    );

    JSONAssert.assertEquals(
        FileUtil.getStringFrom("contracts/deal-multi-picklist-search-response.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.LENIENT);
  }


  @Test
  @Sql("/test-scripts/create-and-map-custom-field-and-es-field-deal.sql")
  public void givenDealWithKanbanViewSearchRequest_shouldReturnResponse() throws Exception {

    // given
    SecurityContextHolder.getContext().setAuthentication(null);
    Action dealAction = new Action();
    dealAction.readAll(true);
    PermissionDTO dealPermission = new PermissionDTO();
    dealPermission.setAction(dealAction);
    dealPermission.setName("deal");
    Set<PermissionDTO> allowedPermissions = Stream.of(dealPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/deals/share-rule/access/read"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/deal/fields?custom-only=true"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));
    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/DEAL/fields?custom-only=false"))
            .willReturn(
                aResponse()
                    .withStatus(400)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    String dealEsSearchResponse =
        FileUtil.getStringFrom("queries/es/response/deal-es-kanban-search-raw-sample-response.json");
    newEsWireMockRule.stubFor(
        WireMock.post(
            urlEqualTo(
                "/6-deal/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(dealEsSearchResponse)));

    // when
    String searchFilter = FileUtil.getStringFrom("queries/es/request/deal-kanban-view-filter.json");
    MvcResult mvcResult =
        mvc.perform(
            post("/v1/search/deal?view=kanban&pipelineId=4568545&sort=updatedAt,desc&page=0&size=100")
                .contentType(MediaType.APPLICATION_JSON)
                .content(searchFilter)
                .header(
                    "Authorization", "Bearer " + TestEntity.getJwt(6L, 6L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then
    wireMockRule.verify(
        0, getRequestedFor(urlEqualTo("/v1/entities/deal/fields?custom-only=true")));

    String dealSearchWithCustomSort = FileUtil.getStringFrom("contracts/es-search-kanban-query-for-deal-with-sort-on-customField.json");

    newEsWireMockRule.verify(
        1, postRequestedFor(urlEqualTo("/6-deal/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .withRequestBody(equalToJson(dealSearchWithCustomSort))
    );

    JSONAssert.assertEquals(
        FileUtil.getStringFrom("contracts/deal-kanban-view-response.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.LENIENT);

    wireMockRule.verify(1, getRequestedFor(urlEqualTo("/v1/deals/share-rule/access/read")));
  }

  @Test
  @Sql("/test-scripts/create-and-map-custom-field-and-es-field-company.sql")
  public void givenCompanySearchRequest_shouldReturnResponse() throws Exception {

    // given
    SecurityContextHolder.getContext().setAuthentication(null);
    Action companyAction = new Action();
    companyAction.readAll(true);
    PermissionDTO companyPermission = new PermissionDTO();
    companyPermission.setAction(companyAction);
    companyPermission.setName("company");
    Set<PermissionDTO> allowedPermissions =
        Stream.of(companyPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
            WireMock.get(urlEqualTo("/v1/companies/share-rule/access/read"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/company/fields?custom-only=true"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));
    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/COMPANY/fields?custom-only=false"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));
    
    String companyESSearchResponse =
        FileUtil.getStringFrom("queries/es/response/company-with-multipicklist-es-search-sample-response.json");
    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/6-company/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(companyESSearchResponse)));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/companies/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));


    // when
    String searchFilter = FileUtil.getStringFrom("queries/es/request/byName-filter.json");
    MvcResult mvcResult =
        mvc.perform(
                post("/v1/search/company?sort=updatedAt,desc&page=0&size=100")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(searchFilter)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 6L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then
    wireMockRule.verify(1, getRequestedFor(urlEqualTo("/v1/companies/share-rule/access/read")));
    wireMockRule.verify(
        0, getRequestedFor(urlEqualTo("/v1/entities/company/fields?custom-only=true")));

    newEsWireMockRule.verify(
        1, postRequestedFor(urlEqualTo("/6-company/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .withRequestBody(equalToJson(FileUtil.getStringFrom("contracts/es-search-query-for-company.json")))
    );

    JSONAssert.assertEquals(
        FileUtil.getStringFrom("contracts/company-multipicklist-search-response.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.LENIENT);
  }

  @Test
  @Sql("/test-scripts/create-and-map-custom-field-and-es-field.sql")
  public void givenContactSearchRequest_shouldReturnResponse() throws Exception {

    // given
    Action contactAction = new Action();
    contactAction.readAll(true);
    PermissionDTO contactPermission = new PermissionDTO();
    contactPermission.setAction(contactAction);
    contactPermission.setName("contact");
    Set<PermissionDTO> allowedPermissions = Stream.of(contactPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/contact/fields?custom-only=true&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        FileUtil.getStringFrom("contracts/get-custom-field-response.json"))));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/get-custom-field-response.json"))));

    String esSearchResponse =
        FileUtil.getStringFrom("queries/es/response/contact-search-response.json");
    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo("/1-contact/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(esSearchResponse)));

    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo("/1-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/id-name-search-es-response-for-contact.json"))));
    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{}")));

    // when
    String searchFilter = FileUtil.getStringFrom("queries/es/request/contact-filter.json");
    MvcResult mvcResult =
        mvc.perform(
                post("/v1/search/contact?sort=myAddress,desc&page=0&size=100")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(searchFilter)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 1L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then

    wireMockRule.verify(1, getRequestedFor(urlEqualTo("/v1/internal/share/access/CONTACT/READ")));

    List<ServeEvent> allServeEvents = newEsWireMockRule.getAllServeEvents();
    ServeEvent idNameSearchEvent = allServeEvents.stream().filter(serveEvent -> serveEvent.getRequest().getUrl().startsWith("/1-id-name/_search"))
        .findFirst().get();
    ServeEvent contactSearchEvent = allServeEvents.stream().filter(serveEvent -> serveEvent.getRequest().getUrl().startsWith("/1-contact/_search"))
        .findFirst().get();

    JSONAssert.assertEquals(FileUtil.getStringFrom("contracts/contact-search-with-all-field-es-query-request.json"),
        contactSearchEvent.getRequest().getBodyAsString(), new CustomComparator(JSONCompareMode.STRICT,new Customization("query.bool.must[*].bool.must[*].range.updatedAt.to",(o1,o2)->true),
        new Customization("query.bool.must[*].bool.must[*].range.updatedAt.from",(o1,o2)->true)));

    JSONAssert.assertEquals(FileUtil.getStringFrom("contracts/idName-query-for-contact-search-with-all-field-es-query-request.json"),
        idNameSearchEvent.getRequest().getBodyAsString(), JSONCompareMode.STRICT);

    List<ServeEvent> allServeEvents1 = wireMockRule.getAllServeEvents();
    wireMockRule.verify(
        1, getRequestedFor(urlEqualTo("/v1/entities/contact/fields?custom-only=true&tenantId=1")));
    JSONAssert.assertEquals(
        FileUtil.getStringFrom("contracts/contact-search-response.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.STRICT);
  }

  @Test
  @Sql("/test-scripts/create-and-map-custom-field-and-es-field.sql")
  public void givenContactSearchRequestWithCompanyId_shouldReturnResponse() throws Exception {

    // given
    Action contactAction = new Action();
    contactAction.readAll(true);
    PermissionDTO contactPermission = new PermissionDTO();
    contactPermission.setAction(contactAction);
    contactPermission.setName("contact");
    Set<PermissionDTO> allowedPermissions = Stream.of(contactPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));


    String esSearchResponse =
        FileUtil.getStringFrom("queries/es/response/contact-response-by-id.json");
    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo("/1-contact/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(esSearchResponse)));

    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo("/1-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/id-name-search-es-response-for-contact.json"))));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{}")));


    // when
    String searchFilter = FileUtil.getStringFrom("queries/es/request/contact-filter.json");
    MvcResult mvcResult =
        mvc.perform(
                get("/v1/search/contacts/lookup?companyId=1&name=contactName")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 1L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then

    JSONAssert.assertEquals(
        FileUtil.getStringFrom("contracts/contact-response-by-id.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.STRICT);

    wireMockRule.verify(2, getRequestedFor(urlEqualTo("/v1/internal/share/access/CONTACT/READ")));

    List<ServeEvent> allServeEvents = newEsWireMockRule.getAllServeEvents();
    List<ServeEvent> contactSearchEvents = allServeEvents.stream().filter(serveEvent -> serveEvent.getRequest().getUrl().startsWith("/1-contact/_search")).collect(
        Collectors.toList());
    JSONAssert.assertEquals(FileUtil.getStringFrom("contracts/contact-search-by-company-id.json"),
        contactSearchEvents.get(0).getRequest().getBodyAsString(), JSONCompareMode.STRICT);

    wireMockRule.verify(
        0, getRequestedFor(urlEqualTo("/v1/entities/contact/fields?custom-only=true&tenantId=1")));
  }

  @Test
  @Sql("/test-scripts/create-and-map-custom-field-and-es-field.sql")
  public void givenContactSearchRequestByMail_shouldReturnResponse() throws Exception {

    // given
    Action contactAction = new Action();
    contactAction.readAll(true);
    PermissionDTO contactPermission = new PermissionDTO();
    contactPermission.setAction(contactAction);
    contactPermission.setName("contact");
    Set<PermissionDTO> allowedPermissions = Stream.of(contactPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/contact/fields?custom-only=true&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        FileUtil.getStringFrom("contracts/get-custom-field-response.json"))));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/get-custom-field-response.json"))));

    String esSearchResponse =
        FileUtil.getStringFrom("queries/es/response/contact-search-by-mail-response.json");
    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/1-contact/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512")
              )
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(esSearchResponse)));

    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/1-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/id-name-search-es-response-for-contact.json"))));
    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{}")));

    // when
    MvcResult mvcResult =
        mvc.perform(
                get("/v1/search/contacts/email?email=<EMAIL>,<EMAIL>")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 1L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then
    newEsWireMockRule.verify(1, postRequestedFor(urlMatching("/1-contact/_search?.*"))
        .withRequestBody(equalToJson(FileUtil.getStringFrom("contracts/contact-search-by-emails-request.json"), true, false)));
    JSONAssert.assertEquals(
        FileUtil.getStringFrom("contracts/contact-by-mail-response.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.STRICT);
  }

  @Test
  @Sql("/test-scripts/create-and-map-custom-field-and-es-field.sql")
  public void givenContactSearchRequestByPhoneNumber_shouldReturnResponse() throws Exception {

    // given
    Action contactAction = new Action();
    contactAction.readAll(true);
    PermissionDTO contactPermission = new PermissionDTO();
    contactPermission.setAction(contactAction);
    contactPermission.setName("contact");
    Set<PermissionDTO> allowedPermissions = Stream.of(contactPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/contact/fields?custom-only=true&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        FileUtil.getStringFrom("contracts/get-custom-field-response.json"))));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/get-custom-field-response.json"))));

    String esSearchResponse =
        FileUtil.getStringFrom("queries/es/response/contact-search-by-phone-number.json");
    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/1-contact/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512")
            )
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(esSearchResponse)));

    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/1-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/id-name-search-es-response-for-contact.json"))));
    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{}")));

    // when
    MvcResult mvcResult =
        mvc.perform(
                get("/v1/search/contact/phoneNumber?phoneNumbers=9999999999,8888888888")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 1L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then
    newEsWireMockRule.verify(1, postRequestedFor(urlMatching("/1-contact/_search?.*"))
        .withRequestBody(equalToJson(FileUtil.getStringFrom("contracts/contact-search-by-phoneNumbers-request.json"), true, false)));
    JSONAssert.assertEquals(
        FileUtil.getStringFrom("contracts/contact-by-phone-number-response.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.STRICT);
  }



  @Test
  @Sql("/test-scripts/create-and-map-custom-field-and-es-field-deal.sql")
  public void givenDealSearchRequestWithIntegrationView_shouldReturnResponse() throws Exception {

    // given
    SecurityContextHolder.getContext().setAuthentication(null);
    Action dealAction = new Action();
    dealAction.readAll(true);
    PermissionDTO dealPermission = new PermissionDTO();
    dealPermission.setAction(dealAction);
    dealPermission.setName("deal");
    Set<PermissionDTO> allowedPermissions = Stream.of(dealPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/deals/share-rule/access/read"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/deal/fields?custom-only=true"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));
    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/DEAL/fields?custom-only=false"))
            .willReturn(
                aResponse()
                    .withStatus(400)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    String dealEsSearchResponse =
        FileUtil.getStringFrom("queries/es/response/deal-es-search-raw-sample-with-all-fields-response.json");
    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/6-deal/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(dealEsSearchResponse)));

    // when
    MvcResult mvcResult =
        mvc.perform(
                get("/v1/search/deal?view=integration&text=&sort=updatedAt,desc&page=0&size=100")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 6L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then
    String dealIntegrationViewResponse = FileUtil.getStringFrom("contracts/deal-search-response-for-integration-view.json");
    JSONAssert.assertEquals(dealIntegrationViewResponse,mvcResult.getResponse().getContentAsString(),JSONCompareMode.STRICT);
    wireMockRule.verify(1, getRequestedFor(urlEqualTo("/v1/deals/share-rule/access/read")));
    wireMockRule.verify(
        0, getRequestedFor(urlEqualTo("/v1/entities/deal/fields?custom-only=true")));

    String dealSearchWithCustomSort = FileUtil.getStringFrom("contracts/es-search-query-for-deal-with-integration-view.json");

    newEsWireMockRule.verify(
        1, postRequestedFor(urlEqualTo("/6-deal/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .withRequestBody(equalToJson(dealSearchWithCustomSort))
    );
  }

  @Test
  @Sql("/test-scripts/create-and-map-custom-field-and-es-field-lead.sql")
  public void givenLeadSearchRequestFromWorkflow_shouldNotEvaluateShareRuleAndReturnResponseWithEmptyRecordActionsMap() throws Exception {
    // given
    Action leadAction = new Action();
    leadAction.readAll(true);
    PermissionDTO leadPermission = new PermissionDTO();
    leadPermission.setAction(leadAction);
    leadPermission.setName("lead");
    Set<PermissionDTO> allowedPermissions = Stream.of(leadPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/lead/fields?custom-only=true&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        FileUtil.getStringFrom("contracts/get-custom-field-response.json"))));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/get-custom-field-response.json"))));

    String leadLookupResponse =
        FileUtil.getStringFrom("queries/es/response/lead-search-response.json");
    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/1-lead/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(leadLookupResponse)));

    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/1-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/id-name-search-es-response-for-contact.json"))));
    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{}")));

    wireMockRule.stubFor(
        WireMock.post(urlEqualTo("/v1/users/search-for-id"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[1,2]")));


    // when
    String searchFilter = FileUtil.getStringFrom("queries/es/request/lead-filter.json");
    MvcResult mvcResult =
        mvc.perform(
                post("/v2/search/lead?sort=myAddress,desc&page=0&size=100")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(searchFilter)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 1L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();

    // then
    wireMockRule.verify(0, getRequestedFor(urlEqualTo("/v1/internal/share/access/LEAD/READ")));

    List<ServeEvent> allServeEvents = newEsWireMockRule.getAllServeEvents();
    ServeEvent idNameSearchEvent = allServeEvents.stream().filter(serveEvent -> serveEvent.getRequest().getUrl().startsWith("/1-id-name/_search"))
        .findFirst().get();
    ServeEvent contactSearchEvent = allServeEvents.stream().filter(serveEvent -> serveEvent.getRequest().getUrl().startsWith("/1-lead/_search"))
        .findFirst().get();

    JSONAssert.assertEquals(FileUtil.getStringFrom("contracts/lead-search-with-all-field-es-query-request.json"),
        contactSearchEvent.getRequest().getBodyAsString(), JSONCompareMode.NON_EXTENSIBLE);

    JSONAssert.assertEquals(FileUtil.getStringFrom("contracts/idName-query-for-lead-search-with-all-field-es-query-request.json"),
        idNameSearchEvent.getRequest().getBodyAsString(), JSONCompareMode.STRICT);

    wireMockRule.verify(
        1, getRequestedFor(urlEqualTo("/v1/entities/lead/fields?custom-only=true&tenantId=1")));
    JSONAssert.assertEquals(
        FileUtil.getStringFrom("contracts/lead-search-response-1.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.STRICT);
  }

  @Test
  @Sql("/test-scripts/create-map-custom-field-for-company-contact-es-field.sql")
  public void givenCompanySearchRequest_withContactId_shouldReturnResponse() throws Exception {

    // given
    SecurityContextHolder.getContext().setAuthentication(null);
    Action companyAction = new Action();
    companyAction.read(true);
    PermissionDTO companyPermission = new PermissionDTO();
    companyPermission.setAction(companyAction);
    companyPermission.setName("company");
    PermissionDTO contactPermission = new PermissionDTO();
    contactPermission.setAction(companyAction);
    contactPermission.setName("contact");
    Set<PermissionDTO> allowedPermissions =
        Stream.of(companyPermission, contactPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/companies/share-rule/access/read"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/contact/fields?custom-only=true&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        FileUtil.getStringFrom("contracts/get-custom-field-response.json"))));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/get-custom-field-response.json"))));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/company/fields?custom-only=true"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));
    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/COMPANY/fields?custom-only=false"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    String esSearchResponse =
        FileUtil.getStringFrom("queries/es/response/contact-response-with-id.json");
    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/1-contact/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(esSearchResponse)));

    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/1-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/id-name-search-es-response-for-contact.json"))));

    String companyESSearchResponse =
        FileUtil.getStringFrom("queries/es/response/company-es-search-raw-sample-response.json");
    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/1-company/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(companyESSearchResponse)));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/companies/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));


    // when
    MvcResult mvcResult =
        mvc.perform(
                get("/v1/search/companies-lookup?name=companyName&contactId=1")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 1L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then
    wireMockRule.verify(
        0, getRequestedFor(urlEqualTo("/v1/entities/company/fields?custom-only=true")));
    JSONAssert.assertEquals(
        FileUtil.getStringFrom("contracts/company-response-associatedWith-contact.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.LENIENT);
  }
  @Test
  public void givenUserHavingMaskedPhoneNumber_tryToSearchByPhoneNumberOnLead_shouldGetMaskedValues() throws Exception {

    // given
    Action leadAction = new Action();
    leadAction.readAll(true);
    PermissionDTO leadPermission = new PermissionDTO();
    leadPermission.setName("lead");
    leadPermission.setAction(leadAction);
    Set<PermissionDTO> allowedPermissions = Stream.of(leadPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/lead/fields?custom-only=true&tenantId=6"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/get-custom-field-response.json"))));

    String leadLookupResponse =
        FileUtil.getStringFrom("queries/es/response/lead-valid-response.json");
    newEsWireMockRule.stubFor(
        WireMock.post(urlMatching("/6-lead/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(leadLookupResponse)));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/config/response/mask-field-response.json"))));

    // when
    MvcResult mvcResult =
        mvc.perform(
                get("/v1/search/lead/phoneNumber?phoneNumbers=9865321254&phoneNumbers=9865321211")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 6L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then
    JSONAssert.assertEquals(
        FileUtil.getStringFrom("contracts/search-response-by-phoneNumbers.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.LENIENT);
  }

  @Test
  @Sql("/test-scripts/create-and-map-custom-field-and-es-field-lead.sql")
  public void givenLeadWithKanbanViewSearchRequest_shouldReturnResponse() throws Exception {

    // given
    SecurityContextHolder.getContext().setAuthentication(null);
    Action dealAction = new Action();
    dealAction.readAll(true);
    PermissionDTO leadPermission = new PermissionDTO();
    leadPermission.setAction(dealAction);
    leadPermission.setName("lead");
    Set<PermissionDTO> allowedPermissions = Stream.of(leadPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlMatching("/v1/entities/[lL][eE][aA][dD]/fields\\?custom-only=true&tenantId=6"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        FileUtil.getStringFrom("contracts/get-lead-custom-field-response.json"))));

    wireMockRule.stubFor(
        WireMock.get(urlMatching("/v1/entities/[lL][eE][aA][dD]/fields\\?custom-only=false&tenantId=6"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        FileUtil.getStringFrom("contracts/get-lead-custom-field-response.json"))));
    String leadEsSearchResponse =
        FileUtil.getStringFrom("queries/es/response/lead-es-kanban-search-raw-sample-response.json");
    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/6-lead/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(leadEsSearchResponse)));

    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/6-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/id-name-search-es-response-for-lead.json"))));
    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/config/response/mask-field-response.json"))));

    // when
    String searchFilter = FileUtil.getStringFrom("queries/es/request/lead-kanban-view-filter.json");
    MvcResult mvcResult =
        mvc.perform(
                post("/v1/search/lead?view=kanban&pipelineId=4568545&sort=updatedAt,desc&page=0&size=100")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(searchFilter)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 6L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then

    String leadSearchWithCustomSort = FileUtil.getStringFrom("contracts/es-search-kanban-query-for-lead-with-sort-on-customField.json");

    newEsWireMockRule.verify(
        1, postRequestedFor(urlEqualTo("/6-lead/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .withRequestBody(equalToJson(leadSearchWithCustomSort))
    );

    JSONAssert.assertEquals(
        FileUtil.getStringFrom("contracts/lead-kanban-view-response.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.LENIENT);

    wireMockRule.verify(1, getRequestedFor(urlEqualTo("/v1/internal/share/access/LEAD/READ")));
  }

  @Test
  @Sql("/test-scripts/map-custom-field-with-es-field-name-lead-tenantId-6.sql")
  public void givenLookupForLeadWithSharedLeadWithMeeting_shouldReturnResponse() throws Exception {

    // given
    Action leadAction = new Action();
    leadAction.read(true);
    leadAction.meeting(true);
    PermissionDTO leadPermission = new PermissionDTO();
    leadPermission.setName("lead");
    leadPermission.setAction(leadAction);
    Set<PermissionDTO> allowedPermissions = Stream.of(leadPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("response/lead-share-rule-resolve-access-response.json"))));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/lead/fields?custom-only=true&tenantId=6"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/get-custom-field-response.json"))));

    String leadLookupResponse =
        FileUtil.getStringFrom("queries/es/response/lead-meeting-permission-response.json");
    newEsWireMockRule.stubFor(
        WireMock.post(urlMatching("/6-lead/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(leadLookupResponse)));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{}")));

    // when
    MvcResult mvcResult =
        mvc.perform(
                get("/v1/search/lead/lookup?view=meeting&converted=false&q=name:tony")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 6L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then
    JSONAssert.assertEquals(
        FileUtil.getStringFrom("contracts/lead-meeting-permission-resposne.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.LENIENT);
  }


  @Test
  @Sql("/test-scripts/create-map-field-for-company-associated-with-company-deal.sql")
  public void givenCompanySearchRequest_withAssociatedEntityContactAndDeal_shouldReturnResponse() throws Exception {

    // given
    SecurityContextHolder.getContext().setAuthentication(null);
    Action companyAction = new Action();
    companyAction.read(true);
    PermissionDTO companyPermission = new PermissionDTO();
    companyPermission.setAction(companyAction);
    companyPermission.setName("company");
    PermissionDTO contactPermission = new PermissionDTO();
    contactPermission.setAction(companyAction);
    contactPermission.setName("contact");
    PermissionDTO dealPermission = new PermissionDTO();
    dealPermission.setAction(companyAction);
    dealPermission.setName("deal");
    Set<PermissionDTO> allowedPermissions =
        Stream.of(companyPermission, contactPermission, dealPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/companies/share-rule/access/read"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/deals/share-rule/access/read"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/contact/fields?custom-only=true&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/get-contact-fields-response.json"))));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/company/fields?custom-only=true"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));
    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/COMPANY/fields?custom-only=false"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/deal/fields?custom-only=true"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));
    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/DEAL/fields?custom-only=false"))
            .willReturn(
                aResponse()
                    .withStatus(400)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    String dealEsSearchResponse =
        FileUtil.getStringFrom("queries/es/response/associated-deal-response-for-company.json");

    String esSearchResponse =
        FileUtil.getStringFrom("queries/es/response/contact-search-by-phone-number.json");
    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/6-contact/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512")
            )
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(esSearchResponse)));

    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/6-deal/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(dealEsSearchResponse)));

    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/6-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/id-name-search-es-response-for-contact.json"))));

    String companyESSearchResponse =
        FileUtil.getStringFrom("queries/es/response/company-response-associated-entity-search.json");
    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/6-company/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(companyESSearchResponse)));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/companies/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    String searchFilter = FileUtil.getStringFrom("queries/es/request/associated-entity-list-.json");

    // when
    MvcResult mvcResult =
        mvc.perform(
                post("/v1/search/company/associated-with-entity?q=name:testSearchQuery")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 6L, allowedPermissions))
                    .content(searchFilter))
            .andExpect(status().isOk())
            .andReturn();

    // then
    wireMockRule.verify(
        0, getRequestedFor(urlEqualTo("/v1/entities/company/fields?custom-only=true")));
    JSONAssert.assertEquals(
        FileUtil.getStringFrom("contracts/company-associated-with-deal-contact-search-response.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.LENIENT);
  }

  @Test
  @Sql("/test-scripts/create-map-field-for-company-associated-with-company-deal.sql")
  public void givenContactSearchRequest_withAssociatedEntityCompanyAndDeal_shouldReturnResponse() throws Exception {

    // given
    SecurityContextHolder.getContext().setAuthentication(null);
    Action companyAction = new Action();
    companyAction.read(true);
    companyAction.readAll(true);
    PermissionDTO companyPermission = new PermissionDTO();
    companyPermission.setAction(companyAction);
    companyPermission.setName("company");
    PermissionDTO contactPermission = new PermissionDTO();
    contactPermission.setAction(companyAction);
    contactPermission.setName("contact");
    PermissionDTO dealPermission = new PermissionDTO();
    dealPermission.setAction(companyAction);
    dealPermission.setName("deal");
    Set<PermissionDTO> allowedPermissions =
        Stream.of(companyPermission, contactPermission, dealPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/companies/share-rule/access/read"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/deals/share-rule/access/read"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/contact/fields?custom-only=true&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/get-contact-fields-response.json"))));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/company/fields?custom-only=true"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));
    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/COMPANY/fields?custom-only=false"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/deal/fields?custom-only=true"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));
    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/DEAL/fields?custom-only=false"))
            .willReturn(
                aResponse()
                    .withStatus(400)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    String dealEsSearchResponse =
        FileUtil.getStringFrom("queries/es/response/associated-deal-response-for-company.json");

    String esSearchResponse =
        FileUtil.getStringFrom("queries/es/response/contact-search-by-phone-number.json");
    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/6-contact/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512")
            )
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(esSearchResponse)));

    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/6-deal/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(dealEsSearchResponse)));

    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/6-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/id-name-search-es-response-for-contact.json"))));

    String companyESSearchResponse =
        FileUtil.getStringFrom("queries/es/response/company-response-associated-entity-search.json");
    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/6-company/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(companyESSearchResponse)));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/companies/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    String searchFilter = FileUtil.getStringFrom("queries/es/request/associated-entity-list-.json");

    // when
    MvcResult mvcResult =
        mvc.perform(
                post("/v1/search/contact/associated-with-entity?q=name:testSearchQuery")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 6L, allowedPermissions))
                    .content(searchFilter))
            .andExpect(status().isOk())
            .andReturn();

    // then
    wireMockRule.verify(
        0, getRequestedFor(urlEqualTo("/v1/entities/company/fields?custom-only=true")));
    JSONAssert.assertEquals(
        FileUtil.getStringFrom("contracts/contact-associated-with-deal-and-company-search-response.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.LENIENT);
  }

  @Test
  @Sql("/test-scripts/create-and-map-custom-field-and-es-field.sql")
  public void givenWhatsAppEntitySearchRequest_shouldReturnResponse() throws Exception {

    // given
    Action contactAction = new Action();
    contactAction.read(true);
    contactAction.sms(true);
    PermissionDTO contactPermission = new PermissionDTO();
    contactPermission.setAction(contactAction);
    contactPermission.setName("contact");

    Action leadAction = new Action();
    leadAction.read(true);
    leadAction.sms(true);
    PermissionDTO leadPermission = new PermissionDTO();
    leadPermission.setAction(leadAction);
    leadPermission.setName("lead");

    Set<PermissionDTO> allowedPermissions = Stream.of(contactPermission, leadPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("response/contact-share-rule-resolve-access-for-sms-response.json"))));
    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("response/lead-share-rule-resolve-access-response.json"))));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("response/lead-share-rule-resolve-access-for-sms-response.json"))));

    String leadEsSearchResponse =
        FileUtil.getStringFrom("queries/es/response/whatsapp-lead-es-search-response.json");
    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo("/1-lead/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(leadEsSearchResponse)));

    String contactEsSearchResponse =
        FileUtil.getStringFrom("queries/es/response/whatsapp-contact-es-search-response.json");
    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo("/1-contact/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(contactEsSearchResponse)));
    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{}")));

    // when
    String searchFilter = FileUtil.getStringFrom("queries/es/request/whatsapp-entity-search-request.json");
    MvcResult mvcResult =
        mvc.perform(
                post("/v1/search/whatsapp-entity")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(searchFilter)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(4L, 1L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then
    List<ServeEvent> allServeEvents = newEsWireMockRule.getAllServeEvents();
    ServeEvent leadSearchEvent = allServeEvents.stream().filter(serveEvent -> serveEvent.getRequest().getUrl().startsWith("/1-lead/_search"))
        .findFirst().get();
    ServeEvent contactSearchEvent = allServeEvents.stream().filter(serveEvent -> serveEvent.getRequest().getUrl().startsWith("/1-contact/_search"))
        .findFirst().get();

    JSONAssert.assertEquals(FileUtil.getStringFrom("contracts/whatsapp-lead-search-es-query.json"),
        leadSearchEvent.getRequest().getBodyAsString(), JSONCompareMode.STRICT);
    JSONAssert.assertEquals(FileUtil.getStringFrom("contracts/whatsapp-contact-search-es-query.json"),
        contactSearchEvent.getRequest().getBodyAsString(), JSONCompareMode.STRICT);

    JSONAssert.assertEquals(
        FileUtil.getStringFrom("contracts/whatsapp-entity-search-response.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.STRICT);
  }

  @Test
  @Sql("/test-scripts/create-and-map-custom-field-and-es-field-lead.sql")
  public void givenLeadSearchRequestWithCampaignActivities_shouldReturnResponse() throws Exception {

    // given
    Action leadAction = new Action();
    leadAction.readAll(true);
    PermissionDTO leadPermission = new PermissionDTO();
    leadPermission.setAction(leadAction);
    leadPermission.setName("lead");
    Set<PermissionDTO> allowedPermissions = Stream.of(leadPermission).collect(Collectors.toSet());

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\":{}},\"accessByRecords\":{}}")));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/lead/fields?custom-only=true&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        FileUtil.getStringFrom("contracts/get-custom-field-response.json"))));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/get-custom-field-response.json"))));

    wireMockRule.stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{}")));

    String leadLookupResponse =
        FileUtil.getStringFrom("queries/es/response/lead-campaign-activities-search-response.json");
    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/1-lead/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(leadLookupResponse)));

    newEsWireMockRule.stubFor(
        WireMock.post(urlEqualTo(
                "/1-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(FileUtil.getStringFrom("contracts/id-name-search-es-response-for-contact.json"))));

    wireMockRule.stubFor(
        WireMock.post(urlEqualTo("/v1/users/search-for-id"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[1,2]")));

    // when
    String searchFilter = FileUtil.getStringFrom("queries/es/request/nested/campaign-activities-filter.json");
    MvcResult mvcResult =
        mvc.perform(
                post("/v1/search/lead?sort=myAddress,desc&page=0&size=100")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(searchFilter)
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 1L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then
    List<ServeEvent> allServeEvents = newEsWireMockRule.getAllServeEvents();
    ServeEvent leadSearchEvent = allServeEvents.stream().filter(serveEvent -> serveEvent.getRequest().getUrl().startsWith("/1-lead/_search"))
        .findFirst().get();

    JSONAssert.assertEquals(FileUtil.getStringFrom("contracts/lead-search-with-campaign-activities-field-es-query-request.json"),
        leadSearchEvent.getRequest().getBodyAsString(), JSONCompareMode.NON_EXTENSIBLE);

    JSONAssert.assertEquals(
        FileUtil.getStringFrom("contracts/lead-with-campaign-activities-search-response.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.STRICT);
  }
}
