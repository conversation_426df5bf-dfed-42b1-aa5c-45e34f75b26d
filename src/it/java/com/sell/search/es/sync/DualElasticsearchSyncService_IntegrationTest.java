package com.sell.search.es.sync;

import com.sell.search.es.abstraction.ElasticsearchClient;
import com.sell.search.es.abstraction.model.*;
import com.sell.search.es.abstraction.impl.Elasticsearch6Client;
import com.sell.search.es.abstraction.impl.Elasticsearch8Client;
import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.testcontainers.elasticsearch.ElasticsearchContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.io.IOException;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest
@Testcontainers
@ActiveProfiles("test")
public class DualElasticsearchSyncService_IntegrationTest {

    @Container
    static ElasticsearchContainer legacyElasticsearch = new ElasticsearchContainer("elasticsearch:6.8.17")
            .withExposedPorts(9200)
            .withEnv("discovery.type", "single-node")
            .withEnv("ES_JAVA_OPTS", "-Xms512m -Xmx512m");

    @Container
    static ElasticsearchContainer newElasticsearch = new ElasticsearchContainer("elasticsearch:8.11.3")
            .withExposedPorts(9200)
            .withEnv("discovery.type", "single-node")
            .withEnv("xpack.security.enabled", "false")
            .withEnv("ES_JAVA_OPTS", "-Xms512m -Xmx512m");

    private DualElasticsearchSyncService dualSyncService;
    private ElasticsearchClient legacyClient;
    private ElasticsearchClient newClient;

    @BeforeEach
    void setUp() {
        // Setup legacy ES client (6.x)
        RestHighLevelClient legacyRestClient = new RestHighLevelClient(
                RestClient.builder(new HttpHost("localhost", legacyElasticsearch.getMappedPort(9200), "http")));
        legacyClient = new Elasticsearch6Client(legacyRestClient);

        // Setup new ES client (8.x)
        newClient = new Elasticsearch8Client("localhost", newElasticsearch.getMappedPort(9200));

        // Create dual sync service
        dualSyncService = new DualElasticsearchSyncService(legacyClient, newClient, true);
    }

    @AfterEach
    void tearDown() throws IOException {
        if (legacyClient != null) {
            legacyClient.close();
        }
        if (newClient != null) {
            newClient.close();
        }
    }

    @Test
    void testDualIndexSync() throws IOException {
        // Given
        String indexName = "test-index";
        String documentId = "test-doc-1";
        Map<String, Object> source = new HashMap<>();
        source.put("name", "Test Document");
        source.put("description", "This is a test document");
        source.put("timestamp", System.currentTimeMillis());

        IndexRequest request = IndexRequest.builder()
                .indexName(indexName)
                .documentType("_doc")
                .documentId(documentId)
                .source(source)
                .build();

        // When
        DualSyncResult<IndexResponse> result = dualSyncService.index(request);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getPrimaryResult().getId()).isEqualTo(documentId);
        assertThat(result.getLegacyResult().getId()).isEqualTo(documentId);
        assertThat(result.getNewResult().getId()).isEqualTo(documentId);

        // Verify document exists in both instances
        assertThat(legacyClient.indexExists(indexName)).isTrue();
        assertThat(newClient.indexExists(indexName)).isTrue();
    }

    @Test
    void testDualBulkSync() throws IOException {
        // Given
        String indexName = "test-bulk-index";
        List<IndexRequest> indexRequests = new ArrayList<>();
        
        for (int i = 1; i <= 5; i++) {
            Map<String, Object> source = new HashMap<>();
            source.put("name", "Bulk Document " + i);
            source.put("number", i);
            
            indexRequests.add(IndexRequest.builder()
                    .indexName(indexName)
                    .documentType("_doc")
                    .documentId("bulk-doc-" + i)
                    .source(source)
                    .build());
        }

        BulkRequest bulkRequest = BulkRequest.builder()
                .indexRequests(indexRequests)
                .build();

        // When
        DualSyncResult<BulkResponse> result = dualSyncService.bulk(bulkRequest);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getPrimaryResult().getItems()).hasSize(5);
        assertThat(result.getLegacyResult().getItems()).hasSize(5);
        assertThat(result.getNewResult().getItems()).hasSize(5);

        // Verify all documents were indexed successfully
        for (BulkResponse.BulkItemResponse item : result.getPrimaryResult().getItems()) {
            assertThat(item.isFailed()).isFalse();
            assertThat(item.getId()).startsWith("bulk-doc-");
        }
    }

    @Test
    void testDualIndexCreation() throws IOException {
        // Given
        String indexName = "test-create-index";
        Map<String, Object> settings = new HashMap<>();
        settings.put("number_of_shards", 1);
        settings.put("number_of_replicas", 0);

        Map<String, Object> mappings = new HashMap<>();
        Map<String, Object> properties = new HashMap<>();
        Map<String, Object> nameField = new HashMap<>();
        nameField.put("type", "text");
        properties.put("name", nameField);
        mappings.put("properties", properties);

        CreateIndexRequest request = CreateIndexRequest.builder()
                .indexName(indexName)
                .settings(settings)
                .mappings(mappings)
                .documentType("_doc")
                .build();

        // When
        DualSyncResult<Boolean> result = dualSyncService.createIndex(request);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getPrimaryResult()).isTrue();
        assertThat(result.getLegacyResult()).isTrue();
        assertThat(result.getNewResult()).isTrue();

        // Verify index exists in both instances
        assertThat(legacyClient.indexExists(indexName)).isTrue();
        assertThat(newClient.indexExists(indexName)).isTrue();
    }

    @Test
    void testSearchFromPrimaryInstance() throws IOException {
        // Given
        String indexName = "test-search-index";
        
        // First create the index and add some documents
        Map<String, Object> settings = new HashMap<>();
        settings.put("number_of_shards", 1);
        settings.put("number_of_replicas", 0);

        CreateIndexRequest createRequest = CreateIndexRequest.builder()
                .indexName(indexName)
                .settings(settings)
                .build();
        
        dualSyncService.createIndex(createRequest);

        // Add test documents
        for (int i = 1; i <= 3; i++) {
            Map<String, Object> source = new HashMap<>();
            source.put("title", "Search Document " + i);
            source.put("content", "This is content for document " + i);
            
            IndexRequest indexRequest = IndexRequest.builder()
                    .indexName(indexName)
                    .documentType("_doc")
                    .documentId("search-doc-" + i)
                    .source(source)
                    .build();
            
            dualSyncService.index(indexRequest);
        }

        // Wait for indexing to complete
        Thread.sleep(1000);

        // When
        SearchRequest searchRequest = SearchRequest.builder()
                .indices(new String[]{indexName})
                .from(0)
                .size(10)
                .build();

        SearchResponse searchResponse = dualSyncService.search(searchRequest);

        // Then
        assertThat(searchResponse.getTotalHits()).isEqualTo(3);
        assertThat(searchResponse.getHits()).hasSize(3);
        
        for (SearchResponse.SearchHit hit : searchResponse.getHits()) {
            assertThat(hit.getId()).startsWith("search-doc-");
            assertThat(hit.getSource()).containsKey("title");
            assertThat(hit.getSource()).containsKey("content");
        }
    }

    @Test
    void testDualSyncWithPartialFailure() {
        // This test would simulate a scenario where one ES instance fails
        // For now, we'll test with dual sync disabled to simulate fallback behavior
        
        // Given
        DualElasticsearchSyncService disabledSyncService = 
            new DualElasticsearchSyncService(legacyClient, newClient, false);
        
        String indexName = "test-fallback-index";
        String documentId = "fallback-doc-1";
        Map<String, Object> source = new HashMap<>();
        source.put("name", "Fallback Test Document");

        IndexRequest request = IndexRequest.builder()
                .indexName(indexName)
                .documentType("_doc")
                .documentId(documentId)
                .source(source)
                .build();

        // When
        DualSyncResult<IndexResponse> result = disabledSyncService.index(request);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getPrimaryResult().getId()).isEqualTo(documentId);
        assertThat(result.getLegacyResult()).isNotNull();
        assertThat(result.getNewResult()).isNull(); // Should be null when dual sync is disabled
    }
}
