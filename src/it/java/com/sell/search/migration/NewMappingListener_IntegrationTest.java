package com.sell.search.migration;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.putRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static org.assertj.core.api.Assertions.assertThat;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.sell.search.migration.domain.IndexMapping;
import com.sell.search.migration.domain.MigrationStatus;
import io.zonky.test.db.AutoConfigureEmbeddedDatabase;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import org.apache.commons.io.FileUtils;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@RunWith(SpringRunner.class)
@AutoConfigureEmbeddedDatabase
@AutoConfigureMockMvc
@TestPropertySource(properties = {"newElasticsearch.port=9090"})
public class NewMappingListener_IntegrationTest {

  public static WireMockServer wireMock = new WireMockServer(9090);
  @Autowired
  private ResourceLoader resourceLoader;
  @Autowired
  private IndexMappingRepository repository;
  @Autowired
  private NewMappingListener listener;

  @BeforeClass
  public static void setUp() {
    wireMock.start();

    wireMock.stubFor(WireMock.put(urlEqualTo("/deal-*/_mapping/deal?master_timeout=2m&timeout=2m"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"acknowledged\": true}")
        )
    );

    wireMock.stubFor(WireMock.put(urlEqualTo("/lead-*/_mapping/lead?master_timeout=2m&timeout=2m"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"acknowledged\": true}")
        )
    );

    wireMock.stubFor(WireMock.put(urlEqualTo("/company-*/_mapping/company?master_timeout=2m&timeout=2m"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"acknowledged\": true}")
        )
    );

    wireMock.stubFor(WireMock.put(urlEqualTo("/contact-*/_mapping/contact?master_timeout=2m&timeout=2m"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"acknowledged\": true}")
        )
    );
  }

  @AfterClass
  public static void tearDown() {
    wireMock.stop();
  }

  @Test
  public void givenNewMappingsToAdd_shouldRunMigrations() throws InterruptedException, IOException {
    CountDownLatch latch = new CountDownLatch(1);
    latch.await(10, TimeUnit.SECONDS);

    wireMock.verify(
        putRequestedFor(urlEqualTo("/deal-*/_mapping/deal?master_timeout=2m&timeout=2m"))
            .withRequestBody(equalToJson(getResourceAsString("classpath:contracts/es-add-products-mapping-on-deal.json")))
    );

    List<IndexMapping> completedJobs = repository.findByStatus(MigrationStatus.COMPLETED);
    assertThat(completedJobs.size()).isEqualTo(29);

    assertThat(completedJobs.get(0).getTargetIndex()).isEqualTo("deal");
    assertThat(completedJobs.get(0).getType()).isEqualTo("deal");
    assertThat(completedJobs.get(0).getName()).isEqualTo("Deal with products");

    assertThat(completedJobs.get(1).getTargetIndex()).isEqualTo("deal");
    assertThat(completedJobs.get(1).getType()).isEqualTo("deal");
    assertThat(completedJobs.get(1).getName()).isEqualTo("Deal with product");

    assertThat(completedJobs.get(2).getTargetIndex()).isEqualTo("lead");
    assertThat(completedJobs.get(2).getType()).isEqualTo("lead");
    assertThat(completedJobs.get(2).getName()).isEqualTo("Created Via And Updated Via Source");

    assertThat(completedJobs.get(3).getTargetIndex()).isEqualTo("company");
    assertThat(completedJobs.get(3).getType()).isEqualTo("company");
    assertThat(completedJobs.get(3).getName()).isEqualTo("Created Via And Updated Via Source Company");

    assertThat(completedJobs.get(4).getTargetIndex()).isEqualTo("deal");
    assertThat(completedJobs.get(4).getType()).isEqualTo("deal");
    assertThat(completedJobs.get(4).getName()).isEqualTo("Created Via And Updated Via Source Deal");

    assertThat(completedJobs.get(5).getTargetIndex()).isEqualTo("contact");
    assertThat(completedJobs.get(5).getType()).isEqualTo("contact");
    assertThat(completedJobs.get(5).getName()).isEqualTo("Created Via And Updated Via Source Contact");

    assertThat(completedJobs.get(6).getTargetIndex()).isEqualTo("lead");
    assertThat(completedJobs.get(6).getType()).isEqualTo("lead");
    assertThat(completedJobs.get(6).getName()).isEqualTo("Lead UTM fields");

    assertThat(completedJobs.get(7).getTargetIndex()).isEqualTo("deal");
    assertThat(completedJobs.get(7).getType()).isEqualTo("deal");
    assertThat(completedJobs.get(7).getName()).isEqualTo("Deal UTM fields");

    assertThat(completedJobs.get(8).getTargetIndex()).isEqualTo("contact");
    assertThat(completedJobs.get(8).getType()).isEqualTo("contact");
    assertThat(completedJobs.get(8).getName()).isEqualTo("Contact Imported By");

    assertThat(completedJobs.get(9).getTargetIndex()).isEqualTo("company");
    assertThat(completedJobs.get(9).getType()).isEqualTo("company");
    assertThat(completedJobs.get(9).getName()).isEqualTo("Company Imported By");

    assertThat(completedJobs.get(10).getTargetIndex()).isEqualTo("contact");
    assertThat(completedJobs.get(10).getType()).isEqualTo("contact");
    assertThat(completedJobs.get(10).getName()).isEqualTo("Contact Source and UTM Fields");

    assertThat(completedJobs.get(11).getTargetIndex()).isEqualTo("company");
    assertThat(completedJobs.get(11).getType()).isEqualTo("company");
    assertThat(completedJobs.get(11).getName()).isEqualTo("Company Owned By");

    assertThat(completedJobs.get(12).getTargetIndex()).isEqualTo("deal");
    assertThat(completedJobs.get(12).getType()).isEqualTo("deal");
    assertThat(completedJobs.get(12).getName()).isEqualTo("Deal Imported By");

    assertThat(completedJobs.get(13).getTargetIndex()).isEqualTo("lead");
    assertThat(completedJobs.get(13).getType()).isEqualTo("lead");
    assertThat(completedJobs.get(13).getName()).isEqualTo("Lead Score");

    assertThat(completedJobs.get(14).getTargetIndex()).isEqualTo("contact");
    assertThat(completedJobs.get(14).getType()).isEqualTo("contact");
    assertThat(completedJobs.get(14).getName()).isEqualTo("Contact Score");

    assertThat(completedJobs.get(15).getTargetIndex()).isEqualTo("lead");
    assertThat(completedJobs.get(15).getType()).isEqualTo("lead");
    assertThat(completedJobs.get(15).getName()).isEqualTo("Lead Phone id");

    assertThat(completedJobs.get(16).getTargetIndex()).isEqualTo("lead");
    assertThat(completedJobs.get(16).getType()).isEqualTo("lead");
    assertThat(completedJobs.get(16).getName()).isEqualTo("Lead Company Phone id");

    assertThat(completedJobs.get(17).getTargetIndex()).isEqualTo("contact");
    assertThat(completedJobs.get(17).getType()).isEqualTo("contact");
    assertThat(completedJobs.get(17).getName()).isEqualTo("Contact Phone id");

    assertThat(completedJobs.get(18).getTargetIndex()).isEqualTo("deal");
    assertThat(completedJobs.get(18).getType()).isEqualTo("deal");
    assertThat(completedJobs.get(18).getName()).isEqualTo("Deal with products with units");

    assertThat(completedJobs.get(19).getTargetIndex()).isEqualTo("lead");
    assertThat(completedJobs.get(19).getType()).isEqualTo("lead");
    assertThat(completedJobs.get(19).getName()).isEqualTo("Lead Address Coordinate");

    assertThat(completedJobs.get(20).getTargetIndex()).isEqualTo("lead");
    assertThat(completedJobs.get(20).getType()).isEqualTo("lead");
    assertThat(completedJobs.get(20).getName()).isEqualTo("Lead Company Address Coordinate");

    assertThat(completedJobs.get(21).getTargetIndex()).isEqualTo("contact");
    assertThat(completedJobs.get(21).getType()).isEqualTo("contact");
    assertThat(completedJobs.get(21).getName()).isEqualTo("Contact Address Coordinate");

    assertThat(completedJobs.get(22).getTargetIndex()).isEqualTo("company");
    assertThat(completedJobs.get(22).getType()).isEqualTo("company");
    assertThat(completedJobs.get(22).getName()).isEqualTo("Company Address Coordinate");

    assertThat(completedJobs.get(23).getTargetIndex()).isEqualTo("lead");
    assertThat(completedJobs.get(23).getType()).isEqualTo("lead");
    assertThat(completedJobs.get(23).getName()).isEqualTo("Lead Campaign Activities");

    assertThat(completedJobs.get(24).getTargetIndex()).isEqualTo("contact");
    assertThat(completedJobs.get(24).getType()).isEqualTo("contact");
    assertThat(completedJobs.get(24).getName()).isEqualTo("Contact Campaign Activities");

    assertThat(completedJobs.get(25).getTargetIndex()).isEqualTo("company");
    assertThat(completedJobs.get(25).getType()).isEqualTo("company");
    assertThat(completedJobs.get(25).getName()).isEqualTo("Company Unique text fields");

    assertThat(completedJobs.get(26).getTargetIndex()).isEqualTo("lead");
    assertThat(completedJobs.get(26).getType()).isEqualTo("lead");
    assertThat(completedJobs.get(26).getName()).isEqualTo("Lead Campaign Activities - Add sentAt");

    assertThat(completedJobs.get(27).getTargetIndex()).isEqualTo("contact");
    assertThat(completedJobs.get(27).getType()).isEqualTo("contact");
    assertThat(completedJobs.get(27).getName()).isEqualTo("Contact Campaign Activities - Add sentAt");

    assertThat(completedJobs.get(28).getTargetIndex()).isEqualTo("deal");
    assertThat(completedJobs.get(28).getType()).isEqualTo("deal");
    assertThat(completedJobs.get(28).getName()).isEqualTo("Deal Score");
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    Resource resource = resourceLoader.getResource(resourcePath);
    File file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }
}
