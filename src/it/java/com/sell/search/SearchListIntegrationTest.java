package com.sell.search;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.sell.search.core.domain.Action;
import com.sell.search.core.domain.PermissionDTO;
import com.sell.search.util.FileUtil;
import com.sell.search.util.TestEntity;
import io.zonky.test.db.AutoConfigureEmbeddedDatabase;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

@SpringBootTest
@RunWith(SpringRunner.class)
@AutoConfigureEmbeddedDatabase
@AutoConfigureMockMvc
@TestPropertySource(
    properties = {
        "elasticsearch.port=9090",
        "newElasticsearch.port=7071",
        "client.entity.basePath=http://localhost:9090"
    })
public class SearchListIntegrationTest {
  @Autowired
  private MockMvc mvc;
  @Test
  @Sql("/test-scripts/search-list-with-relative-date-filters.sql")
  public void givenRequestToGetPreferredSearchListHavingRelativeDateFilter_shouldFetch() throws Exception {
    //given
    Action leadAction = new Action();
    leadAction.readAll(true);
    PermissionDTO leadPermission = new PermissionDTO();
    leadPermission.setName("searchList");
    leadPermission.setAction(leadAction);
    Set<PermissionDTO> allowedPermissions = Stream.of(leadPermission).collect(Collectors.toSet());
    //when
    MvcResult mvcResult =
        mvc.perform(
                get("/v1/search-lists/preferred?entityType=LEAD")
            .header(
                "Authorization", "Bearer " + TestEntity.getJwt(6L, 6L, allowedPermissions)))
            .andExpect(status().isOk())
            .andReturn();
    // then
    JSONAssert.assertEquals(
        FileUtil.getStringFrom("response/search-list-relative-date-filter-response.json"),
        mvcResult.getResponse().getContentAsString(),
        new CustomComparator(JSONCompareMode.STRICT));
  }

  @Test
  public void shouldCreateSearchList() throws Exception {
    //given
    Action readAction = new Action();
    readAction.read(true).write(true).readAll(true);

    PermissionDTO emailPermission = new PermissionDTO();
    emailPermission.setName("email");
    emailPermission.setAction(readAction);
    PermissionDTO searchListPermission = new PermissionDTO();
    searchListPermission.setName("searchList");
    searchListPermission.setAction(readAction);;
    Set<PermissionDTO> allowedPermissions = Stream.of(emailPermission, searchListPermission).collect(Collectors.toSet());
    String searchListCreateRequest = FileUtil.getStringFrom("request/search-list-create-request.json");

    //when
    MvcResult mvcResult =
        mvc.perform(
                post("/v1/search-lists/email")
                    .header(
                        "Authorization", "Bearer " + TestEntity.getJwt(6L, 6L, allowedPermissions))
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(searchListCreateRequest))
            .andExpect(status().isOk())
            .andReturn();
    //then
    JSONAssert.assertEquals(
        FileUtil.getStringFrom("response/search-list-create-response.json"),
        mvcResult.getResponse().getContentAsString(),
        new CustomComparator(JSONCompareMode.STRICT, new Customization("createdAt", (o1, o2) -> true),
            new Customization("updatedAt", (o1, o2) -> true),
            new Customization("id", (o1, o2) -> true)
        ));
  }
}
