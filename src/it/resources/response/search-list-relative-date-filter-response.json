{"createdAt": null, "updatedAt": null, "createdBy": null, "updatedBy": null, "recordActions": {"read": true, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}, "metaData": null, "id": 1, "entityType": "LEAD", "name": "Custom Lead list1", "description": "system default", "searchRequest": {"fields": ["name", "estimatedValue", "product", "company", "pipelineStage", "estimatedClosureOn", "ownedBy"], "jsonRule": {"id": null, "field": null, "type": null, "input": null, "operator": null, "value": null, "data": null, "property": null, "primaryField": null, "condition": "AND", "not": null, "rules": [{"id": "ownedBy", "field": "ownedBy", "type": "long", "input": null, "operator": "equal", "value": null, "data": null, "property": null, "primaryField": null, "condition": null, "not": null, "rules": null, "group": false}, {"id": "createdAt", "field": "createdAt", "type": "date", "input": null, "operator": "last_seven_days", "value": null, "data": null, "property": null, "primaryField": null, "condition": null, "not": null, "rules": null, "group": false}], "group": true}}, "preferredFields": [], "systemDefault": false, "size": 10, "sort": null, "view": "LIST", "pipelineId": null, "freezedColumn": null}