{"createdAt": "2024-02-16T06:49:07.986+0000", "updatedAt": "2024-02-16T06:49:07.986+0000", "createdBy": 6, "updatedBy": 6, "recordActions": {"read": true, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}, "metaData": null, "id": 1, "entityType": "EMAIL", "name": "Received emails", "description": "All Received Emails", "searchRequest": {"fields": null, "jsonRule": {"id": null, "field": null, "type": null, "input": null, "operator": null, "value": null, "data": null, "property": null, "primaryField": null, "condition": "AND", "not": null, "rules": [{"id": "direction", "field": "direction", "type": "string", "input": "string", "operator": "equal", "value": "received", "data": null, "property": null, "primaryField": null, "condition": null, "not": null, "rules": null, "group": false}], "group": true}}, "systemDefault": false, "sort": "updatedAt,desc", "size": 10, "view": null, "freezedColumn": null, "pipelineId": null}