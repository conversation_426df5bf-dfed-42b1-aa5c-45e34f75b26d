{"from": 0, "size": 100, "query": {"bool": {"must": [{"bool": {"must": [{"nested": {"query": {"term": {"pipeline.id": {"value": 4568545, "boost": 1.0}}}, "path": "pipeline", "ignore_unmapped": false, "score_mode": "sum", "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}, "_source": {"includes": ["name", "pipeline", "pipelineStage", "products", "id", "ownerId"], "excludes": []}, "sort": [{"kylas_text_1": {"order": "desc", "missing": "_last"}}], "aggregations": {"pipelineStage": {"nested": {"path": "pipelineStage"}, "aggregations": {"byPipelineStage": {"terms": {"field": "pipelineStage.id", "size": 40, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": [{"_count": "desc"}, {"_key": "asc"}]}, "aggregations": {"reverse_to_parent": {"reverse_nested": {}, "aggregations": {"topDocuments": {"top_hits": {"from": 0, "size": 100, "version": false, "seq_no_primary_term": false, "explain": false, "_source": {"includes": ["name", "pipeline", "pipelineStage", "products", "id", "ownerId"], "excludes": []}, "sort": [{"kylas_text_1": {"order": "desc", "missing": "_last"}}]}}}}}}}}}}