{"from": 0, "size": 10, "query": {"bool": {"must": [{"bool": {"should": [{"terms": {"ownerId": [4], "boost": 1.0}}, {"terms": {"id": [259, 260, 261, 262], "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}, {"bool": {"must": [{"bool": {"should": [{"query_string": {"query": "*tony*", "fields": ["firstName^1.0", "lastName^1.0"], "type": "best_fields", "default_operator": "or", "max_determinized_states": 10000, "enable_position_increments": true, "fuzziness": "0", "fuzzy_prefix_length": 0, "fuzzy_max_expansions": 50, "phrase_slop": 0, "escape": false, "auto_generate_synonyms_phrase_query": true, "fuzzy_transpositions": true, "boost": 1.0}}, {"nested": {"query": {"query_string": {"query": "*tony*", "fields": ["phoneNumbers.value^1.0"], "type": "best_fields", "default_operator": "or", "max_determinized_states": 10000, "enable_position_increments": true, "fuzziness": "0", "fuzzy_prefix_length": 0, "fuzzy_max_expansions": 50, "phrase_slop": 0, "escape": false, "auto_generate_synonyms_phrase_query": true, "fuzzy_transpositions": true, "boost": 1.0}}, "path": "phoneNumbers", "ignore_unmapped": false, "score_mode": "sum", "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}, "_source": {"includes": ["id", "firstName", "lastName", "phoneNumbers", "ownerId"], "excludes": []}, "sort": [{"updatedAt": {"order": "desc"}}]}