{"from": 0, "size": 100, "query": {"match_all": {"boost": 1.0}}, "_source": {"includes": ["id", "name", "estimatedValue", "product", "estimatedClosureOn", "actualClosureDate", "actualValue", "company", "ownedBy", "associatedContacts", "pipeline", "pipelineStage", "forecastingType", "created<PERSON>y", "createdAt", "updatedBy", "updatedAt", "campaign", "source", "customFieldValues", "kylas_MULTI_PICKLIST_2", "kylas_text_1", "id", "ownerId"], "excludes": []}, "sort": [{"kylas_text_1": {"order": "desc", "missing": "_last"}}]}