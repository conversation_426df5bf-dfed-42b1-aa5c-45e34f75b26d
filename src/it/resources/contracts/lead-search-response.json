{"content": [{"pipeline": 11, "firstName": "Bark", "lastName": "Nomad", "customFieldValues": {"myAddress": "mumbai pune highway", "myCity": 7548}, "name": "Nomad", "id": 259, "ownerId": 6, "pipelineStage": 945, "recordActions": {"read": true, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": false, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}, "version": 6, "products": [{"name": "CRM", "id": 123}, {"name": "Marketing", "id": 124}]}, {"emails": [{"type": "OFFICE", "value": "<EMAIL>", "primary": true}, {"type": "OFFICE", "value": "<EMAIL>", "primary": false}], "firstName": null, "lastName": "YesMad", "customFieldValues": {"myAddress": "mumbai pune highway", "myCity": 7548}, "name": "YesMad", "id": 260, "ownerId": 6, "recordActions": {"read": true, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": false, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}, "version": 6, "products": [{"name": "Marketing", "id": 124}]}, {"firstName": null, "lastName": "Hello", "companyPhones": [{"code": "IN", "dialCode": "+91", "type": "MOBILE", "value": "1231231231", "primary": true}], "customFieldValues": {"myAddress": "mumbai pune highway", "myCity": 7548}, "name": "Hello", "id": 261, "salutation": 89, "ownerId": 6, "recordActions": {"read": true, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": false, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}, "version": 6, "phoneNumbers": [{"code": "IN", "dialCode": "+91", "type": "MOBILE", "value": "1231231231", "primary": true}, {"code": "IN", "dialCode": "+91", "type": "WORK", "value": "1231231232", "primary": false}]}, {"firstName": null, "lastName": "Hello", "companyPhones": [{"code": "IN", "dialCode": "+91", "type": "MOBILE", "value": "1231231231", "primary": true}, {"code": "IN", "dialCode": "+91", "type": "WORK", "value": "1231231232", "primary": false}], "customFieldValues": {"myAddress": "mumbai pune highway", "myCountry": [7598, 7599]}, "name": "Hello", "id": 262, "ownerId": 6, "recordActions": {"read": true, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": false, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}, "version": 6}], "metaData": {"idNameStore": {"myCountry": {"7598": "Mumbai", "7599": "Delhi"}, "myCity": {"7548": "Mumbai"}}}, "last": true, "totalPages": 1, "totalElements": 4, "sort": [{"direction": "DESC", "property": "my<PERSON><PERSON><PERSON>", "ignoreCase": false, "nullHandling": "NATIVE", "descending": true, "ascending": false}], "first": true, "numberOfElements": 4, "size": 100, "number": 0}