{"from": 0, "size": 100, "query": {"match_all": {"boost": 1.0}}, "_source": {"includes": ["myTextField", "field2", "paragraphField", "myText", "my<PERSON><PERSON><PERSON>", "myCity", "myCountry", "salutation", "pipeline", "pipelineStage", "firstName", "lastName", "emails", "products", "forecastingType", "customFieldValues", "kylas_text_1", "kylas_MULTI_PICKLIST_2", "kylas_PICK_LIST_2", "id", "ownerId", "convertedBy"], "excludes": []}, "sort": [{"updatedAt": {"order": "desc", "missing": "_last"}}]}