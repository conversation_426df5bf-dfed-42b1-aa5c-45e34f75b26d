{"content": [{"name": "Nomad", "id": 259, "ownerId": 6, "recordActions": {"read": true, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": false, "deleteAll": false, "quotation": false}, "version": 6}, {"name": "YesMad", "id": 259, "ownerId": 6, "recordActions": {"read": true, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": false, "deleteAll": false, "quotation": false}, "version": 6}, {"name": "Hello", "id": 259, "ownerId": 6, "recordActions": {"read": true, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": false, "deleteAll": false, "quotation": false}, "version": 6}, {"customFieldValues": {"myMulti": [{"name": "val299", "id": 299}]}, "name": "myDeal", "id": 259, "ownerId": 6, "recordActions": {"read": true, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": false, "deleteAll": false, "quotation": false}, "version": 6}], "metaData": {"idNameStore": {}}, "last": true, "totalPages": 1, "totalElements": 4, "sort": [{"direction": "DESC", "property": "updatedAt", "ignoreCase": false, "nullHandling": "NATIVE", "ascending": false, "descending": true}], "first": true, "numberOfElements": 4, "size": 100, "number": 0}