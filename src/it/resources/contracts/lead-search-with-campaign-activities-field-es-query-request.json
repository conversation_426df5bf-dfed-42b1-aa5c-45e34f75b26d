{"from": 0, "size": 100, "query": {"bool": {"must": [{"bool": {"must": [{"nested": {"query": {"bool": {"must": [{"term": {"campaignActivities.id": {"value": 203, "boost": 1.0}}}, {"nested": {"query": {"term": {"campaignActivities.activities.id": {"value": 305, "boost": 1.0}}}, "path": "campaignActivities.activities", "ignore_unmapped": false, "score_mode": "sum", "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}, "path": "campaignActivities", "ignore_unmapped": false, "score_mode": "sum", "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}, "_source": {"includes": ["firstName", "lastName", "ownerId", "pipelineStage", "companyName", "phoneNumbers", "emails", "id", "recordActions", "customFieldValues", "kylas_text_1", "kylas_MULTI_PICKLIST_2", "kylas_PICK_LIST_2", "id", "ownerId", "convertedBy"], "excludes": []}, "sort": [{"kylas_text_1": {"order": "desc", "missing": "_last"}}]}