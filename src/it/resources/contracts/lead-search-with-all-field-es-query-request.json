{"from": 0, "size": 100, "query": {"bool": {"must": [{"bool": {"must": [{"terms": {"firstName": ["tony stark"], "boost": 1.0}}, {"term": {"lastName": {"value": "last", "boost": 1.0}}}, {"term": {"ownerId": {"value": 1, "boost": 1.0}}}, {"term": {"companyName": {"value": "my company", "boost": 1.0}}}, {"term": {"salutation": {"value": 473, "boost": 1.0}}}, {"term": {"timezone": {"value": "Etc/GMT+12", "boost": 1.0}}}, {"term": {"city": {"value": "pune", "boost": 1.0}}}, {"term": {"state": {"value": "maharashtra", "boost": 1.0}}}, {"term": {"zipcode": {"value": "411033", "boost": 1.0}}}, {"term": {"country": {"value": "IN", "boost": 1.0}}}, {"wildcard": {"department": {"wildcard": "*department*", "boost": 1.0}}}, {"wildcard": {"facebook": {"wildcard": "*twitter*", "boost": 1.0}}}, {"bool": {"must_not": [{"wildcard": {"twitter": {"wildcard": "*facebook*", "boost": 1.0}}}], "adjust_pure_negative": true, "boost": 1.0}}, {"prefix": {"linkedIn": {"value": "http", "boost": 1.0}}}, {"wildcard": {"address": {"wildcard": "*address*", "boost": 1.0}}}, {"term": {"companyAddress": {"value": "company address", "boost": 1.0}}}, {"term": {"companyCity": {"value": "company city", "boost": 1.0}}}, {"term": {"companyState": {"value": "comapny state", "boost": 1.0}}}, {"term": {"companyZipcode": {"value": "411045", "boost": 1.0}}}, {"term": {"companyCountry": {"value": "IN", "boost": 1.0}}}, {"range": {"companyEmployees": {"from": 1.0, "to": null, "include_lower": false, "include_upper": true, "boost": 1.0}}}, {"range": {"companyAnnualRevenue": {"from": null, "to": 9000.0, "include_lower": true, "include_upper": false, "boost": 1.0}}}, {"term": {"companyWebsite": {"value": "http://company.com", "boost": 1.0}}}, {"term": {"companyIndustry": {"value": "REAL_ESTATE", "boost": 1.0}}}, {"term": {"companyBusinessType": {"value": "type", "boost": 1.0}}}, {"term": {"requirementName": {"value": "rname", "boost": 1.0}}}, {"term": {"requirementCurrency": {"value": "USD", "boost": 1.0}}}, {"range": {"requirementBudget": {"from": 40.0, "to": 9000.0, "include_lower": true, "include_upper": true, "boost": 1.0}}}, {"range": {"convertedAt": {"from": 1585751400000, "to": null, "include_lower": false, "include_upper": true, "boost": 1.0}}}, {"range": {"updatedAt": {"from": null, "to": 1588170600000, "include_lower": true, "include_upper": false, "boost": 1.0}}}, {"term": {"kylas_text_1": {"value": "mumbai pune highway", "boost": 1.0}}}, {"nested": {"query": {"terms": {"kylas_MULTI_PICKLIST_2.id": [45, 98], "boost": 1.0}}, "path": "kylas_MULTI_PICKLIST_2", "ignore_unmapped": false, "score_mode": "sum", "boost": 1.0}}, {"terms": {"updatedBy": [1, 2], "boost": 1.0}}, {"terms": {"createdBy": [1, 2], "boost": 1.0}}, {"terms": {"ownerId": [1, 2], "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}, "_source": {"includes": ["firstName", "lastName", "ownerId", "pipelineStage", "companyName", "phoneNumbers", "emails", "id", "recordActions", "customFieldValues", "kylas_text_1", "kylas_MULTI_PICKLIST_2", "kylas_PICK_LIST_2", "id", "ownerId", "convertedBy"], "excludes": []}, "sort": [{"kylas_text_1": {"order": "desc", "missing": "_last"}}]}