{"from": 0, "size": 1000, "query": {"bool": {"must": [{"nested": {"query": {"terms": {"emails.value": ["<EMAIL>", "<EMAIL>"], "boost": 1.0}}, "path": "emails", "ignore_unmapped": false, "score_mode": "sum", "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}, "_source": {"includes": ["firstName", "lastName", "emails", "id", "ownerId"], "excludes": []}, "sort": [{"updatedAt": {"order": "desc", "missing": "_last"}}]}