{"from": 0, "size": 100, "query": {"bool": {"must": [{"bool": {"must": [{"nested": {"query": {"term": {"myMulti.id": {"value": 299, "boost": 1.0}}}, "path": "myMulti", "ignore_unmapped": false, "score_mode": "sum", "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}, "_source": {"includes": ["myMulti", "id", "ownerId"], "excludes": []}, "sort": [{"kylas_text_1": {"order": "desc", "missing": "_last"}}]}