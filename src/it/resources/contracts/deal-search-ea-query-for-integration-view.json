{"from": 0, "size": 100, "query": {"match_all": {"boost": 1.0}}, "_source": {"includes": ["id", "products", "myMulti", "campaign", "source", "forecastingType", "pipelineStage", "pipeline", "associatedContacts", "ownedBy", "company", "actualClosureDate", "estimatedClosureOn", "actualValue", "estimatedValue", "name", "updatedAt", "currency", "customFieldValues", "kylas_MULTI_PICKLIST_2", "kylas_text_1", "id", "ownerId"], "excludes": []}, "sort": [{"name": {"order": "desc", "missing": "_last"}}]}