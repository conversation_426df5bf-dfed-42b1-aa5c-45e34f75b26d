{"from": 0, "size": 100, "query": {"bool": {"must": [{"bool": {"must": [{"term": {"name": {"value": "first", "boost": 1.0}}}, {"term": {"id": {"value": 259, "boost": 1.0}}}, {"nested": {"query": {"term": {"multiPicklist.id": {"value": 99, "boost": 1.0}}}, "path": "multiPicklist", "ignore_unmapped": false, "score_mode": "sum", "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}, "_source": {"includes": ["name", "multiPicklist", "id", "ownerId"], "excludes": []}, "sort": [{"kylas_text_1": {"order": "desc", "missing": "_last"}}]}