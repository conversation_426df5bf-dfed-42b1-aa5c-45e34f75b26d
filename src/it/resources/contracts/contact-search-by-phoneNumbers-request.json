{"from": 0, "size": 1000, "query": {"bool": {"must": [{"nested": {"query": {"terms": {"phoneNumbers.value": ["9999999999", "8888888888"], "boost": 1.0}}, "path": "phoneNumbers", "ignore_unmapped": false, "score_mode": "sum", "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}, "_source": {"includes": ["firstName", "lastName", "phoneNumbers", "ownerId", "id", "ownerId"], "excludes": []}, "sort": [{"updatedAt": {"order": "desc", "missing": "_last"}}]}