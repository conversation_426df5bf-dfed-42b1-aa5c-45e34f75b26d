{"LEAD": [{"name": "All leads", "description": "All leads accessible", "rules": null}, {"name": "All my leads", "description": "All leads owned by me", "rules": [{"id": "ownerId", "field": "ownerId", "type": "long", "input": "long", "operator": "equal", "value": ""}]}, {"name": "Shared with me", "description": "All leads shared with me", "rules": [{"id": "ownerId", "field": "ownerId", "type": "long", "input": "long", "operator": "not_equal", "value": ""}]}, {"name": "All converted leads", "description": "All converted leads", "rules": [{"id": "convertedBy", "field": "convertedBy", "type": "long", "input": "long", "operator": "is_not_null"}]}, {"name": "Open leads", "description": "Leads with Open status", "rules": [{"id": "forecastingType", "field": "forecastingType", "type": "string", "input": "string", "operator": "equal", "value": "OPEN"}]}, {"name": "Closed Won leads", "description": "Leads with Closed Won status", "rules": [{"id": "forecastingType", "field": "forecastingType", "type": "string", "input": "string", "operator": "equal", "value": "CLOSED_WON"}]}, {"name": "Closed Lost leads", "description": "Leads with Closed Lost status", "rules": [{"id": "forecastingType", "field": "forecastingType", "type": "string", "input": "string", "operator": "equal", "value": "CLOSED_LOST"}]}, {"name": "Closed Unqualified leads", "description": "Leads with Closed Unqualified status", "rules": [{"id": "forecastingType", "field": "forecastingType", "type": "string", "input": "string", "operator": "equal", "value": "CLOSED_UNQUALIFIED"}]}, {"name": "New Leads", "description": "Newly created leads", "rules": [{"id": "isNew", "field": "isNew", "type": "boolean", "input": "string", "operator": "equal", "value": true}]}, {"name": "Leads that need attention", "description": "Leads that need attention", "rules": [{"id": "latestActivityCreatedAt", "field": "latestActivityCreatedAt", "type": "date", "input": null, "operator": "less", "value": "lastM<PERSON>h"}, {"id": "forecastingType", "field": "forecastingType", "type": "string", "input": null, "operator": "equal", "value": "OPEN"}, {"id": "convertedAt", "field": "convertedAt", "type": "date", "input": null, "operator": "is_null", "value": null}]}, {"name": "Leads with pending activities", "description": "Leads with pending activities", "rules": [{"id": "taskDueOn", "field": "taskDueOn", "type": "date", "input": null, "operator": "is_not_null", "value": null}, {"id": "forecastingType", "field": "forecastingType", "type": "string", "input": null, "operator": "equal", "value": "OPEN"}]}, {"name": "Leads with no planned activities", "description": "Leads with no planned activities", "rules": [{"id": "taskDueOn", "operator": "is_null", "value": null, "field": "taskDueOn", "type": "date"}, {"id": "meetingScheduledOn", "operator": "is_null", "value": null, "field": "meetingScheduledOn", "type": "date"}, {"id": "forecastingType", "operator": "equal", "value": "OPEN", "field": "forecastingType", "type": "string"}, {"id": "convertedAt", "operator": "is_null", "value": null, "field": "convertedAt", "type": "date"}]}], "CONTACT": [{"name": "All contacts", "description": "All contacts accessible", "rules": null}, {"name": "Stakeholder contacts", "description": "Stakeholders contacts", "rules": [{"id": "stakeholder", "field": "stakeholder", "type": "boolean", "input": "boolean", "operator": "equal", "value": true}]}, {"name": "Marketing contacts", "description": "All contacts from Marketing", "rules": [{"id": "department", "field": "department", "type": "string", "input": "string", "operator": "equal", "value": "Marketing"}]}, {"name": "Sales contacts", "description": "All contacts from Sales", "rules": [{"id": "department", "field": "department", "type": "string", "input": "string", "operator": "equal", "value": "Sales"}]}, {"name": "IT contacts", "description": "All contacts from IT", "rules": [{"id": "department", "field": "department", "type": "string", "input": "string", "operator": "equal", "value": "IT"}]}], "DEAL": [{"name": "All Deals", "description": "All deals accessible", "rules": null}, {"name": "All my deals", "description": "All deals owned by me", "rules": [{"id": "ownedBy", "field": "ownedBy", "type": "long", "input": "long", "operator": "equal", "value": ""}]}, {"name": "Shared with me", "description": "All deals shared with me", "rules": [{"id": "ownedBy", "field": "ownedBy", "type": "long", "input": "long", "operator": "not_equal", "value": ""}]}, {"name": "Open deals", "description": "Deals with Open status", "rules": [{"id": "forecastingType", "field": "forecastingType", "type": "string", "input": "string", "operator": "equal", "value": "OPEN"}]}, {"name": "Closed Won deals", "description": "Deals with Closed Won status", "rules": [{"id": "forecastingType", "field": "forecastingType", "type": "string", "input": "string", "operator": "equal", "value": "CLOSED_WON"}]}, {"name": "Closed Lost deals", "description": "Deals with Closed Lost status", "rules": [{"id": "forecastingType", "field": "forecastingType", "type": "string", "input": "string", "operator": "equal", "value": "CLOSED_LOST"}]}, {"name": "Closed Unqualified deals", "description": "Deals with Closed Unqualified status", "rules": [{"id": "forecastingType", "field": "forecastingType", "type": "string", "input": "string", "operator": "equal", "value": "CLOSED_UNQUALIFIED"}]}, {"name": "New Deals", "description": "Newly created deals", "rules": [{"id": "isNew", "field": "isNew", "type": "boolean", "input": null, "operator": "equal", "value": true}]}, {"name": "Deals that need attention", "description": "Deals that need attention", "rules": [{"id": "latestActivityCreatedAt", "field": "latestActivityCreatedAt", "type": "date", "input": null, "operator": "less", "value": "lastM<PERSON>h"}, {"id": "forecastingType", "field": "forecastingType", "type": "string", "input": null, "operator": "equal", "value": "OPEN"}]}, {"name": "Deals with pending activities", "description": "Deals with pending activities", "rules": [{"id": "taskDueOn", "field": "taskDueOn", "type": "date", "input": null, "operator": "is_not_null", "value": null}, {"id": "forecastingType", "field": "forecastingType", "type": "string", "input": null, "operator": "equal", "value": "OPEN"}]}, {"name": "Deals with no planned activities", "description": "Deals with no planned activities", "rules": [{"id": "taskDueOn", "field": "taskDueOn", "type": "date", "input": null, "operator": "is_null", "value": null}, {"id": "meetingScheduledOn", "field": "meetingScheduledOn", "type": "date", "input": null, "operator": "is_null", "value": null}, {"id": "forecastingType", "field": "forecastingType", "type": "string", "input": null, "operator": "equal", "value": "OPEN"}]}], "EMAIL": [{"name": "Inbox", "description": "All my incoming Emails", "rules": [{"id": "direction", "field": "direction", "type": "string", "input": "string", "operator": "equal", "value": "received"}, {"id": "user", "field": "user", "type": "long", "input": "long", "operator": "equal", "value": ""}]}, {"name": "<PERSON><PERSON>", "description": "All my sent Emails", "rules": [{"id": "direction", "field": "direction", "type": "string", "input": "string", "operator": "equal", "value": "sent"}, {"id": "user", "field": "user", "type": "long", "input": "long", "operator": "equal", "value": ""}]}, {"name": "All My Emails", "description": "All emails owned by me in last 30 days", "rules": [{"id": "user", "field": "user", "type": "long", "input": "long", "operator": "equal", "value": ""}, {"id": "createdAt", "field": "createdAt", "type": "date", "operator": "last_thirty_days", "timeZone": ""}]}, {"name": "All", "description": "All accessible email in last 30 days", "rules": [{"id": "createdAt", "field": "createdAt", "type": "date", "operator": "last_thirty_days", "timeZone": ""}]}, {"name": "Today's Emails", "description": "Incoming emails received Today", "rules": [{"id": "direction", "field": "direction", "type": "string", "input": "string", "operator": "equal", "value": "received"}, {"id": "createdAt", "field": "createdAt", "type": "date", "operator": "today", "timeZone": ""}]}]}