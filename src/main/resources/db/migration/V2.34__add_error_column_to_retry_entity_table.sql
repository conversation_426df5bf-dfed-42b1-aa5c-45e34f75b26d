ALTER TABLE retry_entity
ADD COLUMN IF NOT EXISTS event_name VA<PERSON><PERSON><PERSON>(256),
ADD COLUMN IF NOT EXISTS last_retry_at TIMESTAMP WITHOUT TIME ZONE,
ADD COLUMN IF NOT EXISTS error_message text;

CREATE INDEX IF NOT EXISTS retryEntity_entityTypeEntityIdEntityAction on retry_entity (entity_type, entity_id, entity_action);

UPDATE retry_entity
SET event_name =
  CASE
    WHEN entity_type = 'lead' AND entity_action = 'UPDATE' THEN 'sales.lead.updated'
    WHEN entity_type = 'deal' AND entity_action = 'UPDATE' THEN 'deal.updated'
    WHEN entity_type = 'contact' AND entity_action = 'UPDATE' THEN 'sales.contact.updated'
    WHEN entity_type = 'company' AND entity_action = 'UPDATE' THEN 'company.updated'
  END
WHERE event_name IS NULL;