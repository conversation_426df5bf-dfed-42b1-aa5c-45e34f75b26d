package com.sell.search.service.client.iam;

import com.sell.search.service.client.iam.response.SubscriptionDetailsResponse;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@RequestMapping("/v1")
public interface TenantContract {

    @GetMapping(value = "/tenants/subscription-details", produces = MediaType.APPLICATION_JSON_VALUE)
    SubscriptionDetailsResponse getPlanSubscriptionDetail();
}
