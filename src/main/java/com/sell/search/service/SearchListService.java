package com.sell.search.service;

import static com.sell.search.core.domain.PermissionAction.READ_ALL;
import static com.sell.search.core.utils.SecurityUtil.getTenantId;
import static com.sell.search.core.utils.SecurityUtil.getUserId;
import static com.sell.search.exception.SearchErrorCodes.FREEZED_COLUMN_NOT_EXISTS_IN_FILEDS;
import static com.sell.search.exception.SearchErrorCodes.SEARCH_LIST_NOT_EXISTS;
import static com.sell.search.exception.SearchErrorCodes.UNSUPPORTED_OPERATION_ON_SYSTEM_DEFAULT_SEARCHLIST;
import static java.util.stream.Collectors.toList;

import com.sell.search.core.domain.BaseEntity;
import com.sell.search.core.domain.EntityType;
import com.sell.search.core.domain.PermissionAction;
import com.sell.search.core.utils.EntityUtil;
import com.sell.search.core.utils.SecurityUtil;
import com.sell.search.search.core.dto.SearchRequest;
import com.sell.search.search.core.dto.SearchResponse;
import com.sell.search.search.core.querybuilder.model.JsonRule;
import com.sell.search.domain.PreferredSearchList;
import com.sell.search.domain.SearchList;
import com.sell.search.dto.CreateSearchListRequest;
import com.sell.search.dto.PreferredSearchListResponse;
import com.sell.search.dto.SearchListResponse;
import com.sell.search.dto.UpdateSearchListRequest;
import com.sell.search.dto.mapper.SearchListMapper;
import com.sell.search.event.SearchListEventPublisher;
import com.sell.search.exception.SearchErrorCodes;
import com.sell.search.exception.SearchException;
import com.sell.search.mq.event.SearchListUpdatedEvent;
import com.sell.search.repository.PreferredSearchListRepository;
import com.sell.search.repository.SearchListRepository;
import com.sell.search.security.UserFacade;
import com.sell.search.service.client.iam.UserService;
import com.sell.search.service.client.iam.response.UserResponse;
import com.sell.search.service.searchlist.ISearchListService;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import javax.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.domain.Specifications;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

@Service
@Slf4j
public class SearchListService {

  private static final List<EntityType> SUPPORTED_ENTITIES = Arrays.asList(EntityType.LEAD, EntityType.DEAL, EntityType.CONTACT, EntityType.COMPANY,
      EntityType.USER, EntityType.TEAM, EntityType.TASK, EntityType.NOTE, EntityType.PROFILE, EntityType.PIPELINE, EntityType.SHARE_RULE, EntityType.EMAIL);

  @Autowired
  private SearchListMapper searchListMapper;

  @Autowired
  private SearchService searchService;

  @Autowired
  private SearchListRepository searchListRepository;

  @Autowired
  private PreferredSearchListRepository preferredSearchListRepository;

  @Autowired
  private ISearchListService coreSearchListService;

  @Autowired
  private SearchListEventPublisher searchListEventPublisher;

  @Autowired
  private UserFacade userFacade;

  @Autowired
  private UserService userService;

  /**
   * Save {@link SearchList} entity to database
   *
   * @param createSearchListRequest It contains all the required and optional parameters needed for creating new {@link SearchList}
   * @return The {@link SearchList} entity that has been persisted into database
   */
  @Secured("searchList_write")
  public SearchListResponse createSearchList(String entityType, CreateSearchListRequest createSearchListRequest) {
    // validate if searchList is allowed for the entityType
    EntityType searchListEntityType = validateAndGetSearchListEntityType(entityType);

    // validate the search request
    if (createSearchListRequest.getSearchRequest() != null && createSearchListRequest.getSearchRequest().getJsonRule() != null) {
      searchService.validateSearchRequest(entityType, createSearchListRequest.getSearchRequest());
    }

    // validate if searchList name is available
    validateSearchListNameAvailable(searchListEntityType, createSearchListRequest.getName());

    SearchList searchList = searchListMapper.fromCreateSearchListRequest(createSearchListRequest);
    searchList.setEntityType(searchListEntityType);

    return searchListMapper.toSearchListResponse(coreSearchListService.create(searchList));
  }

  /**
   * Fetch paginated list of {@link SearchList} records
   *
   * @param entityType searchList entityType
   * @param pageable {@link Pageable} props
   * @return Paginated list of {@link SearchList} records
   */
  @Secured("searchList_read")
  public Page<SearchListResponse> getAllSearchLists(String entityType, Pageable pageable) {
    EntityType searchListEntityType = validateAndGetSearchListEntityType(entityType);

    return searchListRepository
        .findAll(getAccessibleSearchListSpecification(searchListEntityType, Collections.emptyList()), pageable)
        .map(this::toSearchListResponseWithSort);
  }

  public List<Content> getEntityCountBySmartListId(List<Long> smartListIds, String entity) {
    EntityType searchListEntityType = validateAndGetSearchListEntityType(entity);
    List<SearchListResponse> searchListResponses = searchListRepository
        .findAll(getAccessibleSearchListSpecification(searchListEntityType, smartListIds))
        .stream()
        .map(this::getSearchListResponse).collect(toList());
    return searchService.getEntityCountBySearchList(entity,searchListResponses);

  }
   public List<SearchListResponse> getEmailSearchListsByIds(List<Long> ids){
     return searchListRepository
         .findAll(getAccessibleSearchListSpecification(EntityType.EMAIL, ids))
         .stream()
         .map(this::getSearchListResponse).collect(toList());
   }

  @Secured("searchList_read")
  public SearchListResponse getSearchList(String entityType, Long id) {
    EntityType searchListEntityType = validateAndGetSearchListEntityType(entityType);
    SearchList requiredSearchList = searchListRepository.findOne(
        getAccessibleSearchListSpecification(searchListEntityType, Collections.singletonList(id)));
    if (ObjectUtils.isEmpty(requiredSearchList)) {
      throw new SearchException(SEARCH_LIST_NOT_EXISTS);
    }

    Optional<PreferredSearchList> preferedSearchList = preferredSearchListRepository.findByUserIdAndSearchList_Id(getUserId(), id);
    if (preferedSearchList.isPresent()) {
      PreferredSearchList preferredSearchList = preferedSearchList.get();
      SearchListResponse searchListWithFields = getSearchListResponse(requiredSearchList);
      if(!ObjectUtils.isEmpty(preferredSearchList.getFields())) {
        String field = preferredSearchList.getFields();
        String[] fields = field.split(",");
        searchListWithFields.getSearchRequest().setFields(fields);
      }

      searchListWithFields.setSize(preferredSearchList.getSize() < 1 ? 10 : preferredSearchList.getSize());
      searchListWithFields.setSort(preferredSearchList.getSort() == null ? "updatedAt,desc" : preferredSearchList.getSort());
      searchListWithFields.setView(preferredSearchList.getView());
      searchListWithFields.setFreezedColumn(preferredSearchList.getFreezedColumn());
      searchListWithFields.setPipelineId(preferedSearchList.get().getPipelineId());
      return searchListWithFields;
    }
    return getSearchListResponse(requiredSearchList);
  }

  /**
   * Update {@link SearchList} entity.
   *
   * @param updateSearchListRequest It represents {@link SearchList} params that needs to be updated
   * @return Updated {@link SearchList} entity
   */
  @Secured("searchList_update")
  public SearchListResponse updateSearchList(String entityType, Long searchListId, UpdateSearchListRequest updateSearchListRequest) {

    // Validate if the searchList exists & accessible
    SearchList searchList = fetchValidSearchList(entityType, searchListId);
    if(searchList.isSystemDefault() && searchList.getEntityType().equals(EntityType.EMAIL)){
      throw new SearchException(UNSUPPORTED_OPERATION_ON_SYSTEM_DEFAULT_SEARCHLIST);
    }

    // Validate if new searchListName is available
    if (updateSearchListRequest.getName() != null && !updateSearchListRequest.getName().equals(searchList.getName())) {
      validateSearchListNameAvailable(searchList.getEntityType(), updateSearchListRequest.getName());
    }

    // Validate search request if json rule is present
    if (updateSearchListRequest.getSearchRequest() != null && updateSearchListRequest.getSearchRequest().getJsonRule() != null) {
      searchService.validateSearchRequest(entityType, updateSearchListRequest.getSearchRequest());
    }

    // Merge the new update request
    SearchList searchListToBeUpdated = searchListMapper.fromUpdateSearchListRequest(updateSearchListRequest, searchList);

    SearchListResponse searchListResponse = searchListMapper.toSearchListResponse(coreSearchListService.update(searchListToBeUpdated));
    SearchListUpdatedEvent searchListUpatedEvent = new SearchListUpdatedEvent(getTenantId(), getUserId(),
        searchListResponse.getId(), entityType,
        searchListResponse.getName(), searchListResponse.getDescription());

    searchListEventPublisher.publishSearchListUpdated(searchListUpatedEvent);
    return searchListResponse;
  }

  public void deleteSearchList(String entityType, Long searchListId) {
    boolean hasDeleteAllOnSearchList = SecurityUtil.hasPermissionAction("searchList", PermissionAction.DELETE_ALL);
    boolean hasDeleteOnSearchList = SecurityUtil.hasPermissionAction("searchList", PermissionAction.DELETE);

    if(!hasDeleteOnSearchList && !hasDeleteAllOnSearchList){
      throw new SearchException(SearchErrorCodes.COMMON_PERMISSION_ERROR);
    }

    Optional<SearchList> searchList = hasDeleteAllOnSearchList ?
        searchListRepository.findByIdAndEntityTypeAndTenantId(searchListId, EntityType.valueOf(entityType), getTenantId())
        : searchListRepository
            .findByIdAndEntityTypeAndTenantIdAndOwnerId(searchListId, EntityType.valueOf(entityType), getTenantId(), getUserId());

    searchList.map(searchListToDelete -> {
      if(searchListToDelete.getEntityType().equals(EntityType.EMAIL) && searchListToDelete.isSystemDefault()){
        throw new SearchException(UNSUPPORTED_OPERATION_ON_SYSTEM_DEFAULT_SEARCHLIST);
      }
      preferredSearchListRepository.deleteBySearchListId(searchListToDelete.getId());
      searchListRepository.deleteById(searchListToDelete.getId());
      return searchListToDelete;
    }).orElseThrow(() -> new SearchException(SEARCH_LIST_NOT_EXISTS));
  }

  private SearchList fetchValidSearchList(String entityType, Long searchListId) {
    EntityType searchListEntityType = validateAndGetSearchListEntityType(entityType);

    SearchList searchList = searchListRepository.customFindOne(sharedRecordSpecification -> Specifications.where(sharedRecordSpecification)
        .and((root, query, cb) -> cb.and(cb.equal(root.get("entityType"), searchListEntityType))), searchListId);

    if (ObjectUtils.isEmpty(searchList)) {
      throw new SearchException(SEARCH_LIST_NOT_EXISTS);
    }
    return searchList;
  }

  public ResponseEntity<PreferredSearchListResponse> savePreferredSearchList(String entityType, Long searchListId, String[] fields, int size,
      String sort, String view, Long pipelineId, String freezedColumn) {
    log.debug("selected default preferred search list for deal with id {}", searchListId);
    if("kanban".equalsIgnoreCase(view) && pipelineId == null){
      throw new SearchException(SearchErrorCodes.INVALID_PIPELINE_FOR_KANBAN_VIEW);
    }
    if(org.apache.commons.lang3.ObjectUtils.isNotEmpty(view) && !"kanban".equalsIgnoreCase(view) && !"list".equalsIgnoreCase(view)){
      throw new SearchException(SearchErrorCodes.INVALID_PREFERRED_SEARCH_VIEW);
    }
    EntityType type = validateAndGetSearchListEntityType(entityType);
    Specification<SearchList> accessibleSpecification = getAccessibleSearchListSpecification(type, Collections.singletonList(searchListId));
    Specification<SearchList> idSpecification = (root, query, cb) -> cb.equal(root.get("id"), searchListId);
    SearchList searchList = searchListRepository
        .findOne(Specifications.where(accessibleSpecification).and(idSpecification));
    if (searchList == null) {
      throw new SearchException(SEARCH_LIST_NOT_EXISTS);
    }

    Long tenantId = getTenantId();
    Long userId = getUserId();

    List<PreferredSearchList> preferredSearchLists = preferredSearchListRepository
        .findByUserIdAndEntityTypeAndTenantId(userId, type, tenantId)
        .stream()
        .map(preferredSearchList -> preferredSearchList.withPreferred(false))
        .collect(toList());

    PreferredSearchList currentPreferred = preferredSearchLists.stream()
        .filter(list -> searchListId.equals(list.getSearchList().getId()))
        .findFirst()
        .map(selectedList -> selectedList.markPreferredWithFields(fields))
        .orElseGet(() -> {
          SearchListResponse searchListResponse = searchListMapper.toSearchListResponse(searchList);
          if(!ObjectUtils.isEmpty(searchListResponse.getSearchRequest().getFields())) {
            String fieldString = String.join(",", searchListResponse.getSearchRequest().getFields());
            return PreferredSearchList.create(userId, type, tenantId, searchList, fieldString);
          }
          return PreferredSearchList.create(userId, type, tenantId, searchList, null);
        });
    if (size > 0) {
      currentPreferred.setSize(size);
    }
    if (sort != null) {
      currentPreferred.setSort(sort);
    }
    if(org.apache.commons.lang3.ObjectUtils.isNotEmpty(view)){
      currentPreferred.setView(view.toUpperCase());
    }
    if(org.apache.commons.lang3.ObjectUtils.isNotEmpty(pipelineId)){
      currentPreferred.setPipelineId(pipelineId);
    }
    updateFreezedColumn(currentPreferred, freezedColumn);
    preferredSearchLists.removeIf(preferred -> preferred.getId().equals(currentPreferred.getId()));
    preferredSearchLists.add(currentPreferred);

    return preferredSearchListRepository.save(preferredSearchLists)
        .stream()
        .filter(PreferredSearchList::isPreferred)
        .findFirst()
        .map(preferred -> ResponseEntity.ok(
            getSearchPreferredListResponse(preferred.getSearchList(), preferred.getFields(), preferred.getSize(), preferred.getSort(), preferred.getView(), preferred.getPipelineId(), preferred.getFreezedColumn())))
        .orElseThrow(() -> new SearchException(SEARCH_LIST_NOT_EXISTS));
  }

  private void updateFreezedColumn(PreferredSearchList currentPreferred, String freezedColumn) {
    if (freezedColumn != null && currentPreferred.getFields() != null){
      String[] fieldsArray = currentPreferred.getFields().split(",");
      boolean isFreezedColumnPresent = Arrays.stream(fieldsArray).anyMatch(str -> str.equals(freezedColumn));
      if (!isFreezedColumnPresent){
        throw new SearchException(FREEZED_COLUMN_NOT_EXISTS_IN_FILEDS);
      }
      currentPreferred.setFreezedColumn(freezedColumn);
    }
    currentPreferred.setFreezedColumn(freezedColumn);
  }


  public ResponseEntity<PreferredSearchListResponse> getPreferredSearchList(String type) {
    log.debug("Inside method getPreferredSearchList");
    EntityType entityType = validateAndGetSearchListEntityType(type);
    ResponseEntity<PreferredSearchListResponse> entity = preferredSearchListRepository
        .findByUserIdAndEntityTypeAndTenantIdAndPreferredTrue(getUserId(), entityType, getTenantId())
        .map(preferred -> ResponseEntity.ok(
            getSearchPreferredListResponse(preferred.getSearchList(), preferred.getFields(), preferred.getSize(), preferred.getSort(), preferred.getView(), preferred.getPipelineId(),
                preferred.getFreezedColumn())))
        .orElse(ResponseEntity.ok().build());

    if (!entity.hasBody()) {
      log.info("There is no Preferred SearchList selected");
      return saveDefaultPreferredListIfNotSelected(type);
    }
    return entity;
  }

  private ResponseEntity<PreferredSearchListResponse> saveDefaultPreferredListIfNotSelected(String type) {
    log.info("Selecting default preferred search list based on entity type");
    EntityType entityType = validateAndGetSearchListEntityType(type);
    Optional<SearchList> searchList = Optional.empty();
    switch (entityType) {
      case LEAD:
        searchList = searchListRepository.findByNameAndTenantId("All leads", getTenantId());
        break;
      case CONTACT:
        searchList = searchListRepository.findByNameAndTenantId("All contacts", getTenantId());
        break;
      case DEAL:
        log.info("Selecting default preferred search list for deal");
        searchList = searchListRepository.findByNameAndTenantId("All Deals", getTenantId());
        log.debug("selected default preferred search list for deal {}", searchList);
        break;
      case COMPANY:
        searchList = searchListRepository.findByNameAndTenantId("All Companies",getTenantId());
        break;
      case EMAIL:
        searchList = searchListRepository.findByNameAndTenantId("Inbox", getTenantId());
        break;
    }
    log.debug("selected default preferred search list for deal after switch {}", searchList);
    return savePreferredSearchList(type, searchList.map(BaseEntity::getId).orElse(null), new String[]{}, 10, null, "LIST", null, null);
  }

  private PreferredSearchListResponse getSearchPreferredListResponse(SearchList searchList, String fields, int size, String sort,
      String view, Long pipelineId, String freezedColumn) {
    SearchListResponse searchListResponse = getSearchListResponse(searchList);
    String[] preferredFields = fields != null ? fields.split(",") : new String[]{};
    PreferredSearchListResponse preferredSearchListResponse = new PreferredSearchListResponse(searchListResponse.getId(),
        searchListResponse.getEntityType(), searchListResponse.getName(),
        searchListResponse.getDescription(), searchListResponse.getSearchRequest(), preferredFields, size, sort, view, pipelineId, freezedColumn);
    preferredSearchListResponse.setSystemDefault(searchListResponse.isSystemDefault());
    preferredSearchListResponse.setRecordActions(searchListResponse.getRecordActions());
    return preferredSearchListResponse;
  }

  private Specification<SearchList> getAccessibleSearchListSpecification(EntityType entityType, List<Long> searchListIds) {
    return (root, query, builder) -> {
      boolean canReadAllList = SecurityUtil.hasPermissionAction("searchList", READ_ALL);
      Predicate ownedBy = builder.equal(root.get("ownerId"), SecurityUtil.getUserId());
      Predicate isSystemDefault = builder.equal(root.get("systemDefault"), true);
      Predicate ownedByOrSystemDefault = builder.or(ownedBy, isSystemDefault);
      Predicate belongsToTenantAndEntityType = builder.and(
          builder.equal(root.get("tenantId"), SecurityUtil.getTenantId()),
          builder.equal(root.get("entityType"), entityType)
      );
      Predicate idPredicate = builder.and(
          root.get("id").in(searchListIds),
          belongsToTenantAndEntityType);
      if (!searchListIds.isEmpty() && canReadAllList) {
        return builder.and(idPredicate, belongsToTenantAndEntityType);
      }
      if (!searchListIds.isEmpty()) {
        return builder.and(idPredicate, belongsToTenantAndEntityType, builder.or(ownedBy, isSystemDefault));
      }
      if (!canReadAllList) {
        return builder.and(belongsToTenantAndEntityType, ownedByOrSystemDefault);
      }
      return belongsToTenantAndEntityType;
    };
  }

  private Specification<SearchList> getAccessibleSearchListForSearchSpecification(EntityType entityType,
      SearchRequest searchRequest) {
    return (root, query, builder) -> {
      boolean canReadAllList = SecurityUtil.hasPermissionAction("searchList", READ_ALL);
      Predicate ownedBy = builder.equal(root.get("ownerId"), SecurityUtil.getUserId());
      Predicate isSystemDefault = builder.equal(root.get("systemDefault"), true);
      Predicate ownedByOrSystemDefault = builder.or(ownedBy, isSystemDefault);
      Predicate belongsToTenantAndEntityType = builder.and(
          builder.equal(root.get("tenantId"), SecurityUtil.getTenantId()),
          builder.equal(root.get("entityType"), entityType)
      );
      if (searchRequest.getJsonRule() != null) {
        Object value = searchRequest.getJsonRule().getRules().get(0).getValue();
        Predicate nameLike = builder.like(
            builder.lower(root.get("name")),
            "%" + value.toString().toLowerCase() + "%"
        );
        Predicate descriptionLike = builder.like(
            builder.lower(root.get("description")),
            "%" + value.toString().toLowerCase() + "%"
        );
        Predicate nameOrDescriptionLike = builder.or(nameLike, descriptionLike);
        if (!canReadAllList) {
          Predicate systemDefaultSearchLists = builder.and(belongsToTenantAndEntityType, ownedByOrSystemDefault);
          return builder.and(systemDefaultSearchLists, nameOrDescriptionLike);
        }
        return builder.and(belongsToTenantAndEntityType, nameOrDescriptionLike);
      }
      if (!canReadAllList) {
        Predicate systemDefaultSearchLists = builder.and(belongsToTenantAndEntityType, ownedByOrSystemDefault);
        return builder.and(systemDefaultSearchLists);
      }
      return builder.and(belongsToTenantAndEntityType);
    };
  }

  private void validateSearchListNameAvailable(EntityType entityType, String searchListName) {
    List<SearchList> searchLists = searchListRepository.customFindAll(sharedRecordSpecification -> Specifications.where(sharedRecordSpecification)
        .and((root, query, cb) -> cb.and(cb.equal(root.get("entityType"), entityType), cb.equal(root.get("name"), searchListName))));

    if (!ObjectUtils.isEmpty(searchLists)) {
      throw new SearchException(SearchErrorCodes.SEARCH_LIST_ALREADY_EXISTS, searchListName);
    }
  }

  private EntityType validateAndGetSearchListEntityType(String entityType) {
    EntityType searchListEntityType = EntityUtil.getEntityType(entityType);

    if (!SUPPORTED_ENTITIES.contains(searchListEntityType)) {
      throw new SearchException(SearchErrorCodes.INVALID_ENTITY_TYPE, searchListEntityType.getLabel());
    }

    return searchListEntityType;
  }

  public SearchResponse<SearchList> getResults(Pageable pageable, SearchRequest searchRequest) {
    return coreSearchListService.getResults(pageable, searchRequest);
  }

  private SearchListResponse getSearchListResponse(SearchList searchList) {
    Long userId = getUserId();
    SearchListResponse searchListResponse = searchListMapper.toSearchListResponse(
        searchList.withRecordActions(userId, SecurityUtil.getUserPermissionAction("searchList"))
    );

    if (searchListResponse.isSystemDefault() && searchListResponse.getSearchRequest() != null) {
      if (searchListResponse.getSearchRequest().getJsonRule() == null) {
        return searchListResponse;
      }

      List<JsonRule> rules = searchListResponse.getSearchRequest().getJsonRule().getRules();
      List<JsonRule> updatedRule = rules.stream().map(rule -> {
        if (Arrays.asList("ownerId", "ownedBy", "user").contains(rule.getField()) && (rule.getValue() == null || rule.getValue().equals(""))) {
          rule.setValue(userId);
        }
        if ((rule.getType().equals("date") && ObjectUtils.isEmpty(rule.getValue()) && ObjectUtils.isEmpty(rule.getTimeZone())) ) {
          UserResponse user = userService.getUser(userId);
          rule.setTimeZone(user.getTimezone());
        }
        if (Objects.equals("latestActivityCreatedAt", rule.getField()) && (rule.getValue() == null || rule.getValue().equals("lastMonth"))) {
          Date currentDate = new DateTime().minusMonths(1).toDate();
          TimeZone tz = TimeZone.getTimeZone("UTC");
          DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm'Z'");
          df.setTimeZone(tz);
          String nowAsISO = df.format(currentDate);
          rule.setValue(nowAsISO);
        }
        return rule;
      }).collect(toList());
      searchListResponse.getSearchRequest().getJsonRule().setRules(updatedRule);
    }
    return searchListResponse;
  }

  public SearchResponse<SearchList> lookup(String fieldAndValue, Pageable pageable) {
    return coreSearchListService.lookup(fieldAndValue, pageable);
  }

  public SearchListResponse toSearchListResponseWithSort(SearchList searchList) {
    SearchListResponse searchListResponse = getSearchListResponse(searchList);
    Optional<PreferredSearchList> preferredSearchList =
        preferredSearchListRepository.findByUserIdAndSearchList_Id(getUserId(),
            searchListResponse.getId());
    if (preferredSearchList.isPresent()) {
      PreferredSearchList preferredList = preferredSearchList.get();
      String sort = preferredList.getSort() == null ? "updatedAt,desc" : preferredList.getSort();
      int size = preferredList.getSize() == 0 ? 10 : preferredList.getSize();
      searchListResponse.setSort(sort);
      searchListResponse.setSize(size);
      searchListResponse.setFreezedColumn(preferredList.getFreezedColumn());
      searchListResponse.setView(preferredList.getView());
      searchListResponse.setPipelineId(preferredList.getPipelineId());
      return searchListResponse;
    }

    return searchListResponse;
  }

  public Page<SearchListResponse> fetchSearchListsContainingTheFieldValue(String entityType, SearchRequest searchRequest, Pageable pageable) {
    EntityType searchListEntityType = validateAndGetSearchListEntityType(entityType);
    return searchListRepository
        .findAll(getAccessibleSearchListForSearchSpecification(searchListEntityType, searchRequest), pageable)
        .map(this::toSearchListResponseWithSort);

  }
}
