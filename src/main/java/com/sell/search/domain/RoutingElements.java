package com.sell.search.domain;

import com.sell.search.exception.SearchErrorCodes;
import com.sell.search.exception.SearchException;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public class RoutingElements {

  private final String resourceName;
  private final String action;

  private RoutingElements(String resourceName, String action) {
    this.resourceName = resourceName;
    this.action = action;
  }

  public static RoutingElements valueOf(String eventName) {

    if (StringUtils.isBlank(eventName)) {
      throw new SearchException(SearchErrorCodes.INVALID_INDEX_EVENT_NAME, eventName);
    }

    String[] split = eventName.split("\\.");

    if (split.length == 2) {
      return new RoutingElements(split[0], split[1]);
    }

    if (split.length == 3) {
      if(split[0].equals("deal") && split[1].equals("metainfo")) {
        return new RoutingElements(split[0], split[2]);
      }
      return new RoutingElements(split[1], split[2]);
    }
    if (split.length == 4) {
      return new RoutingElements(split[1], split[3]);
    }

    throw new SearchException(SearchErrorCodes.INVALID_INDEX_EVENT_NAME, eventName);
  }
}
