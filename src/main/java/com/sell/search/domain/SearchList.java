package com.sell.search.domain;

import com.sell.search.core.annotation.AccessPermission;
import com.sell.search.core.constants.InputSize;
import com.sell.search.core.domain.Action;
import com.sell.search.core.domain.EntityType;
import com.sell.search.core.domain.TenantOwnerAwareBaseEntity;
import com.sell.search.entity.core.annotation.Eventable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;
import org.hibernate.validator.constraints.NotEmpty;

@Entity
@Table
@Where(clause = "deleted=false")
@Getter
@Setter
@AccessPermission(value = "searchList")
@Eventable
public class SearchList extends TenantOwnerAwareBaseEntity {
  // TODO: uniqueness should be by Name for a owner, however would have to handle it when its visible to public.

  @NotNull
  @Enumerated(value = EnumType.STRING)
  private EntityType entityType;

  @NotEmpty
  private String name;

  @Column(length = InputSize.MAX_INPUT_SIZE_DESCRIPTION)
  private String description;

  @Column(columnDefinition = "TEXT")
  @NotEmpty
  private String searchRequest;

  private boolean systemDefault;

  public SearchList withRecordActions(Long userId , Action userAction) {
    Action action = new Action();
    if (userAction.isReadAll() || (userAction.isRead() && (getOwnerId().equals(userId) || systemDefault))) {
      action.setRead(true);
    }
    if (userAction.isUpdateAll()
        || (userAction.isUpdate() && (getOwnerId().equals(userId)))) {
      action.setUpdate(true);
    }
    if (userAction.isDeleteAll()
        || (userAction.isDelete() && (getOwnerId().equals(userId)))) {
      action.setDelete(true);
    }
    if ((userAction.isRead() && systemDefault) || ((getOwnerId().equals(userId) && userAction.isRead() && !systemDefault))) {
      action.setDelete(true);
    }
    this.setRecordActions(action);
    return this;
  }
}
