package com.sell.search.domain;

import static org.apache.commons.lang3.ArrayUtils.isEmpty;

import com.sell.search.core.domain.EntityType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToOne;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PreferredSearchList {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @NotNull
  private Long userId;

  @NotNull
  @Enumerated(value = EnumType.STRING)
  private EntityType entityType;

  @OneToOne
  private SearchList searchList;

  @NotNull
  private Long tenantId;

  @Column(columnDefinition = "TEXT")
  private String fields;

  @NotNull
  private boolean preferred;

  private int size;

  private String sort;

  private String view = "LIST";

  private Long pipelineId;

  private String freezedColumn;

  public PreferredSearchList(Long userId, EntityType entityType, SearchList searchList, Long tenantId) {
    this.userId = userId;
    this.entityType = entityType;
    this.searchList = searchList;
    this.tenantId = tenantId;
  }

  public PreferredSearchList(Long userId, EntityType type, Long tenantId, SearchList searchList, String fields, boolean preferred) {
    this.userId = userId;
    this.entityType = type;
    this.searchList = searchList;
    this.tenantId = tenantId;
    this.fields = fields;
    this.preferred = preferred;
    this.sort = "updatedAt,desc";
    this.size = 10;
  }

  public static PreferredSearchList create(Long userId, EntityType type, Long tenantId, SearchList searchList, String fields) {
    return new PreferredSearchList(userId, type, tenantId, searchList, fields, true);
  }

  public PreferredSearchList withPreferred(boolean preferred) {
    return new PreferredSearchList(
        this.id, this.userId, this.entityType, this.searchList, this.tenantId, this.fields, preferred, this.size, this.sort,this.view,this.pipelineId, this.freezedColumn);
  }

  public PreferredSearchList markPreferredWithFields(String[] fields) {
    String fieldsString = isEmpty(fields) ? this.fields : String.join(",", fields);;
    return new PreferredSearchList(
        this.id, this.userId, this.entityType, this.searchList, this.tenantId, fieldsString, true, this.size, this.sort,this.view,this.pipelineId, this.freezedColumn);
  }
}


