package com.sell.search.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.HashMap;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * Created by hemants on 19/02/19. This is base entity for rabbitmq Message. The message should have atleast following mandatory fields
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class BaseEntity extends HashMap<String, Object> {

  @NotEmpty
  public String getId() {
    return ((Integer) this.get("id")).toString();
  }

  @NotNull
  public Integer getVersion() {
    return (Integer) this.get("version");
  }

  @NotEmpty
  public String getTenantId() {
    return ((Integer) this.get("tenantId")).toString();
  }
}
