package com.sell.search.domain;

import com.sell.search.repository.PreferredSearchListRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PreferredSearchListHealthFacade {

  private final PreferredSearchListRepository preferredSearchListRepository;

  @Autowired
  public PreferredSearchListHealthFacade(PreferredSearchListRepository preferredSearchListRepository) {
    this.preferredSearchListRepository = preferredSearchListRepository;
  }

  public PreferredSearchList getSearchListIdByTenantId(Long tenantId) {
      return preferredSearchListRepository.findFirstByTenantId(tenantId);
  }
}
