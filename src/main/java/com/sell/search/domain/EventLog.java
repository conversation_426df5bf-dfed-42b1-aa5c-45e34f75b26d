package com.sell.search.domain;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import com.sell.search.core.domain.Audit;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by hemants on 26/02/19. An entity class for event logs
 * 
 * @deprecated
 */
@Entity
@Setter
@Getter
@Deprecated
public class EventLog extends Audit {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;
  private String name;

  @Enumerated(value = EnumType.STRING)
  private EventStatus status;
  private Integer retryCount;
  private String messageId;
  private String sentBy;

  public EventLog id(Long id) {
    this.id = id;
    return this;
  }

  public EventLog name(String name) {
    this.name = name;
    return this;
  }

  public EventLog status(EventStatus status) {
    this.status = status;
    return this;
  }

  public EventLog retryCount(Integer retryCount) {
    this.retryCount = retryCount;
    return this;
  }

  public EventLog messageId(String messageId) {
    this.messageId = messageId;
    return this;
  }

  public EventLog sentBy(String sentBy) {
    this.sentBy = sentBy;
    return this;
  }
}
