package com.sell.search.domain.retry;

import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Getter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

@Entity
@Table
@Getter
@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
public class RetryEntity implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private Long tenantId;

  private Long entityId;

  private String entityType;

  private String eventName;

  private Long retryCount;

  @Enumerated(value = EnumType.STRING)
  private ActionType entityAction;

  private Date createdAt;

  @Type(type = "jsonb")
  @Column(columnDefinition = "jsonb")
  private Map<String, Object> entityPayload;

  private String errorMessage;

  private Date lastRetryAt;


  public RetryEntity() {
  }

  public RetryEntity(Long tenantId, Long entityId, String entityType, ActionType entityAction, String eventName, Map<String, Object> entityPayload, String errorMessage) {
    this.tenantId = tenantId;
    this.entityId = entityId;
    this.entityType = entityType;
    this.entityAction = entityAction;
    this.eventName = eventName;
    this.entityPayload = entityPayload;
    this.errorMessage = errorMessage;
    this.retryCount = 0L;
    this.createdAt = new Date();
  }

  public static RetryEntity createNew(Long tenantId, String entityType, Long entityId, ActionType entityAction, String eventName, Map<String, Object> payload,
     String errorMessage) {
    return new RetryEntity(tenantId, entityId, entityType, entityAction, eventName, payload, errorMessage);
  }

  public void with(HashMap<String, Object> payload, String errorMessage) {
    this.entityPayload = payload;
    this.errorMessage = errorMessage;
    this.retryCount = 0L;
    this.lastRetryAt = null;
  }

  public RetryEntity updateRetryCount() {
    this.retryCount += 1;
    this.lastRetryAt = new Date();
    return this;
  }

  public RetryEntity withErrorMessage(String errorMessage) {
    this.errorMessage = errorMessage;
    return this;
  }

  public enum ActionType {
    CREATE,
    UPDATE,
    DELETE
  }

}
