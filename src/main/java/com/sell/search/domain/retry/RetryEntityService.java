package com.sell.search.domain.retry;

import com.sell.search.service.EntityLifeCycleListener;
import java.util.HashMap;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RetryEntityService {

  private final RetryEntityRepository retryEntityRepository;
  private final EntityLifeCycleListener entityLifeCycleListener;

  @Autowired
  public RetryEntityService(RetryEntityRepository retryEntityRepository, EntityLifeCycleListener entityLifeCycleListener) {
    this.retryEntityRepository = retryEntityRepository;
    this.entityLifeCycleListener = entityLifeCycleListener;
  }

  public void retryErrorEntities() {
    List<RetryEntity> entitiesForRetry = getEntitiesForRetry();
    log.info("RetryEntityService: Count of entities for retry {}", entitiesForRetry.size());
    entitiesForRetry.forEach(entity -> {
      try {
        entityLifeCycleListener.retryEntityOperation(entity.getEventName(), (HashMap<String, Object>) entity.getEntityPayload());
        log.info("RetryEntityService: Retry successful for {} entity {} operation with entityId: {}, removing entry from retry table",
            entity.getEntityType(), entity.getEntityAction(), entity.getEntityId());
        retryEntityRepository.deleteRetryEntity(entity.getId());
      } catch (Exception e) {
        log.error(
            "RetryEntityService: Exception while retrying {} entity {} operation with entityId: {}, updating retry count, updatedRetryCount: {}, error: {}",
            entity.getEntityType(), entity.getEntityAction(), entity.getEntityId(), entity.getRetryCount() + 1, e.getMessage(), e);
        entity.updateRetryCount().withErrorMessage(e.getMessage());
        retryEntityRepository.saveAndFlush(entity);
      }
    });
  }

  private List<RetryEntity> getEntitiesForRetry() {
    return retryEntityRepository.findAll(
            (r, cq, cb) -> cb.le(r.get("retryCount"), 10),
            new PageRequest(0, 3000, Direction.ASC, "createdAt"))
        .getContent();
  }

}
