package com.sell.search.domain.retry;

import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface RetryEntityRepository extends JpaRepository<RetryEntity, Long>, JpaSpecificationExecutor<RetryEntity> {
  Optional<RetryEntity> findByEntityTypeAndEntityId(String entityType, long entityId);

  @Transactional
  @Modifying(flushAutomatically = true, clearAutomatically = true)
  @Query(value = "DELETE FROM retry_entity where id = :id", nativeQuery = true)
  void deleteRetryEntity(@Param("id") long id);

  @Query("DELETE FROM RetryEntity WHERE entityType=:entityType AND entityId=:entityId")
  @Transactional
  @Modifying
  void deleteByEntityTypeAndEntityId(@Param("entityType") String entityType, @Param("entityId") long entityId);
}
