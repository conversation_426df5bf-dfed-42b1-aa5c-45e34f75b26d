package com.sell.search.domain.retry;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.search.domain.retry.RetryEntity.ActionType;
import com.sell.search.domain.RoutingElements;
import java.util.HashMap;
import java.util.List;
import javax.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.retry.MessageRecoverer;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ErrorMessageRecoveryFacade implements MessageRecoverer {

  private final RetryEntityRepository retryEntityRepository;
  private final ObjectMapper objectMapper;

  public ErrorMessageRecoveryFacade(RetryEntityRepository retryEntityRepository, ObjectMapper objectMapper) {
    this.retryEntityRepository = retryEntityRepository;
    this.objectMapper = objectMapper;
  }

  @Override
  public void recover(Message message, Throwable throwable) {
    String eventName = message.getMessageProperties().getReceivedRoutingKey();
    RoutingElements routingElements = RoutingElements.valueOf(eventName);
    String messageBody = new String(message.getBody());
    try {
      HashMap<String, Object> payload = objectMapper.readValue(messageBody, HashMap.class);
      ActionType entityAction = getActionType(routingElements.getAction());
      String errorMessage = throwable.getMessage();
      if (ObjectUtils.isNotEmpty(throwable.getCause())) {
        errorMessage = throwable.getCause().getMessage();
      }
      persistErrorMessage(payload.getOrDefault("tenantId", null), routingElements.getResourceName().toLowerCase(), payload.getOrDefault("id", null),
          payload, entityAction, eventName, errorMessage);
    } catch (Exception e) {
      log.error("Error while saving {} into database with message payload {}, error: {}", eventName, messageBody, e.getMessage(), e);
    }
  }

  public void deleteAllRetryEntriesForEntity(String entity, Long entityId) {
    log.info("Removing all retry entries for entity: {}, entityId: {}", entity, entityId);
    retryEntityRepository.deleteByEntityTypeAndEntityId(entity.toLowerCase(), entityId);
  }

  private void persistErrorMessage(Object tenantIdObject, String entityType, Object entityIdObject,
      HashMap<String, Object> payload, ActionType entityAction, String eventName, String errorMessage) {
    Long tenantId = ObjectUtils.isEmpty(tenantIdObject) ? null : Long.valueOf(tenantIdObject.toString());
    Long entityId = ObjectUtils.isEmpty(entityIdObject) ? null : Long.valueOf(entityIdObject.toString());
    log.info("Persist error message for retry entity {}, entityId: {}, operation: {}, tenantId: {}, errorMessage: {}",
        entityType, entityId, entityAction, tenantId, errorMessage);

    List<RetryEntity> all = retryEntityRepository.findAll((root, criteriaQuery, criteriaBuilder) -> {
      Predicate entityTypePredicate = criteriaBuilder.equal(root.get("entityType"), entityType);
      Predicate entityIdPredicate = criteriaBuilder.equal(root.get("entityId"), entityId);
      Predicate entityOperation = criteriaBuilder.equal(root.get("entityAction"), entityAction);
      return criteriaBuilder.and(entityTypePredicate, entityIdPredicate, entityOperation);
    });
    if (all.isEmpty()) {
      RetryEntity aNew = RetryEntity.createNew(tenantId, entityType, entityId, entityAction, eventName, payload, errorMessage);
      retryEntityRepository.save(aNew);
      return;
    }
    RetryEntity eventPayloadError = all.get(0);
    if (ActionType.UPDATE.equals(entityAction) && ((Integer) payload.getOrDefault("version", 0) <=
            (Integer) eventPayloadError.getEntityPayload().getOrDefault("version", 0))) {
      log.info("Retry event with same or greater version is already present for retry, ignoring current event, entity: {}, entityId: {}, operation: {}, version: {}, persistedVersion: {}",
          entityType, entityId, entityAction, payload.getOrDefault("version", 0), eventPayloadError.getEntityPayload().getOrDefault("version", 0));
      return;
    }
    eventPayloadError.with(payload, errorMessage);
    retryEntityRepository.save(eventPayloadError);
  }

  private ActionType getActionType(String action) {
    if ("created".equals(action)) {
      return ActionType.CREATE;
    }
    if ("updated".equals(action)) {
      return ActionType.UPDATE;
    }
    if ("deleted".equals(action)) {
      return ActionType.DELETE;
    }
    return null;
  }

}
