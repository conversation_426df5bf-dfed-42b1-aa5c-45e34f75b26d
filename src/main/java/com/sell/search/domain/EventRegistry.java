package com.sell.search.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sell.search.core.annotation.IgnoreOwnerFilter;
import com.sell.search.core.annotation.IgnoreTenantFilter;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

@Entity
@Table
@Getter
@Setter
@NoArgsConstructor
@IgnoreTenantFilter
@IgnoreOwnerFilter
public class EventRegistry {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @JsonIgnore
  private Long id;

  @NotEmpty
  private String serviceName;

  @NotEmpty
  private String eventName;

  private String queueName;

  public EventRegistry(String serviceName, String eventName, String queueName) {
    this.serviceName = serviceName;
    this.eventName = eventName;
    this.queueName = queueName;
  }
}
