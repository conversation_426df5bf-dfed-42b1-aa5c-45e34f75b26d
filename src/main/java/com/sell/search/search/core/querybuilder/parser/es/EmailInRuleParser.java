package com.sell.search.search.core.querybuilder.parser.es;

import com.sell.search.search.core.querybuilder.model.IRule;
import com.sell.search.search.core.querybuilder.model.enums.EnumRuleType;
import com.sell.search.search.core.querybuilder.parser.AbstractEsRuleParser;
import com.sell.search.search.core.querybuilder.parser.JsonRuleParser;
import java.util.List;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.AbstractQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;

public class EmailInRuleParser extends AbstractEsRuleParser {


  public EmailInRuleParser() {
  }

  @Override
  public boolean canParse(IRule rule) {
    return EnumRuleType.EMAILS.getValue().equalsIgnoreCase(rule.getType());
  }

  @Override
  public AbstractQueryBuilder parse(IRule rule, JsonRuleParser parser) {
    List<String> emails = (List<String>) rule.getValue();
    return QueryBuilders.nestedQuery(
        "emails",
        QueryBuilders.termsQuery("emails.value", emails),
        ScoreMode.Total);

  }
}