package com.sell.search.search.core.querybuilder.parser.es;

import com.sell.search.core.domain.EntityType;
import com.sell.search.search.core.querybuilder.model.IRule;
import com.sell.search.search.core.querybuilder.model.enums.EnumOperator;
import com.sell.search.search.core.querybuilder.model.es.Field;
import com.sell.search.search.core.querybuilder.parser.AbstractEsRuleParser;
import com.sell.search.search.core.querybuilder.parser.JsonRuleParser;
import java.util.List;
import java.util.Map;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.common.unit.Fuzziness;
import org.elasticsearch.index.query.AbstractQueryBuilder;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.QueryStringQueryBuilder;
import org.elasticsearch.index.query.WildcardQueryBuilder;

public class MultiFieldRuleParser extends AbstractEsRuleParser {

  private Map<EntityType, List<Field>> entityFieldsToSearch;

  public MultiFieldRuleParser(Map<EntityType, List<Field>> normalFields) {
    this.entityFieldsToSearch = normalFields;
  }

  @Override
  public boolean canParse(IRule rule) {
    return EnumOperator.MULTI_FIELD.getValue().equals(rule.getOperator());
  }

  @Override
  public AbstractQueryBuilder parse(IRule rule, JsonRuleParser parser) {

    String searchCriteria = rule.getValue().toString();
    boolean isQuotedValue = searchCriteria.startsWith("\"") && searchCriteria.endsWith("\"");
    String unQuotedValue = searchCriteria.replaceAll("^\"|\"$", "");

    String queryString = isQuotedValue ?  "*" + unQuotedValue + "*" : rule.getValue().toString();

    QueryStringQueryBuilder nonNestedQueryString = QueryBuilders.queryStringQuery(queryString);
    nonNestedQueryString.fuzziness(Fuzziness.ZERO);

    BoolQueryBuilder bool = QueryBuilders.boolQuery();

    entityFieldsToSearch.get(parser.getEntityType())
        .stream()
        .filter(field -> !field.isNested())
        .forEach(s -> nonNestedQueryString.field(s.getName()));

    bool.should(nonNestedQueryString);

    entityFieldsToSearch.get(parser.getEntityType())
        .stream()
        .filter(field -> field.isNested())
        .forEach(field -> {
            QueryStringQueryBuilder nestedQueryToSearch = QueryBuilders.queryStringQuery(queryString);
            nestedQueryToSearch.fuzziness(Fuzziness.ZERO);
            nestedQueryToSearch.field(String.format("%s.%s", field.getName(), field.getNestedSearchKey()));
            bool.should(QueryBuilders.nestedQuery(field.getName(), nestedQueryToSearch, ScoreMode.Total));
        });

    return bool;

  }
}