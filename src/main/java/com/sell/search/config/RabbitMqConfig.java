package com.sell.search.config;

import com.sell.search.domain.retry.ErrorMessageRecoveryFacade;
import org.springframework.amqp.rabbit.config.RetryInterceptorBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.interceptor.RetryOperationsInterceptor;

@Configuration
public class RabbitMqConfig {
  private final long initialInterval;
  private final double multiplier;
  private final long maxInterval;
  private final int maxAttempt;
  private final ErrorMessageRecoveryFacade errorMessageRecoveryFacade;

  @Autowired
  public RabbitMqConfig(
      @Value("${spring.rabbitmq.retry.initial-interval:2000}") long initialInterval,
      @Value("${spring.rabbitmq.retry.multiplier:2}") double multiplier,
      @Value("${spring.rabbitmq.retry.max-interval:30000}") long maxInterval,
      @Value("${spring.rabbitmq.retry.max-attempt:5}") int maxAttempt,
      ErrorMessageRecoveryFacade errorMessageRecoveryFacade) {
    this.initialInterval = initialInterval;
    this.multiplier = multiplier;
    this.maxInterval = maxInterval;
    this.maxAttempt = maxAttempt;
    this.errorMessageRecoveryFacade = errorMessageRecoveryFacade;
  }

  @Bean
  @Qualifier("retryInterceptor")
  public RetryOperationsInterceptor retryInterceptor() {
    return RetryInterceptorBuilder.stateless()
        .backOffOptions(initialInterval, multiplier, maxInterval)
        .maxAttempts(maxAttempt)
        .recoverer(errorMessageRecoveryFacade)
        .build();
  }
}
