package com.sell.search.config;

import com.sell.search.core.client.InternalShareRuleService;
import com.sell.search.core.domain.EntityType;
import com.sell.search.entity.core.service.DefaultIdNameSearchService;
import com.sell.search.entity.core.service.IdNameResolver;
import com.sell.search.entity.core.service.IdNameSearchService;
import com.sell.search.entity.core.service.client.IEntityClient;
import com.sell.search.global.search.GlobalSearchBaseEntity;
import com.sell.search.global.search.Lead;
import com.sell.search.search.core.config.SearchConfig;
import com.sell.search.search.core.querybuilder.EsQueryBuilderFactory;
import com.sell.search.search.core.querybuilder.builder.EsBuilder;
import com.sell.search.search.core.querybuilder.filter.DefaultValueConvertFilter;
import com.sell.search.search.core.querybuilder.filter.IRuleFilter;
import com.sell.search.search.core.querybuilder.filter.ValidateFilter;
import com.sell.search.search.core.querybuilder.model.es.Field;
import com.sell.search.search.core.querybuilder.parser.IGroupParser;
import com.sell.search.search.core.querybuilder.parser.IRuleParser;
import com.sell.search.search.core.querybuilder.parser.es.DefaultGroupParser;
import com.sell.search.query.builder.AbstractQueryBuilder;
import com.sell.search.query.builder.CompanyQueryBuilder;
import com.sell.search.query.builder.ContactQueryBuilder;
import com.sell.search.query.builder.DealQueryBuilder;
import com.sell.search.query.builder.LeadQueryBuilder;
import com.sell.search.query.builder.QueryOperatorBuilder;
import feign.Feign;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/** Created by hemants on 15/03/19. */
@Configuration
public class ApplicationConfig {

  @Value("${client.entity.basePath}")
  private String clientBasePath;

  @Value("${newElasticsearch.host:localhost}")
  public String newElasticsearchHost;

  @Value("${newElasticsearch.port:9200}")
  public int newElasticsearchPort;

  private final Feign.Builder clientBuilder;

  public ApplicationConfig(Feign.Builder clientBuilder) {
    this.clientBuilder = clientBuilder;
  }

  @Bean
  public InternalShareRuleService getInternalShareRuleService() {
    return new InternalShareRuleService(clientBasePath, clientBuilder);
  }

  @Bean
  public EsQueryBuilderFactory getEsQueryBuilderFactory() {
    Map<EntityType, List<Field>> fullTextMapping = new HashMap<>();
    fullTextMapping.put(
        EntityType.LEAD,
        Arrays.asList(
            new Field("firstName"),
            new Field("lastName"),
            new Field("companyName"),
            new Field("emails").withNested("value"),
            new Field("phoneNumbers").withNested("value")));

    fullTextMapping.put(
        EntityType.CONTACT,
        Arrays.asList(
            new Field("firstName"),
            new Field("lastName"),
            new Field("department"),
            new Field("designation"),
            new Field("emails").withNested("value"),
            new Field("phoneNumbers").withNested("value")));

    return new EsQueryBuilderFactory(fullTextMapping);
  }

  @Bean
  public EsBuilder getEsBuilder() {
    return getEsQueryBuilderFactory().builder();
  }

  @Configuration
  class SearchConfiguration extends SearchConfig {}

  @Bean
  public IdNameSearchService getIdNameSearchService() {
    RestHighLevelClient newEsRestHighLevelClient =
        new RestHighLevelClient(
            RestClient.builder(new HttpHost(newElasticsearchHost, newElasticsearchPort, "http")));
    return new DefaultIdNameSearchService(newEsRestHighLevelClient);
  }


  @Bean
  public IdNameResolver getIdNameResolver(
      IEntityClient entityClient, IdNameSearchService idNameSearchService) {
    return new IdNameResolver(entityClient, idNameSearchService);
  }

  @Bean
  public List<AbstractQueryBuilder> queryBuilders() {

    List<AbstractQueryBuilder> builders = new ArrayList<>();
    List<IRuleFilter> filters = new ArrayList<>();
    filters.add(new ValidateFilter());
    filters.add(new DefaultValueConvertFilter());
    IGroupParser groupParser = new DefaultGroupParser();

    Set<IRuleParser> parsers =
        QueryOperatorBuilder.getParsers("operatorMapping/es-field-operator-mapping.json");

    AbstractQueryBuilder leadQueryBuilder =
        new LeadQueryBuilder(groupParser, new ArrayList(parsers), filters);

    AbstractQueryBuilder contactQueryBuilder =
        new ContactQueryBuilder(groupParser, new ArrayList(parsers), filters);

    AbstractQueryBuilder companyQueryBuilder =
        new CompanyQueryBuilder(groupParser, new ArrayList(parsers), filters);

    AbstractQueryBuilder dealQueryBuilder =
        new DealQueryBuilder(groupParser, new ArrayList(parsers), filters);

    builders.add(companyQueryBuilder);
    builders.add(dealQueryBuilder);
    builders.add(leadQueryBuilder);
    builders.add(contactQueryBuilder);
    return builders;
  }

}
