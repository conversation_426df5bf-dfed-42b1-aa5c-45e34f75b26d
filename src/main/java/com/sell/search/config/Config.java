package com.sell.search.config;

import com.sell.search.core.config.CoreConfig;
import com.sell.search.core.utils.JwtTokenConverter;
import com.sell.search.entity.core.config.CrudEntityConfig;
import org.apache.http.HttpHost;
import org.elasticsearch.client.ElasticsearchClient;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created by hemants on 29/03/19.
 */
@Configuration
public class Config extends CoreConfig {

  @Value("${jwt.secret-key}")
  private String jwtSecretKey;

  @Value("${newElasticsearch.host}") String elasticsearchHost;
  @Value("${newElasticsearch.port}") int elasticsearchPort;

  @Configuration
  public class SalesEntityConfig extends CrudEntityConfig {

  }
  @Bean
  public JwtTokenConverter jwtTokenConverter(){
    return new JwtTokenConverter("sell",jwtSecretKey);
  }

  @Bean
  public RestHighLevelClient restHighLevelClient(){
    return new RestHighLevelClient(
        RestClient.builder(new HttpHost(elasticsearchHost, elasticsearchPort, "http")));

  }
}
