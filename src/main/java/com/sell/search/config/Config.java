package com.sell.search.config;

import com.sell.search.core.config.CoreConfig;
import com.sell.search.core.utils.JwtTokenConverter;
import com.sell.search.entity.core.config.CrudEntityConfig;
import com.sell.search.es.abstraction.ElasticsearchClient;
import com.sell.search.es.abstraction.impl.Elasticsearch6Client;
import com.sell.search.es.abstraction.impl.Elasticsearch8Client;
import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created by hemants on 29/03/19.
 */
@Configuration
public class Config extends CoreConfig {

  @Value("${jwt.secret-key}")
  private String jwtSecretKey;

  @Value("${legacyElasticsearch.host}") String legacyElasticsearchHost;
  @Value("${legacyElasticsearch.port}") int legacyElasticsearchPort;

  @Value("${newElasticsearch.host}") String newElasticsearchHost;
  @Value("${newElasticsearch.port}") int newElasticsearchPort;

  @Configuration
  public class SalesEntityConfig extends CrudEntityConfig {

  }
  @Bean
  public JwtTokenConverter jwtTokenConverter(){
    return new JwtTokenConverter("sell",jwtSecretKey);
  }

  @Bean
  @Qualifier("legacyRestHighLevelClient")
  public RestHighLevelClient legacyRestHighLevelClient(){
    return new RestHighLevelClient(
        RestClient.builder(new HttpHost(legacyElasticsearchHost, legacyElasticsearchPort, "http")));
  }

  @Bean
  @Qualifier("newRestHighLevelClient")
  public RestHighLevelClient newRestHighLevelClient(){
    return new RestHighLevelClient(
        RestClient.builder(new HttpHost(newElasticsearchHost, newElasticsearchPort, "http")));
  }

  @Bean
  @Qualifier("legacyElasticsearchClient")
  public ElasticsearchClient legacyElasticsearchClient(){
    return new Elasticsearch6Client(legacyRestHighLevelClient());
  }

  @Bean
  @Qualifier("newElasticsearchClient")
  public ElasticsearchClient newElasticsearchClient(){
    return new Elasticsearch8Client(newElasticsearchHost, newElasticsearchPort);
  }

  // Keep the original bean for backward compatibility
  @Bean
  public RestHighLevelClient restHighLevelClient(){
    return legacyRestHighLevelClient();
  }
}
