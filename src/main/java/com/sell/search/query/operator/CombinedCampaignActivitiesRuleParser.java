package com.sell.search.query.operator;

import com.sell.search.search.core.querybuilder.model.IRule;
import com.sell.search.search.core.querybuilder.model.JsonRule;
import com.sell.search.search.core.querybuilder.model.enums.EnumOperator;
import com.sell.search.search.core.querybuilder.parser.JsonRuleParser;
import java.util.List;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.AbstractQueryBuilder;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;

public class CombinedCampaignActivitiesRuleParser extends AbstractEsRuleParser {

  public CombinedCampaignActivitiesRuleParser() {
    super(EnumOperator.EQUAL);
  }

  @Override
  public boolean canParse(IRule rule) {
    return "combined_campaign_activities".equals(rule.getOperator())
        && "campaignActivities_with_activities".equals(rule.getField());
  }

  @Override
  public AbstractQueryBuilder parse(IRule rule, JsonRuleParser parser) {
    if (rule.getData() instanceof java.util.Map) {
      java.util.Map<String, JsonRule> data = (java.util.Map<String, JsonRule>) rule.getData();
      JsonRule campaignRule = data.get("campaignActivities");
      JsonRule activitiesRule = data.get("activities");

      if (campaignRule != null && activitiesRule != null) {
        return buildCombinedQuery(campaignRule, activitiesRule);
      }
    }
    throw new IllegalArgumentException("Invalid campaign activity combined rule data");
  }

  private AbstractQueryBuilder buildCombinedQuery(JsonRule campaignRule, JsonRule activitiesRule) {
    BoolQueryBuilder campaignActivitiesInnerQuery = QueryBuilders.boolQuery();
    addCampaignActivitiesFilter(campaignActivitiesInnerQuery, campaignRule);
    addActivitiesFilter(campaignActivitiesInnerQuery, activitiesRule);

    return QueryBuilders.nestedQuery("campaignActivities", campaignActivitiesInnerQuery, ScoreMode.Total);
  }

  private void addCampaignActivitiesFilter(BoolQueryBuilder boolQuery, JsonRule campaignRule) {
    switch (campaignRule.getOperator()) {
      case "equal":
        boolQuery.must(QueryBuilders.termQuery("campaignActivities.id", campaignRule.getValue()));
        break;
      case "not_equal":
        boolQuery.mustNot(QueryBuilders.termQuery("campaignActivities.id", campaignRule.getValue()));
        break;
      case "in":
        List<Object> inValues = (List<Object>) campaignRule.getValue();
        boolQuery.must(QueryBuilders.termsQuery("campaignActivities.id", inValues));
        break;
      case "not_in":
        List<Object> notInValues = (List<Object>) campaignRule.getValue();
        boolQuery.mustNot(QueryBuilders.termsQuery("campaignActivities.id", notInValues));
        break;
    }
  }

  private void addActivitiesFilter(BoolQueryBuilder boolQuery, JsonRule activitiesRule) {
    switch (activitiesRule.getOperator()) {
      case "equal":
        boolQuery.must(
            QueryBuilders.nestedQuery(
                "campaignActivities.activities",
                QueryBuilders.termQuery("campaignActivities.activities.id", activitiesRule.getValue()),
                ScoreMode.Total));
        break;
      case "not_equal":
        boolQuery.mustNot(
            QueryBuilders.nestedQuery(
                "campaignActivities.activities",
                QueryBuilders.termQuery("campaignActivities.activities.id", activitiesRule.getValue()),
                ScoreMode.Total));
        break;
      case "in":
        List<Object> inValues = (List<Object>) activitiesRule.getValue();
        boolQuery.must(
            QueryBuilders.nestedQuery(
                "campaignActivities.activities",
                QueryBuilders.termsQuery("campaignActivities.activities.id", inValues),
                ScoreMode.Total));
        break;
      case "not_in":
        List<Object> notInValues = (List<Object>) activitiesRule.getValue();
        boolQuery.mustNot(
            QueryBuilders.nestedQuery(
                "campaignActivities.activities",
                QueryBuilders.termsQuery("campaignActivities.activities.id", notInValues),
                ScoreMode.Total));
        break;
    }
  }
}
