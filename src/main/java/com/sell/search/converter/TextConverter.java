package com.sell.search.converter;

import com.sell.search.dto.EsFieldType;
import com.sell.search.field.domain.Field;
import com.sell.search.model.FieldType;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TextConverter implements DataTypeConverter {

  private final List<FieldType> valuesApplicableList =
      Arrays.asList(FieldType.SINGLE_LINE_TEXT, FieldType.PARAGRAPH_TEXT, FieldType.RICH_TEXT);

  @Override
  public boolean canConvert(Field field) {
    return valuesApplicableList.contains(field.getFieldType());
  }

  @Override
  public Map<String, Object> convert(Field field) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("type", EsFieldType.TEXT.getValue().toLowerCase());

    return fieldMap;
  }
}
