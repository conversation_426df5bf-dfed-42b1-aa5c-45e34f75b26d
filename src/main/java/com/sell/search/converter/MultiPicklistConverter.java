package com.sell.search.converter;

import static java.util.Collections.singletonList;

import com.sell.search.core.domain.EntityType;
import com.sell.search.dto.EsFieldType;
import com.sell.search.field.domain.Field;
import com.sell.search.model.FieldType;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MultiPicklistConverter implements DataTypeConverter {

  private final List<FieldType> valuesApplicableList = singletonList(FieldType.MULTI_PICKLIST);

  @Override
  public boolean canConvert(Field field) {
    return valuesApplicableList.contains(field.getFieldType());
  }

  @Override
  public Map<String, Object> convert(Field field) {

    Map<String, Object> idType = new HashMap<>();
    idType.put("type", EsFieldType.LONG.getValue().toLowerCase());

    Map<String, Object> fieldMap = new HashMap<>();

    Map<String, Object> idMap = new HashMap<>();
    idMap.put("id", idType);
    if(Arrays.asList(EntityType.DEAL,EntityType.COMPANY).contains(field.getEntityType())){
      Map<String,Object> nameType = new HashMap<>();
      nameType.put("type",EsFieldType.KEYWORD.getValue().toLowerCase());
      nameType.put("normalizer", "case_insensitive");
      idMap.put("name",nameType);
    }
    fieldMap.put("type", EsFieldType.NESTED.getValue().toLowerCase());
    fieldMap.put("properties", idMap);

    return fieldMap;
  }
}
