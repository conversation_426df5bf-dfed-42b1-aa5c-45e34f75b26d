package com.sell.search.converter;

import com.sell.search.dto.EsFieldType;
import com.sell.search.field.domain.Field;
import com.sell.search.model.FieldType;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by shashanks3 on 6/5/19.
 */
public class DateConverter implements DataTypeConverter {

  private final List<FieldType> valuesApplicableList = Arrays.asList(FieldType.DATE_PICKER,
      FieldType.DATETIME_PICKER);

  @Override
  public boolean canConvert(Field field) {
    return valuesApplicableList.contains(field.getFieldType());
  }

  @Override
  public Map<String, Object> convert(Field field) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("type", EsFieldType.DATE.getValue().toLowerCase());
    return fieldMap;
  }
}
