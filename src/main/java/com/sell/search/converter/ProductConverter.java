package com.sell.search.converter;

import static java.util.Collections.singletonList;

import com.sell.search.dto.EsFieldType;
import com.sell.search.field.domain.Field;
import com.sell.search.model.FieldType;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProductConverter implements DataTypeConverter {

  private final List<FieldType> valuesApplicableList = singletonList(FieldType.LOOK_UP);
  DataTypeConverter dataTypeConverterFatory;

  ProductConverter(DataTypeConverter dataTypeConverter) {
    dataTypeConverterFatory = dataTypeConverter;
  }

  @Override
  public boolean canConvert(Field field) {
    return valuesApplicableList.contains(field.getFieldType())
        && "products".equalsIgnoreCase(field.getName()) && field.isStandard();
  }

  @Override
  public Map<String, Object> convert(Field field) {
    Map<String, Object> fieldMap = new HashMap<>();
    Map<String, Object> productMap = new HashMap<>();
    Field numberField = new Field();
    numberField.setFieldType(FieldType.NUMBER);
    Field textField = new Field();
    textField.setFieldType(FieldType.TEXT_FIELD);
    productMap.put("productId", dataTypeConverterFatory.convert(numberField));
    productMap.put("name", dataTypeConverterFatory.convert(textField));
    fieldMap.put("type", EsFieldType.NESTED.getValue().toLowerCase());
    fieldMap.put("properties", productMap);
    return fieldMap;
  }
}
