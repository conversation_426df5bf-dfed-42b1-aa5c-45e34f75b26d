package com.sell.search.converter;

import com.sell.search.dto.EsFieldType;
import com.sell.search.field.domain.Field;
import com.sell.search.model.FieldType;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by shashanks3 on 15/5/19.
 */
public class PhoneConverter implements DataTypeConverter {

  private final List<FieldType> valuesApplicableList = Arrays.asList(FieldType.PHONE);
  DataTypeConverter dataTypeConverterFatory;

  PhoneConverter(DataTypeConverter dataTypeConverter) {
    dataTypeConverterFatory = dataTypeConverter;
  }

  @Override
  public boolean canConvert(Field field) {
    return valuesApplicableList.contains(field.getFieldType());
  }

  @Override
  public Map<String, Object> convert(Field field) {
    Map<String, Object> fieldMap = new HashMap<>();
    Map<String, Object> phoneMap = new HashMap<>();
    Field textField = new Field();
    textField.setFieldType(FieldType.TEXT_FIELD);
    Field booleanField = new Field();
    booleanField.setFieldType(FieldType.CHECKBOX);
    phoneMap.put("type", dataTypeConverterFatory.convert(textField));
    phoneMap.put("code", dataTypeConverterFatory.convert(textField));
    phoneMap.put("value", dataTypeConverterFatory.convert(textField));
    phoneMap.put("isPrimary", dataTypeConverterFatory.convert(booleanField));
    fieldMap.put("type", EsFieldType.NESTED.getValue().toLowerCase());
    fieldMap.put("properties", phoneMap);
    return fieldMap;
  }
}
