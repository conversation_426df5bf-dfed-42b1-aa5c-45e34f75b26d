package com.sell.search.converter;

import com.sell.search.dto.EsFieldType;
import com.sell.search.field.domain.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
public class DataTypeConverterFactory implements DataTypeConverter {

  List<DataTypeConverter> allConverter = new ArrayList<>();

  public DataTypeConverterFactory() {
    allConverter.add(new KeyWordConverter());
    allConverter.add(new TextConverter());
    allConverter.add(new NumericConverter());
    allConverter.add(new TimeConverter());
    allConverter.add(new DateConverter());
    allConverter.add(new EmailConverter(this));
    allConverter.add(new BooleanConverter());
    allConverter.add(new PhoneConverter(this));
    allConverter.add(new ProductConverter(this));
    allConverter.add(new MultiPicklistConverter());
  }

  public Map<String, Object> convert(List<Field> fieldList, boolean isNewMapping) {
    Map<String, Object> esFieldMapping = new HashMap<>();
    Map<String, Object> searchMap = new HashMap<>();
    /*
    Adding to fullText for search on all data
     */
    if (isNewMapping) {
      searchMap.put("type", EsFieldType.TEXT.getValue().toLowerCase());
      esFieldMapping.put("fullText", searchMap);
    }

    fieldList.forEach(field -> {
      Map<String, Object> fieldMap = convert(field);
      esFieldMapping.put(field.getName(), fieldMap);
    });

    return esFieldMapping;
  }


  @Override
  public boolean canConvert(Field field) {
    return false;
  }

  @Override
  public Map<String, Object> convert(Field field) {
    for (DataTypeConverter converter : allConverter) {
      if (converter.canConvert(field)) {
        return converter.convert(field);
      }
    }
    TextConverter textConverter = new TextConverter();
    return textConverter.convert(field);
  }
}
