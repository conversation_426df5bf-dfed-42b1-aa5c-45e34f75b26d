package com.sell.search.converter;

import com.sell.search.dto.EsFieldType;
import com.sell.search.field.domain.Field;
import com.sell.search.model.FieldType;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EmailConverter implements DataTypeConverter {

  private final List<FieldType> valuesApplicableList = Arrays.asList(FieldType.EMAIL);
  DataTypeConverter dataTypeConverterFatory;

  EmailConverter(DataTypeConverter dataTypeConverter) {
    dataTypeConverterFatory = dataTypeConverter;
  }

  @Override
  public boolean canConvert(Field field) {
    return valuesApplicableList.contains(field.getFieldType());
  }

  @Override
  public Map<String, Object> convert(Field field) {
    Map<String, Object> fieldMap = new HashMap<>();
    Map<String, Object> emailMap = new HashMap<>();
    Field textField = new Field();
    textField.setFieldType(FieldType.TEXT_FIELD);
    Field booleanField = new Field();
    booleanField.setFieldType(FieldType.CHECKBOX);
    emailMap.put("type", dataTypeConverterFatory.convert(textField));
    emailMap.put("value", dataTypeConverterFatory.convert(textField));
    emailMap.put("isPrimary", dataTypeConverterFatory.convert(booleanField));
    fieldMap.put("type", EsFieldType.NESTED.getValue().toLowerCase());
    fieldMap.put("properties", emailMap);
    return fieldMap;
  }
}
