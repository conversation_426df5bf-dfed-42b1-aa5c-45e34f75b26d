package com.sell.search.converter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.search.core.domain.EntityType;
import com.sell.search.dto.EsFieldType;
import com.sell.search.field.domain.Field;
import com.sell.search.model.FieldType;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;

/**
 * Created by shashanks3 on 6/5/19. A field to index structured content such as email addresses, hostnames, status codes, zip codes or tags. They are
 * typically used for filtering (Find me all blog posts where status is published), for sorting, and for aggregations. Keyword fields are only
 * searchable by their exact value
 */
@Slf4j
public class KeyWordConverter implements DataTypeConverter {

  private final List<FieldType> valuesApplicableList = Arrays
      .asList(FieldType.TEXT_FIELD, FieldType.PICK_LIST, FieldType.FORECASTING_TYPE);

  @Override
  public boolean canConvert(Field field) {
    return valuesApplicableList.contains(field.getFieldType());
  }

  @Override
  public Map<String, Object> convert(Field field) {
    Map<String, Object> fieldMap = new HashMap<>();

    if ((field.getEntityType() == EntityType.DEAL || field.getEntityType() == EntityType.COMPANY) && field.getFieldType() == FieldType.PICK_LIST) {
      try {
        fieldMap = new ObjectMapper().readValue(new ClassPathResource("es/id-name-field-for-picklist.json").getInputStream(), Map.class);
      } catch (IOException e) {
        log.info("Exception while reading from file with message {}, {}", e.getMessage(), e);
      }
      return fieldMap;
    }

    fieldMap.put("type", EsFieldType.KEYWORD.getValue().toLowerCase());
    fieldMap.put("normalizer", "case_insensitive");
    return fieldMap;
  }
}
