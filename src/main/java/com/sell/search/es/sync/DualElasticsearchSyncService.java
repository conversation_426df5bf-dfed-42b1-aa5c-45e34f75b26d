package com.sell.search.es.sync;

import com.sell.search.es.abstraction.ElasticsearchClient;
import com.sell.search.es.abstraction.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Service that synchronizes data to both legacy (6.x) and new (8.x) Elasticsearch instances
 */
@Service
@Slf4j
public class DualElasticsearchSyncService {

    private final ElasticsearchClient legacyClient;
    private final ElasticsearchClient newClient;
    private final boolean dualSyncEnabled;
    private final ExecutorService executorService;

    public DualElasticsearchSyncService(
            @Qualifier("legacyElasticsearchClient") ElasticsearchClient legacyClient,
            @Qualifier("newElasticsearchClient") ElasticsearchClient newClient,
            @Value("${elasticsearch.dual.sync.enabled:true}") boolean dualSyncEnabled) {
        this.legacyClient = legacyClient;
        this.newClient = newClient;
        this.dualSyncEnabled = dualSyncEnabled;
        this.executorService = Executors.newFixedThreadPool(4);
    }

    /**
     * Index a document to both Elasticsearch instances
     */
    public DualSyncResult<IndexResponse> index(IndexRequest request) {
        if (!dualSyncEnabled) {
            return indexLegacyOnly(request);
        }

        CompletableFuture<IndexResponse> legacyFuture = CompletableFuture.supplyAsync(() -> {
            try {
                log.debug("Indexing document {} to legacy ES", request.getDocumentId());
                return legacyClient.index(request);
            } catch (IOException e) {
                log.error("Failed to index document {} to legacy ES: {}", request.getDocumentId(), e.getMessage());
                throw new RuntimeException("Legacy ES indexing failed", e);
            }
        }, executorService);

        CompletableFuture<IndexResponse> newFuture = CompletableFuture.supplyAsync(() -> {
            try {
                log.debug("Indexing document {} to new ES", request.getDocumentId());
                return newClient.index(request);
            } catch (IOException e) {
                log.error("Failed to index document {} to new ES: {}", request.getDocumentId(), e.getMessage());
                throw new RuntimeException("New ES indexing failed", e);
            }
        }, executorService);

        return waitForBothResults(legacyFuture, newFuture, "index", request.getDocumentId());
    }

    /**
     * Bulk index documents to both Elasticsearch instances
     */
    public DualSyncResult<BulkResponse> bulk(BulkRequest request) {
        if (!dualSyncEnabled) {
            return bulkLegacyOnly(request);
        }

        CompletableFuture<BulkResponse> legacyFuture = CompletableFuture.supplyAsync(() -> {
            try {
                log.debug("Bulk indexing {} documents to legacy ES", request.getIndexRequests().size());
                return legacyClient.bulk(request);
            } catch (IOException e) {
                log.error("Failed to bulk index to legacy ES: {}", e.getMessage());
                throw new RuntimeException("Legacy ES bulk indexing failed", e);
            }
        }, executorService);

        CompletableFuture<BulkResponse> newFuture = CompletableFuture.supplyAsync(() -> {
            try {
                log.debug("Bulk indexing {} documents to new ES", request.getIndexRequests().size());
                return newClient.bulk(request);
            } catch (IOException e) {
                log.error("Failed to bulk index to new ES: {}", e.getMessage());
                throw new RuntimeException("New ES bulk indexing failed", e);
            }
        }, executorService);

        return waitForBothResults(legacyFuture, newFuture, "bulk", 
                String.valueOf(request.getIndexRequests().size()));
    }

    /**
     * Search from the primary (legacy) Elasticsearch instance
     * In the future, this can be switched to the new instance
     */
    public SearchResponse search(SearchRequest request) {
        try {
            log.debug("Searching from legacy ES for indices: {}", String.join(",", request.getIndices()));
            return legacyClient.search(request);
        } catch (IOException e) {
            log.error("Failed to search from legacy ES: {}", e.getMessage());
            throw new RuntimeException("Legacy ES search failed", e);
        }
    }

    /**
     * Create index on both Elasticsearch instances
     */
    public DualSyncResult<Boolean> createIndex(CreateIndexRequest request) {
        if (!dualSyncEnabled) {
            return createIndexLegacyOnly(request);
        }

        CompletableFuture<Boolean> legacyFuture = CompletableFuture.supplyAsync(() -> {
            try {
                log.debug("Creating index {} on legacy ES", request.getIndexName());
                return legacyClient.createIndex(request);
            } catch (IOException e) {
                log.error("Failed to create index {} on legacy ES: {}", request.getIndexName(), e.getMessage());
                throw new RuntimeException("Legacy ES index creation failed", e);
            }
        }, executorService);

        CompletableFuture<Boolean> newFuture = CompletableFuture.supplyAsync(() -> {
            try {
                log.debug("Creating index {} on new ES", request.getIndexName());
                return newClient.createIndex(request);
            } catch (IOException e) {
                log.error("Failed to create index {} on new ES: {}", request.getIndexName(), e.getMessage());
                throw new RuntimeException("New ES index creation failed", e);
            }
        }, executorService);

        return waitForBothResults(legacyFuture, newFuture, "createIndex", request.getIndexName());
    }

    /**
     * Check if index exists on legacy ES (primary source of truth)
     */
    public boolean indexExists(String indexName) {
        try {
            return legacyClient.indexExists(indexName);
        } catch (IOException e) {
            log.error("Failed to check index existence on legacy ES: {}", e.getMessage());
            throw new RuntimeException("Legacy ES index existence check failed", e);
        }
    }

    /**
     * Delete document from both Elasticsearch instances
     */
    public DualSyncResult<Boolean> delete(String indexName, String documentType, String documentId) {
        if (!dualSyncEnabled) {
            return deleteLegacyOnly(indexName, documentType, documentId);
        }

        CompletableFuture<Boolean> legacyFuture = CompletableFuture.supplyAsync(() -> {
            try {
                log.debug("Deleting document {} from legacy ES", documentId);
                return legacyClient.delete(indexName, documentType, documentId);
            } catch (IOException e) {
                log.error("Failed to delete document {} from legacy ES: {}", documentId, e.getMessage());
                throw new RuntimeException("Legacy ES deletion failed", e);
            }
        }, executorService);

        CompletableFuture<Boolean> newFuture = CompletableFuture.supplyAsync(() -> {
            try {
                log.debug("Deleting document {} from new ES", documentId);
                return newClient.delete(indexName, documentType, documentId);
            } catch (IOException e) {
                log.error("Failed to delete document {} from new ES: {}", documentId, e.getMessage());
                throw new RuntimeException("New ES deletion failed", e);
            }
        }, executorService);

        return waitForBothResults(legacyFuture, newFuture, "delete", documentId);
    }

    private <T> DualSyncResult<T> waitForBothResults(CompletableFuture<T> legacyFuture, 
                                                     CompletableFuture<T> newFuture, 
                                                     String operation, String identifier) {
        try {
            T legacyResult = legacyFuture.get();
            T newResult = newFuture.get();
            
            log.info("Successfully completed {} operation for {} on both ES instances", operation, identifier);
            return DualSyncResult.<T>builder()
                    .legacyResult(legacyResult)
                    .newResult(newResult)
                    .success(true)
                    .build();
        } catch (Exception e) {
            log.error("Failed to complete {} operation for {} on both ES instances: {}", 
                    operation, identifier, e.getMessage());
            
            // Try to get partial results
            T legacyResult = null;
            T newResult = null;
            
            try {
                legacyResult = legacyFuture.getNow(null);
            } catch (Exception ignored) {}
            
            try {
                newResult = newFuture.getNow(null);
            } catch (Exception ignored) {}
            
            return DualSyncResult.<T>builder()
                    .legacyResult(legacyResult)
                    .newResult(newResult)
                    .success(false)
                    .error(e.getMessage())
                    .build();
        }
    }

    // Fallback methods when dual sync is disabled
    private DualSyncResult<IndexResponse> indexLegacyOnly(IndexRequest request) {
        try {
            IndexResponse result = legacyClient.index(request);
            return DualSyncResult.<IndexResponse>builder()
                    .legacyResult(result)
                    .success(true)
                    .build();
        } catch (IOException e) {
            throw new RuntimeException("Legacy ES indexing failed", e);
        }
    }

    private DualSyncResult<BulkResponse> bulkLegacyOnly(BulkRequest request) {
        try {
            BulkResponse result = legacyClient.bulk(request);
            return DualSyncResult.<BulkResponse>builder()
                    .legacyResult(result)
                    .success(true)
                    .build();
        } catch (IOException e) {
            throw new RuntimeException("Legacy ES bulk indexing failed", e);
        }
    }

    private DualSyncResult<Boolean> createIndexLegacyOnly(CreateIndexRequest request) {
        try {
            Boolean result = legacyClient.createIndex(request);
            return DualSyncResult.<Boolean>builder()
                    .legacyResult(result)
                    .success(true)
                    .build();
        } catch (IOException e) {
            throw new RuntimeException("Legacy ES index creation failed", e);
        }
    }

    private DualSyncResult<Boolean> deleteLegacyOnly(String indexName, String documentType, String documentId) {
        try {
            Boolean result = legacyClient.delete(indexName, documentType, documentId);
            return DualSyncResult.<Boolean>builder()
                    .legacyResult(result)
                    .success(true)
                    .build();
        } catch (IOException e) {
            throw new RuntimeException("Legacy ES deletion failed", e);
        }
    }
}
