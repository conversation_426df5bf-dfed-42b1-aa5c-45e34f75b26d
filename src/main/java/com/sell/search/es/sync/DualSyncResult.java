package com.sell.search.es.sync;

import lombok.Builder;
import lombok.Data;

/**
 * Result wrapper for dual Elasticsearch sync operations
 */
@Data
@Builder
public class DualSyncResult<T> {
    private T legacyResult;
    private T newResult;
    private boolean success;
    private String error;

    /**
     * Get the primary result (legacy for now, can be switched to new later)
     */
    public T getPrimaryResult() {
        return legacyResult;
    }

    /**
     * Check if both operations succeeded
     */
    public boolean isBothSuccessful() {
        return success && legacyResult != null && newResult != null;
    }

    /**
     * Check if at least one operation succeeded
     */
    public boolean isPartialSuccess() {
        return legacyResult != null || newResult != null;
    }
}
