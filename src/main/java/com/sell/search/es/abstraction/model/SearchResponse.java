package com.sell.search.es.abstraction.model;

import lombok.Builder;
import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class SearchResponse {
    private long totalHits;
    private List<SearchHit> hits;
    private Map<String, Object> aggregations;
    private boolean timedOut;
    private long took;

    @Data
    @Builder
    public static class SearchHit {
        private String id;
        private String index;
        private String type;
        private float score;
        private Map<String, Object> source;
    }
}
