package com.sell.search.es.abstraction.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.VersionType;
import co.elastic.clients.elasticsearch.core.*;
import co.elastic.clients.elasticsearch.core.bulk.BulkOperation;
import co.elastic.clients.elasticsearch.core.bulk.IndexOperation;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.indices.CreateIndexRequest;
import co.elastic.clients.elasticsearch.indices.ExistsRequest;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import com.sell.search.es.abstraction.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class Elasticsearch8Client implements com.sell.search.es.abstraction.ElasticsearchClient {

    private final ElasticsearchClient client;
    private final RestClient restClient;

    public Elasticsearch8Client(String host, int port) {
        this.restClient = RestClient.builder(new HttpHost(host, port, "http")).build();
        RestClientTransport transport = new RestClientTransport(restClient, new JacksonJsonpMapper());
        this.client = new ElasticsearchClient(transport);
    }

    @Override
    public IndexResponse index(IndexRequest request) throws IOException {
        co.elastic.clients.elasticsearch.core.IndexRequest.Builder<Map<String, Object>> builder = 
            new co.elastic.clients.elasticsearch.core.IndexRequest.Builder<Map<String, Object>>()
                .index(request.getIndexName())
                .id(request.getDocumentId())
                .document(request.getSource());

        if (request.getVersionType() != null) {
            builder.versionType(VersionType.valueOf(request.getVersionType().toUpperCase()));
        }
        if (request.getVersion() != null) {
            builder.version(request.getVersion());
        }

        co.elastic.clients.elasticsearch.core.IndexResponse esResponse = 
            client.index(builder.build());

        return IndexResponse.builder()
                .id(esResponse.id())
                .index(esResponse.index())
                .type("_doc") // ES 8.x uses _doc as default type
                .version(esResponse.version())
                .result(esResponse.result().jsonValue())
                .created(esResponse.result().jsonValue().equals("created"))
                .build();
    }

    @Override
    public BulkResponse bulk(BulkRequest request) throws IOException {
        List<BulkOperation> operations = new ArrayList<>();
        
        for (IndexRequest indexReq : request.getIndexRequests()) {
            IndexOperation.Builder<Map<String, Object>> opBuilder = 
                new IndexOperation.Builder<Map<String, Object>>()
                    .index(indexReq.getIndexName())
                    .id(indexReq.getDocumentId())
                    .document(indexReq.getSource());

            if (indexReq.getVersionType() != null) {
                opBuilder.versionType(VersionType.valueOf(indexReq.getVersionType().toUpperCase()));
            }
            if (indexReq.getVersion() != null) {
                opBuilder.version(indexReq.getVersion());
            }

            operations.add(BulkOperation.of(op -> op.index(opBuilder.build())));
        }

        co.elastic.clients.elasticsearch.core.BulkRequest esBulkRequest = 
            co.elastic.clients.elasticsearch.core.BulkRequest.of(b -> b.operations(operations));

        co.elastic.clients.elasticsearch.core.BulkResponse esBulkResponse = 
            client.bulk(esBulkRequest);

        List<BulkResponse.BulkItemResponse> items = new ArrayList<>();
        esBulkResponse.items().forEach(item -> {
            items.add(BulkResponse.BulkItemResponse.builder()
                    .id(item.id())
                    .index(item.index())
                    .type("_doc")
                    .version(item.version() != null ? item.version() : 0L)
                    .result(item.result() != null ? item.result() : "unknown")
                    .failed(item.error() != null)
                    .failureMessage(item.error() != null ? item.error().reason() : null)
                    .build());
        });

        return BulkResponse.builder()
                .hasFailures(esBulkResponse.errors())
                .items(items)
                .took(esBulkResponse.took())
                .build();
    }

    @Override
    public SearchResponse search(SearchRequest request) throws IOException {
        co.elastic.clients.elasticsearch.core.SearchRequest.Builder esRequestBuilder = 
            new co.elastic.clients.elasticsearch.core.SearchRequest.Builder()
                .index(List.of(request.getIndices()))
                .from(request.getFrom())
                .size(request.getSize());

        // Note: Query building for ES 8.x would need more complex mapping
        // For now, we'll use a simple match_all query
        esRequestBuilder.query(q -> q.matchAll(m -> m));

        co.elastic.clients.elasticsearch.core.SearchResponse<Map> esResponse = 
            client.search(esRequestBuilder.build(), Map.class);

        List<SearchResponse.SearchHit> hits = new ArrayList<>();
        for (Hit<Map> hit : esResponse.hits().hits()) {
            hits.add(SearchResponse.SearchHit.builder()
                    .id(hit.id())
                    .index(hit.index())
                    .type("_doc")
                    .score(hit.score() != null ? hit.score().floatValue() : 0.0f)
                    .source(hit.source())
                    .build());
        }

        return SearchResponse.builder()
                .totalHits(esResponse.hits().total() != null ? esResponse.hits().total().value() : 0L)
                .hits(hits)
                .timedOut(esResponse.timedOut())
                .took(esResponse.took())
                .build();
    }

    @Override
    public boolean createIndex(CreateIndexRequest request) throws IOException {
        CreateIndexRequest.Builder esRequestBuilder = new CreateIndexRequest.Builder()
                .index(request.getIndexName());

        if (request.getSettings() != null) {
            esRequestBuilder.settings(s -> s.withJson(
                co.elastic.clients.json.JsonData.of(request.getSettings())));
        }

        if (request.getMappings() != null) {
            esRequestBuilder.mappings(m -> m.withJson(
                co.elastic.clients.json.JsonData.of(request.getMappings())));
        }

        return client.indices().create(esRequestBuilder.build()).acknowledged();
    }

    @Override
    public boolean indexExists(String indexName) throws IOException {
        ExistsRequest request = ExistsRequest.of(e -> e.index(indexName));
        return client.indices().exists(request).value();
    }

    @Override
    public boolean delete(String indexName, String documentType, String documentId) throws IOException {
        co.elastic.clients.elasticsearch.core.DeleteRequest request = 
            co.elastic.clients.elasticsearch.core.DeleteRequest.of(d -> d
                .index(indexName)
                .id(documentId));
        
        co.elastic.clients.elasticsearch.core.DeleteResponse response = client.delete(request);
        return response.result().jsonValue().equals("deleted");
    }

    @Override
    public String getVersion() {
        return "8.11.3";
    }

    @Override
    public void close() throws IOException {
        restClient.close();
    }
}
