package com.sell.search.es.abstraction.model;

import lombok.Builder;
import lombok.Data;
import java.util.List;

@Data
@Builder
public class BulkResponse {
    private boolean hasFailures;
    private List<BulkItemResponse> items;
    private long took;

    @Data
    @Builder
    public static class BulkItemResponse {
        private String id;
        private String index;
        private String type;
        private long version;
        private String result;
        private boolean failed;
        private String failureMessage;
    }
}
