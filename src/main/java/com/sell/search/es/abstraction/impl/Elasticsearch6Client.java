package com.sell.search.es.abstraction.impl;

import com.sell.search.es.abstraction.ElasticsearchClient;
import com.sell.search.es.abstraction.model.*;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.action.admin.indices.exists.indices.IndicesExistsRequest;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.VersionType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class Elasticsearch6Client implements ElasticsearchClient {

    private final RestHighLevelClient client;

    public Elasticsearch6Client(RestHighLevelClient client) {
        this.client = client;
    }

    @Override
    public com.sell.search.es.abstraction.model.IndexResponse index(
            com.sell.search.es.abstraction.model.IndexRequest request) throws IOException {
        
        IndexRequest esRequest = new IndexRequest(
                request.getIndexName(),
                request.getDocumentType(),
                request.getDocumentId())
                .source(request.getSource(), XContentType.JSON);

        if (request.getVersionType() != null) {
            esRequest.versionType(VersionType.fromString(request.getVersionType()));
        }
        if (request.getVersion() != null) {
            esRequest.version(request.getVersion());
        }

        IndexResponse esResponse = client.index(esRequest, RequestOptions.DEFAULT);

        return com.sell.search.es.abstraction.model.IndexResponse.builder()
                .id(esResponse.getId())
                .index(esResponse.getIndex())
                .type(esResponse.getType())
                .version(esResponse.getVersion())
                .result(esResponse.getResult().name())
                .created(esResponse.getResult().name().equals("CREATED"))
                .build();
    }

    @Override
    public com.sell.search.es.abstraction.model.BulkResponse bulk(
            com.sell.search.es.abstraction.model.BulkRequest request) throws IOException {
        
        BulkRequest esBulkRequest = new BulkRequest();
        
        for (com.sell.search.es.abstraction.model.IndexRequest indexReq : request.getIndexRequests()) {
            IndexRequest esIndexRequest = new IndexRequest(
                    indexReq.getIndexName(),
                    indexReq.getDocumentType(),
                    indexReq.getDocumentId())
                    .source(indexReq.getSource(), XContentType.JSON);
            
            if (indexReq.getVersionType() != null) {
                esIndexRequest.versionType(VersionType.fromString(indexReq.getVersionType()));
            }
            if (indexReq.getVersion() != null) {
                esIndexRequest.version(indexReq.getVersion());
            }
            
            esBulkRequest.add(esIndexRequest);
        }

        BulkResponse esBulkResponse = client.bulk(esBulkRequest, RequestOptions.DEFAULT);

        List<com.sell.search.es.abstraction.model.BulkResponse.BulkItemResponse> items = new ArrayList<>();
        esBulkResponse.forEach(item -> {
            items.add(com.sell.search.es.abstraction.model.BulkResponse.BulkItemResponse.builder()
                    .id(item.getId())
                    .index(item.getIndex())
                    .type(item.getType())
                    .version(item.getVersion())
                    .result(item.getResponse().getResult().name())
                    .failed(item.isFailed())
                    .failureMessage(item.isFailed() ? item.getFailureMessage() : null)
                    .build());
        });

        return com.sell.search.es.abstraction.model.BulkResponse.builder()
                .hasFailures(esBulkResponse.hasFailures())
                .items(items)
                .took(esBulkResponse.getTook().millis())
                .build();
    }

    @Override
    public com.sell.search.es.abstraction.model.SearchResponse search(
            com.sell.search.es.abstraction.model.SearchRequest request) throws IOException {
        
        SearchRequest esRequest = new SearchRequest(request.getIndices());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        
        if (request.getQuery() != null) {
            sourceBuilder.query(org.elasticsearch.common.xcontent.XContentFactory.jsonBuilder()
                    .map(request.getQuery()).build());
        }
        
        sourceBuilder.from(request.getFrom());
        sourceBuilder.size(request.getSize());
        
        esRequest.source(sourceBuilder);
        
        SearchResponse esResponse = client.search(esRequest, RequestOptions.DEFAULT);
        
        List<com.sell.search.es.abstraction.model.SearchResponse.SearchHit> hits = new ArrayList<>();
        for (SearchHit hit : esResponse.getHits().getHits()) {
            hits.add(com.sell.search.es.abstraction.model.SearchResponse.SearchHit.builder()
                    .id(hit.getId())
                    .index(hit.getIndex())
                    .type(hit.getType())
                    .score(hit.getScore())
                    .source(hit.getSourceAsMap())
                    .build());
        }

        return com.sell.search.es.abstraction.model.SearchResponse.builder()
                .totalHits(esResponse.getHits().getTotalHits())
                .hits(hits)
                .timedOut(esResponse.isTimedOut())
                .took(esResponse.getTook().millis())
                .build();
    }

    @Override
    public boolean createIndex(com.sell.search.es.abstraction.model.CreateIndexRequest request) throws IOException {
        CreateIndexRequest esRequest = new CreateIndexRequest(request.getIndexName());
        
        if (request.getSettings() != null) {
            esRequest.settings(request.getSettings());
        }
        
        if (request.getMappings() != null && request.getDocumentType() != null) {
            esRequest.mapping(request.getDocumentType(), request.getMappings());
        }
        
        return client.indices().create(esRequest, RequestOptions.DEFAULT).isAcknowledged();
    }

    @Override
    public boolean indexExists(String indexName) throws IOException {
        IndicesExistsRequest request = new IndicesExistsRequest(indexName);
        return client.indices().exists(request, RequestOptions.DEFAULT);
    }

    @Override
    public boolean delete(String indexName, String documentType, String documentId) throws IOException {
        DeleteRequest request = new DeleteRequest(indexName, documentType, documentId);
        return client.delete(request, RequestOptions.DEFAULT).getResult().name().equals("DELETED");
    }

    @Override
    public String getVersion() {
        return "6.8.17";
    }

    @Override
    public void close() throws IOException {
        client.close();
    }
}
