package com.sell.search.es.abstraction.model;

import lombok.Builder;
import lombok.Data;
import java.util.Map;

@Data
@Builder
public class SearchRequest {
    private String[] indices;
    private Map<String, Object> query;
    private int from;
    private int size;
    private Map<String, Object> sort;
    private String[] sourceIncludes;
    private String[] sourceExcludes;
    private Map<String, Object> aggregations;
}
