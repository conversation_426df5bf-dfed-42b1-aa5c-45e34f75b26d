package com.sell.search.es.mapping;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.search.core.domain.EntityType;
import com.sell.search.entity.core.config.IdNameConfig;
import com.sell.search.controller.response.IdName;
import com.sell.search.converter.DataTypeConverterFactory;
import com.sell.search.es.mapping.domain.IdNameRequest;
import com.sell.search.es.mapping.domain.Indexable;
import com.sell.search.es.mapping.dto.DocumentIdIndexMapping;
import com.sell.search.es.mapping.exception.ElasticsearchAliasException;
import com.sell.search.es.mapping.exception.ElasticsearchIndexException;
import com.sell.search.es.mapping.exception.IdNameStoreException;
import com.sell.search.exception.SearchErrorCodes;
import com.sell.search.exception.SearchException;
import com.sell.search.field.domain.Field;
import com.sell.search.model.FieldType;
import com.sell.search.security.UserFacade;
import com.sell.search.es.sync.DualElasticsearchSyncService;
import com.sell.search.es.sync.DualSyncResult;
import com.sell.search.es.abstraction.model.IndexRequest;
import com.sell.search.es.abstraction.model.BulkRequest;
import com.sell.search.es.abstraction.model.CreateIndexRequest;
import com.sell.search.utils.JsonUtil;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.lucene.search.join.ScoreMode;
import org.codehaus.jettison.json.JSONException;
import org.codehaus.jettison.json.JSONObject;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.action.admin.indices.alias.IndicesAliasesRequest;
import org.elasticsearch.action.admin.indices.alias.IndicesAliasesRequest.AliasActions;
import org.elasticsearch.action.admin.indices.alias.IndicesAliasesRequest.AliasActions.Type;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.admin.indices.get.GetIndexRequest;
import org.elasticsearch.action.admin.indices.mapping.put.PutMappingRequest;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest.RefreshPolicy;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.VersionType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.NestedQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ElasticsearchFacade {
  private final RestHighLevelClient client;
  private final ObjectMapper objectMapper;
  private final UserFacade userFacade;
  private final DataTypeConverterFactory dataTypeConverterFactory;
  private final DualElasticsearchSyncService dualSyncService;

  @Autowired
  public ElasticsearchFacade(
      @Value("${newElasticsearch.host}") String host,
      @Value("${newElasticsearch.port}") int port,
      ObjectMapper objectMapper,
      UserFacade userFacade,
      DataTypeConverterFactory dataTypeConverterFactory,
      DualElasticsearchSyncService dualSyncService) {
    this.objectMapper = objectMapper;
    this.userFacade = userFacade;
    this.dataTypeConverterFactory = dataTypeConverterFactory;
    this.dualSyncService = dualSyncService;
    // Keep the legacy client for backward compatibility and non-sync operations
    this.client = new RestHighLevelClient(RestClient.builder(new HttpHost(host, port, "http")));
  }

  public boolean createIndexAlias(String indexName, String entity, long tenantId) {
    log.info(
        "Index alias creating for tenantId {}, entity {}, into root indexName {}",
        tenantId,
        entity,
        indexName);
    IndicesAliasesRequest indicesAliasesRequest = new IndicesAliasesRequest();
    try {

      AliasActions aliasActions =
          new AliasActions(Type.ADD)
              .index(indexName)
              .alias(String.format("%d-%s", tenantId, entity))
              .filter(getFilter(tenantId));
      indicesAliasesRequest.addAliasAction(aliasActions);
      return client
          .indices()
          .updateAliases(indicesAliasesRequest, RequestOptions.DEFAULT)
          .isAcknowledged();
    } catch (IOException e) {
      log.error(e.getMessage(), e);
      throw new ElasticsearchAliasException();
    }
  }

  private String getFilter(long tenantId) throws JsonProcessingException {
    Map<String, Map<String, Long>> filter = new HashMap<>();
    filter.put(
        "term",
        new HashMap() {
          {
            put("tenantId", tenantId);
          }
        });
    return objectMapper.writeValueAsString(filter);
  }

  public boolean createIndex(String indexTemplate, String indexName, String documentName) {
    log.info("Root index creation for indexName {} and documentName {}",indexName,documentName);
    try {
      JSONObject jsonObject = new JSONObject(indexTemplate);
      Map<String, Object> settings = JsonUtil.toMap(jsonObject.getString("settings"));
      Map<String, Object> mappings = JsonUtil.toMap(jsonObject.getString("mappings"));

      // Create abstraction layer request
      com.sell.search.es.abstraction.model.CreateIndexRequest abstractRequest =
          com.sell.search.es.abstraction.model.CreateIndexRequest.builder()
              .indexName(indexName)
              .settings(settings)
              .mappings(mappings)
              .documentType(documentName)
              .build();

      // Use dual sync service to create index on both ES instances
      DualSyncResult<Boolean> result = dualSyncService.createIndex(abstractRequest);

      if (result.isSuccess()) {
        return result.getPrimaryResult();
      } else {
        log.error("Failed to create index on both ES instances: {}", result.getError());
        throw new ElasticsearchAliasException();
      }
    } catch (ElasticsearchStatusException e) {
      if (e.getMessage().contains("already exists")) {
        log.error("Do not retry this exception {}, {}", e.getMessage(), e);
        return true;
      }
      log.error(e.getMessage(), e);
      throw new ElasticsearchAliasException();
    } catch (IOException | JSONException e) {
      log.error(e.getMessage(), e);
      throw new ElasticsearchAliasException();
    }
  }

  public String create(String indexName, IdNameRequest idNameRequest) {
    // Create abstraction layer request
    com.sell.search.es.abstraction.model.IndexRequest abstractRequest =
        com.sell.search.es.abstraction.model.IndexRequest.builder()
            .indexName(indexName)
            .documentType(IdNameConfig.TYPE_NAME)
            .documentId(getDocumentId(idNameRequest.getId(), idNameRequest.getForType()))
            .source(toMap(idNameRequest))
            .versionType("INTERNAL")
            .build();

    try {
      // Use dual sync service to index to both ES instances
      DualSyncResult<com.sell.search.es.abstraction.model.IndexResponse> result =
          dualSyncService.index(abstractRequest);

      if (result.isSuccess()) {
        return result.getPrimaryResult().getId();
      } else {
        log.error("Failed to sync document to both ES instances: {}", result.getError());
        throw new IdNameStoreException("Dual sync failed: " + result.getError());
      }
    } catch (Exception e) {
      log.error("error while creating id name in es {}", e);
      throw new IdNameStoreException(e.getMessage(), e);
    }
  }

  private Map toMap(Object object) {
    return new ObjectMapper().convertValue(object, Map.class);
  }

  @Cacheable(value = "default#3600", key = "{#indexName+'-index-exist'}",unless = "#result==false")
  public boolean isIndexExist(String indexName) {
    log.info("Start check isIndex {} exist",indexName);
    try {
      // Use dual sync service to check index existence (uses primary/legacy ES)
      boolean exists = dualSyncService.indexExists(indexName);
      log.info("End check isIndex {} exist {} ",indexName,exists);
      return exists;
    } catch (Exception e) {
      throw new ElasticsearchIndexException(e.getMessage(), e);
    }
  }

  public List<String> createBulk(String indexName, List<IdNameRequest> idNameRequests) {
    if(idNameRequests.size()==1){
      return Arrays.asList(create(indexName,idNameRequests.get(0)));
    }

    // Create abstraction layer bulk request
    List<com.sell.search.es.abstraction.model.IndexRequest> abstractIndexRequests =
        idNameRequests.stream()
            .map(idNameRequest ->
                com.sell.search.es.abstraction.model.IndexRequest.builder()
                    .indexName(indexName)
                    .documentType(IdNameConfig.TYPE_NAME)
                    .documentId(getDocumentId(idNameRequest.getId(), idNameRequest.getForType()))
                    .source(toMap(idNameRequest))
                    .versionType("INTERNAL")
                    .build())
            .collect(Collectors.toList());

    com.sell.search.es.abstraction.model.BulkRequest abstractBulkRequest =
        com.sell.search.es.abstraction.model.BulkRequest.builder()
            .indexRequests(abstractIndexRequests)
            .timeout("3m")
            .build();

    try {
      // Use dual sync service to bulk index to both ES instances
      DualSyncResult<com.sell.search.es.abstraction.model.BulkResponse> result =
          dualSyncService.bulk(abstractBulkRequest);

      if (result.isSuccess()) {
        return result.getPrimaryResult().getItems().stream()
            .map(item -> item.getId())
            .collect(Collectors.toList());
      } else {
        log.error("Failed to bulk sync documents to both ES instances: {}", result.getError());
        throw new ElasticsearchIndexException("Dual bulk sync failed: " + result.getError());
      }
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new ElasticsearchIndexException(e.getMessage(), e);
    }
  }

  public Optional<DocumentIdIndexMapping> findEsIdByIdAndForType(
      String indexName, Long id, String forType) {
    try {

      SearchRequest searchRequest = new SearchRequest(indexName);
      SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
      searchSourceBuilder.query(
          QueryBuilders.boolQuery()
              .must(QueryBuilders.termQuery(IdNameConfig.ID, id))
              .must(QueryBuilders.termQuery(IdNameConfig.FOR_TYPE, forType)));
      searchRequest.source(searchSourceBuilder);
      return findIdFromSearchHits(client.search(searchRequest, RequestOptions.DEFAULT), id);
    } catch (IOException e) {
      throw new ElasticsearchIndexException(e.getMessage(), e);
    }
  }

  private Optional<DocumentIdIndexMapping> findIdFromSearchHits(
      SearchResponse searchResponse, Long id) {
    SearchHit[] hits = searchResponse.getHits().getHits();
    if (hits != null && hits.length > 0) {
      for (SearchHit s : hits) {
        Map<String, Object> map = s.getSourceAsMap();
        if (map.containsKey("id")) {
          Long storedId = Long.valueOf((Integer) map.get("id"));
          if (storedId.equals(id)) {
            return Optional.of(new DocumentIdIndexMapping(s.getId(), s.getIndex()));
          }
        }
      }
    }
    return Optional.empty();
  }

  public String update(String indexId, String documentId, Indexable idNameRequest, String documentType) {
    // Create abstraction layer request
    com.sell.search.es.abstraction.model.IndexRequest abstractRequest =
        com.sell.search.es.abstraction.model.IndexRequest.builder()
            .indexName(indexId)
            .documentType(documentType)
            .documentId(documentId)
            .source(toMap(idNameRequest))
            .versionType("INTERNAL")
            .build();

    try {
      // Use dual sync service to update document in both ES instances
      DualSyncResult<com.sell.search.es.abstraction.model.IndexResponse> result =
          dualSyncService.index(abstractRequest);

      if (result.isSuccess()) {
        return result.getPrimaryResult().getId();
      } else {
        log.error("Failed to update document in both ES instances: {}", result.getError());
        throw new ElasticsearchIndexException("Dual update sync failed: " + result.getError());
      }
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new ElasticsearchIndexException(e.getMessage(), e);
    }
  }

  private String getDocumentId(long id, String forType) {
    return String.format("%s_%s_%s", forType, userFacade.getTenantId(),id);
  }

  public String delete(String indexName, IdNameRequest idNameRequest) {
    log.info("Deleting idName from indexName {} with documentId {}", indexName, idNameRequest.getDocumentId());
    DeleteRequest deleteRequest = new DeleteRequest(indexName, "store", idNameRequest.getDocumentId());
    try {
      return client.delete(deleteRequest, RequestOptions.DEFAULT).getId();
    } catch (IOException e) {
      log.info("Exception while deleting idName from indexName {} with documentId {}, message {}, {} ", indexName, idNameRequest.getDocumentId(),
          e.getMessage(), e);
      throw new ElasticsearchIndexException(e.getMessage(), e);
    }
  }

  public void createField(String name, FieldType fieldType, EntityType entityType, String indexName, long indexId, String rootIndexName) {
    log.info("Request received for field creation with name:{},fieldType:{}, entityType:{},indexName:{},indexId:{}", name, fieldType, entityType,
        indexName, indexId);
    PutMappingRequest request = new PutMappingRequest(rootIndexName);
    request.type(entityType.name().toLowerCase());


    Map<String, Object> map = new HashMap<>();
    Map<String, Object> fieldNameWithType = new HashMap<>();


    Map fieldSource = getFieldSource(entityType, fieldType);
    fieldNameWithType.put(name, fieldSource);
    map.put("properties", fieldNameWithType);
    request.source(map);
    try {
      client.indices().putMapping(request, RequestOptions.DEFAULT);
    } catch (IOException e) {
      log.info("Exception while creating field with name:{},fieldType:{}, entityType:{},indexName:{},indexId:{}", name, fieldType, entityType,
          indexName, indexId);
      log.error(e.getMessage(), e);
    }
  }


  private Map getFieldSource(EntityType entityType, FieldType fieldType) {
    Field field = new Field();
    field.setFieldType(fieldType);
    field.setEntityType(entityType);
    return dataTypeConverterFactory.convert(field);
  }

  public void deleteIndex(String indexName) {
    log.info("Delete master index {}",indexName);
    DeleteIndexRequest deleteIndex = new DeleteIndexRequest(indexName);
    try {
      client.indices().delete(deleteIndex,RequestOptions.DEFAULT);
    } catch (IOException e) {
      log.error(e.getMessage(),e);
      throw new SearchException(SearchErrorCodes.ES_DELETE_INDEX);
    }
  }

  public void updateIdNameOnEntity(String esFieldName, IdName idName, EntityType entityType, FieldType fieldType) {
    log.info("{} idName update request received for esFieldName {} with id {} and name {}",entityType, esFieldName,idName.getId(), idName.getName());
    String index= new String();
    if(entityType.equals(EntityType.DEAL)) {
      index = userFacade.getTenantId() + "-deal";
    }
    else if (entityType.equals(EntityType.COMPANY)) {
      index = userFacade.getTenantId()+"-company";
    }
    UpdateByQueryRequest request = getUpdateRequest(index, esFieldName,idName,fieldType);

    try {
      BulkByScrollResponse bulkByScrollResponse = client.updateByQuery(request, RequestOptions.DEFAULT);
      int batches = bulkByScrollResponse.getBatches();
      log.info("{} idName update request received for esFieldName {} with id {} and name {} total batch update are{} ",entityType, esFieldName,idName.getId(), idName.getName(), batches);
    } catch (IOException e) {
      log.info("{} idName update request received for esFieldName {} with id {} and name {} completed with error {}, {} ",entityType,esFieldName,idName.getId(), idName.getName(), e.getMessage(),e);
    }
  }

  public void updateUserNameOnEntity(IdName idName, EntityType entityType) {
    log.info("{} userName update request received with id {} and name {}",entityType, idName.getId(), idName.getName());
    String index= new String();
    if(entityType.equals(EntityType.DEAL)) {
      index = userFacade.getTenantId() + "-deal";
    }
    else if (entityType.equals(EntityType.COMPANY)) {
      index = userFacade.getTenantId()+"-company";
    }

    BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
        .should(QueryBuilders.nestedQuery("createdBy",
            QueryBuilders.termQuery("createdBy.id", idName.getId()), ScoreMode.Total))
        .should(QueryBuilders.nestedQuery("ownedBy",
            QueryBuilders.termQuery("ownedBy.id", idName.getId()), ScoreMode.Total))
        .should(QueryBuilders.nestedQuery("updatedBy",
            QueryBuilders.termQuery("updatedBy.id", idName.getId()), ScoreMode.Total))
        .should(QueryBuilders.nestedQuery("importedBy",
            QueryBuilders.termQuery("importedBy.id", idName.getId()), ScoreMode.Total));




    UpdateByQueryRequest request = new UpdateByQueryRequest(index);
    request.setQuery(boolQuery);
    Map<String, Object> param = new HashMap<>();
    param.put("name",idName.getName());
    param.put("id",idName.getId());

    String scriptText =
            "if (ctx._source.containsKey('createdBy') && ctx._source.createdBy != null && ctx._source.createdBy.id == params.id) " +
            "  { ctx._source.createdBy.name = params.name; } " +
            "if (ctx._source.containsKey('ownedBy') && ctx._source.ownedBy != null && ctx._source.ownedBy.id == params.id) " +
            "  { ctx._source.ownedBy.name = params.name; } " +
            "if (ctx._source.containsKey('updatedBy') && ctx._source.updatedBy != null && ctx._source.updatedBy.id == params.id) " +
            "  { ctx._source.updatedBy.name = params.name; } " +
            "if (ctx._source.containsKey('importedBy') && ctx._source.importedBy != null && ctx._source.importedBy.id == params.id) " +
            "  { ctx._source.importedBy.name = params.name; }";

    Script script = new Script(ScriptType.INLINE, "painless", scriptText, param);
    request.setScript(script);

    try {
      BulkByScrollResponse bulkByScrollResponse = client.updateByQuery(request, RequestOptions.DEFAULT);
      int batches = bulkByScrollResponse.getBatches();
      log.info("{} userName {} updated request completed with success ,total batch update are {} ",entityType,idName.getName(), batches);
    } catch (IOException e) {
      log.info("{} idName update request failed with id {} and name {} completed with error {}, {} ",entityType, idName.getId(), idName.getName(), e.getMessage(),e);
    }
  }


  private UpdateByQueryRequest getUpdateRequest(String index, String esFieldName, IdName idName, FieldType fieldType) {
    if(FieldType.MULTI_PICKLIST.equals(fieldType)){

      UpdateByQueryRequest request = new UpdateByQueryRequest(index);
      QueryBuilder query = QueryBuilders.termQuery(esFieldName+".id",idName.getId());
      NestedQueryBuilder source = QueryBuilders.nestedQuery(esFieldName, query, ScoreMode.Total);
      request.setQuery(source);
      Map<String, Object> param = new HashMap<>();
      param.put("name",idName.getName());
      param.put("id",idName.getId());

      StringBuilder sb = new StringBuilder()
          .append("for(int i=0; i<ctx._source.").append(esFieldName).append(".size(); i++)")
          .append("{ if (ctx._source.").append(esFieldName).append(".get(i).get('id') == params.id) ")
          .append("{ ctx._source.").append(esFieldName).append(".get(i).put('name',params.name);}}");

      Script script = new Script(ScriptType.INLINE,"painless",sb.toString(),param);
      request.setScript(script);
      return request;

    }
    UpdateByQueryRequest request = new UpdateByQueryRequest(index);
    QueryBuilder query = QueryBuilders.termQuery(esFieldName+".id",idName.getId());
    NestedQueryBuilder source = QueryBuilders.nestedQuery(esFieldName, query, ScoreMode.Total);
    request.setQuery(source);
    Map<String, Object> param = new HashMap<>();
    param.put("name",idName.getName());
    Script script = new Script(ScriptType.INLINE,"painless","ctx._source."+esFieldName+".name = params.name",param);
    request.setScript(script);
    return request;
  }

}
