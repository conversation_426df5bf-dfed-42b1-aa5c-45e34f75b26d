package com.sell.search.es.mapping.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class IndexConfigurationByPlan {
  private String entity;
  private String indexTemplateName;
  private String documentName;
  @JsonCreator
  public IndexConfigurationByPlan(
      @JsonProperty("entity") String entity,
      @JsonProperty("indexTemplateName") String indexTemplateName,
      @JsonProperty("documentName") String documentName) {
    this.entity = entity;
    this.indexTemplateName = indexTemplateName;
    this.documentName = documentName;
  }
}
