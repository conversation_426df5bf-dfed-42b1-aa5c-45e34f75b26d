package com.sell.search.es.mapping;

import static com.sell.search.dto.mapper.SearchListMapper.log;

import com.sell.search.es.EsCustomFieldMappingServiceCache;
import com.sell.search.core.domain.EntityType;
import com.sell.search.field.domain.Field;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class EsCustomFieldMappingService {

  @Autowired
  private EsCustomFieldMappingServiceCache esCustomFieldMappingServiceCache;
  @Autowired private CustomFieldEsFieldMappingRepository customFieldEsFieldMappingRepository;

  @Autowired private TenantIndexAliasMappingRepository tenantIndexAliasMappingRepository;

  @Autowired private EsCustomFieldFacade esCustomFieldFacade;

  public CustomFieldEsFieldMapping mapEsFieldAndCustomField(Field field) {

    TenantIndexAliasMapping mapping =
        tenantIndexAliasMappingRepository.findOneByTenantId(field.getTenantId());

    CustomFieldEsFieldMapping fieldEsFieldMapping = esCustomFieldMappingServiceCache.getCustomFieldEsFieldMappingByFieldId(field.getIdPk());
    if (fieldEsFieldMapping != null) {
      return fieldEsFieldMapping;
    }

    List<EsCustomField> unMappedEsFields = esCustomFieldFacade.getUnMappedESFields(mapping, field);

    Optional<EsCustomField> esCustomFieldOptional = unMappedEsFields.stream().findFirst();

    CustomFieldEsFieldMapping customFieldEsFieldMapping = getCustomFieldEsFieldMapping(field);

    if (esCustomFieldOptional.isPresent()) {
      log.info(
          "Unmapped ES field is present so mapping custom field with field id:{} and entity type:{} and field type:{},"
              + "by Tenant Id:{} with existing ES field",
          field.getId(),
          field.getEntityType(),
          field.getFieldType(),
          field.getTenantId());
      customFieldEsFieldMapping.setEsCustomField(esCustomFieldOptional.get());
    } else {
      EsCustomField esCustomField = esCustomFieldFacade.createField(mapping, field);
      customFieldEsFieldMapping.setEsCustomField(esCustomField);
    }
    return esCustomFieldMappingServiceCache.saveMapping(customFieldEsFieldMapping);
  }

  private CustomFieldEsFieldMapping getCustomFieldEsFieldMapping(Field field) {
    CustomFieldEsFieldMapping customFieldEsFieldMapping = new CustomFieldEsFieldMapping();
    customFieldEsFieldMapping.setField(field);
    customFieldEsFieldMapping.setEntityType(field.getEntityType());
    customFieldEsFieldMapping.setTenantId(field.getTenantId());
    return customFieldEsFieldMapping;
  }

  public Map<String, String> findCustomFieldToEsFieldMappingByTenantIdAndEntityType(
      long tenantId, EntityType entityType) {
    return esCustomFieldMappingServiceCache.findCustomFieldtoEsFieldMappingByTenantIdAndEntityType(tenantId,entityType);
  }

  public Map<String, String> findEsCustomFieldToEsCustomFieldMappingByTenantIdAndEntityType(long tenantId, EntityType entityType) {
    return esCustomFieldMappingServiceCache.findEsCustomFieldtoEsCustomFieldMappingByTenantIdAndEntityType(tenantId, entityType);
  }

  public void refresh(long tenantId, String entity) {
    esCustomFieldMappingServiceCache.refreshCustomFieldToEsFieldMapping(tenantId,EntityType.valueOf(entity.toUpperCase()));
    esCustomFieldMappingServiceCache.refreshEsFieldToCustomFieldMapping(tenantId,EntityType.valueOf(entity.toUpperCase()));
  }
}
