package com.sell.search.es.mapping.domain;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdNameRequest implements Indexable {
  private final long id;
  private final long tenantId;
  private final String name;
  private final String forType;

  @JsonCreator
  public IdNameRequest(
      @JsonProperty("id") long id,
      @JsonProperty("tenantId") long tenantId,
      @JsonProperty("name") String name,
      @JsonProperty("forType") String forType) {
    this.id = id;
    this.tenantId = tenantId;
    this.forType = forType;
    this.name = name;
  }

  @JsonIgnore
  public String getDocumentId(){
    return String.format("%s_%s_%s",forType,tenantId,id);
  }
}
