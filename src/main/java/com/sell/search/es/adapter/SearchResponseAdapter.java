package com.sell.search.es.adapter;

import com.sell.search.es.abstraction.model.SearchResponse;
import org.elasticsearch.search.SearchHit;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Adapter to convert between different Elasticsearch response formats
 * and ensure consistent API responses regardless of the underlying ES version
 */
@Component
public class SearchResponseAdapter {

    /**
     * Convert our abstraction SearchResponse to the legacy format expected by existing controllers
     */
    public org.elasticsearch.action.search.SearchResponse adaptToLegacyFormat(SearchResponse abstractResponse) {
        // This is a complex conversion that would require creating a mock SearchResponse
        // For now, we'll return the original response and handle conversion in the service layer
        throw new UnsupportedOperationException("Direct conversion not implemented - use service layer adaptation");
    }

    /**
     * Convert SearchResponse to a generic Map format that can be used by controllers
     */
    public Map<String, Object> adaptToGenericFormat(SearchResponse response) {
        Map<String, Object> result = new HashMap<>();
        
        // Basic response metadata
        result.put("took", response.getTook());
        result.put("timed_out", response.isTimedOut());
        result.put("total_hits", response.getTotalHits());
        
        // Convert hits
        Map<String, Object> hits = new HashMap<>();
        hits.put("total", response.getTotalHits());
        hits.put("max_score", getMaxScore(response.getHits()));
        
        List<Map<String, Object>> hitsList = new ArrayList<>();
        for (SearchResponse.SearchHit hit : response.getHits()) {
            Map<String, Object> hitMap = new HashMap<>();
            hitMap.put("_id", hit.getId());
            hitMap.put("_index", hit.getIndex());
            hitMap.put("_type", hit.getType());
            hitMap.put("_score", hit.getScore());
            hitMap.put("_source", hit.getSource());
            hitsList.add(hitMap);
        }
        hits.put("hits", hitsList);
        
        result.put("hits", hits);
        
        // Add aggregations if present
        if (response.getAggregations() != null) {
            result.put("aggregations", response.getAggregations());
        }
        
        return result;
    }

    /**
     * Convert legacy SearchResponse to our generic format
     */
    public Map<String, Object> adaptLegacyToGenericFormat(org.elasticsearch.action.search.SearchResponse legacyResponse) {
        Map<String, Object> result = new HashMap<>();
        
        // Basic response metadata
        result.put("took", legacyResponse.getTook().millis());
        result.put("timed_out", legacyResponse.isTimedOut());
        result.put("total_hits", legacyResponse.getHits().getTotalHits());
        
        // Convert hits
        Map<String, Object> hits = new HashMap<>();
        hits.put("total", legacyResponse.getHits().getTotalHits());
        hits.put("max_score", legacyResponse.getHits().getMaxScore());
        
        List<Map<String, Object>> hitsList = new ArrayList<>();
        for (SearchHit hit : legacyResponse.getHits().getHits()) {
            Map<String, Object> hitMap = new HashMap<>();
            hitMap.put("_id", hit.getId());
            hitMap.put("_index", hit.getIndex());
            hitMap.put("_type", hit.getType());
            hitMap.put("_score", hit.getScore());
            hitMap.put("_source", hit.getSourceAsMap());
            hitsList.add(hitMap);
        }
        hits.put("hits", hitsList);
        
        result.put("hits", hits);
        
        // Add aggregations if present
        if (legacyResponse.getAggregations() != null) {
            result.put("aggregations", legacyResponse.getAggregations().asMap());
        }
        
        return result;
    }

    /**
     * Ensure response format consistency between versions
     */
    public Map<String, Object> normalizeResponse(Map<String, Object> response) {
        // Normalize field names and structure to ensure consistency
        Map<String, Object> normalized = new HashMap<>(response);
        
        // Ensure consistent field naming
        if (normalized.containsKey("took")) {
            Long took = (Long) normalized.get("took");
            normalized.put("took", took);
        }
        
        // Ensure hits structure is consistent
        if (normalized.containsKey("hits")) {
            Map<String, Object> hits = (Map<String, Object>) normalized.get("hits");
            if (hits.containsKey("total")) {
                // Handle different total formats between ES versions
                Object total = hits.get("total");
                if (total instanceof Map) {
                    // ES 7+ format: {"value": 123, "relation": "eq"}
                    Map<String, Object> totalMap = (Map<String, Object>) total;
                    hits.put("total", totalMap.get("value"));
                }
                // ES 6.x format is already a number
            }
        }
        
        return normalized;
    }

    private Float getMaxScore(List<SearchResponse.SearchHit> hits) {
        if (hits == null || hits.isEmpty()) {
            return null;
        }
        
        float maxScore = 0.0f;
        for (SearchResponse.SearchHit hit : hits) {
            if (hit.getScore() > maxScore) {
                maxScore = hit.getScore();
            }
        }
        return maxScore;
    }
}
