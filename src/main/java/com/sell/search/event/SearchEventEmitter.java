package com.sell.search.event;


import com.sell.search.core.event.CoreEventEmitter;
import com.sell.search.event.listener.MessageListenerRouter;
import javax.annotation.PostConstruct;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.MessageListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

/**
 * Created by HemantS on 04-11-2016. Base configuration for event emitter/listner
 */
@Configuration
public class SearchEventEmitter extends CoreEventEmitter {

  @Override
  public String getServiceName() {
    return "search";
  }

  @Autowired
  private AmqpAdmin rabbitAdmin;

  @Autowired
  private RabbitTemplate rabbitTemplate;

  MessageListenerRouter messageListenerRouter;

  @PostConstruct
  public void initMessageListenerRouter() {
    messageListenerRouter = new MessageListenerRouter();
  }


  @Override
  public void on(String serviceName, String eventName, MessageListener listener) {

    super.on(serviceName, eventName, messageListenerRouter);
    messageListenerRouter.addListener(eventName, listener);
  }

}
