package com.sell.search.core.annotation;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created by hemants on 25/03/19.
 */
@Target({ ElementType.METHOD, ElementType.ANNOTATION_TYPE, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Parameter(
    name = "page",
    description = "Results page you want to retrieve (0..N)",
    in = ParameterIn.QUERY,
    schema = @Schema(type = "integer", format = "int32")
)
@Parameter(
    name = "size",
    description = "Number of records per page.",
    in = ParameterIn.QUERY,
    schema = @Schema(type = "integer", format = "int32")
)
@Parameter(
    name = "sort",
    description = "Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.",
    in = ParameterIn.QUERY,
    schema = @Schema(type = "string", example = "property,asc")
)
public @interface ApiPageable {
}
