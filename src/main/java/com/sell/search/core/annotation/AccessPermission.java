package com.sell.search.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Marks entities with access permission name applicable for them.
 *
 * <AUTHOR>
 *
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface AccessPermission {
  /**
   * Returns the access permission applicable for the class.
   *
   * @return the permission
   */
  String value();
}
