package com.sell.search.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * Our custom JPARepository implementation will ignore owner filter & permissionRules which apply on ownership for a class which is annotated by this
 * annotation.
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface IgnoreOwnerFilter {
  //
}
