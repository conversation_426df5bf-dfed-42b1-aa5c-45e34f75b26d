package com.sell.search.core.repository;


import com.sell.search.core.client.EntityShareAccessResolver;
import com.sell.search.core.domain.EntityType;
import com.sell.search.core.domain.ErrorCodes;
import com.sell.search.core.domain.ICustomFindSpec;
import com.sell.search.core.domain.PermissionAction;
import com.sell.search.core.dto.AccessDTO;
import com.sell.search.core.exception.BaseException;
import com.sell.search.core.utils.BeanUtil;
import com.sell.search.core.utils.ClassUtil;
import com.sell.search.core.utils.EntityUtil;
import com.sell.search.core.utils.SecurityUtil;
import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.domain.Specifications;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.util.Pair;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * Created by hemants on 01/05/19.
 */
@Slf4j
public class SharableBaseRepositoryImpl<T, ID extends Serializable> extends BaseRepositoryImpl<T, ID> {

  private static final String FIELD_PARAM_RECORD_ID = "id";
  private static final String FIELD_PARAM_OWNER_ID = "ownerId";
  private static final String FIELD_GETTER_OWNER_ID = "getOwnerId";

  private EntityShareAccessResolver sharableEntityClient;

  private final EntityType entityType;
  private final boolean isOwnerIdApplicable;
  /**
   * Constructor used by repository factory to create implementation instance for all jpa repositories
   */
  public SharableBaseRepositoryImpl(JpaEntityInformation<T, ?> entityInformation, EntityManager entityManager) {
    super(entityInformation, entityManager);
    isOwnerIdApplicable = ClassUtil.hasField(getDomainClass(), FIELD_GETTER_OWNER_ID);

    entityType = EntityUtil.getEntityType(getDomainClass());
    if (entityType == null)
      log.warn("EntityType not found: {}, share rule resolution will not apply for this entity", getDomainClass().getSimpleName());
  }

  /**
   * Special method for updating owner<br>
   * ** this is required since simple permission authorization is done against current ownerId
   *
   * @param entity
   * @param ownerIdToUpdate
   * @return
   */
  @Override
  @Transactional
  public <S extends T> S updateEntityOwner(S entity, Long ownerIdToUpdate) {
    // user should be owner of the entity for updating ownership
    // allow if the current user has updateAll (Admin) permissions
    if (!hasPermission(PermissionAction.UPDATE_ALL)) {
      Object ownerId = ClassUtil.getObjectField(entity, FIELD_GETTER_OWNER_ID);
      if (!SecurityUtil.getUserId().equals(ownerId))
        throw new BaseException(ErrorCodes.COMMON_RECORD_PERMISSION_ERROR);
    }

    try {
      // Set the new ownerId
      entity.getClass().getMethod("setOwnerId", Long.class).invoke(entity, ownerIdToUpdate);
      S result = em.merge(entity);
      flush();
      return result;
    } catch (SecurityException | IllegalArgumentException | NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
      log.error("Unable to update owner", e);
      throw new BaseException(ErrorCodes.COMMON_OWNER_NOT_APPLICABLE);
    }
  }

  protected Pair<Specification<T>, AccessDTO> getBaseSpecification(ID id, ICustomFindSpec<T> customFindSpec) {
    return super.resolveSelectSpecification(id, customFindSpec);
  }

  @Override
  protected Pair<Specification<T>, AccessDTO> resolveSelectSpecification(ID id, ICustomFindSpec<T> customFindSpec) {
    Pair<Specification<T>, AccessDTO> baseSpecResponse = getBaseSpecification(id, customFindSpec);

    // No record level permission check required for Public READ entities
    if (!isOwnerIdApplicable
        || (entityType != null && entityType.getTenantAccess() != null && entityType.getTenantAccess().contains(PermissionAction.READ)))
      return baseSpecResponse;

    // Resolve shared access for the current user
    AccessDTO resolvedAccessDTO = resolveSharedAccess(PermissionAction.READ);

    // No record level permission check required for users with readAll permission
    if (hasPermission(PermissionAction.READ_ALL)) {
      return Pair.of(baseSpecResponse.getFirst(), resolvedAccessDTO);
    }

    // Get shared specification
    Specification<T> sharedSpec = getSharedSpecification(resolvedAccessDTO);

    if (customFindSpec != null) { // Merge shared specification with provided custom specification
      sharedSpec = customFindSpec.specification(sharedSpec);
    }

    // combine shared & base spec
    Specification<T> querySpec = Specifications.where(baseSpecResponse.getFirst()).and(sharedSpec);

    return Pair.of(querySpec, resolvedAccessDTO);
  }

  private Specification<T> getSharedSpecification(AccessDTO resolvedAccessDTO) {
    return (Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
      List<Predicate> shareAccessPredicates = new ArrayList<>();
      List<Long> ownerIds = new ArrayList<>();
      List<Long> recordIds = new ArrayList<>();

      // user should see their profile even if they are not admin
      Long loggedInUserId = SecurityUtil.getUserId();
      if (entityType == EntityType.USER)
        recordIds.add(loggedInUserId);

      // user should see what they own plus what they have been shared
      ownerIds.add(loggedInUserId);

      if (resolvedAccessDTO != null) {
        if (!ObjectUtils.isEmpty(resolvedAccessDTO.fetchOwnerIds())) {
          ownerIds.addAll(resolvedAccessDTO.fetchOwnerIds());
        }

        if (!ObjectUtils.isEmpty(resolvedAccessDTO.fetchRecordIds())) {
          recordIds.addAll(resolvedAccessDTO.fetchRecordIds());
        }
      }

      shareAccessPredicates.add(root.get(FIELD_PARAM_OWNER_ID).in(ownerIds));
      if (!ObjectUtils.isEmpty(recordIds))
        shareAccessPredicates.add(root.get(FIELD_PARAM_RECORD_ID).in(recordIds));

      return cb.or(shareAccessPredicates.toArray(new Predicate[shareAccessPredicates.size()]));
    };
  }

  @Override
  protected void validateSharedPermissionAction(T entity, PermissionAction permissionAction) {
    Object ownerId = ClassUtil.getObjectField(entity, FIELD_GETTER_OWNER_ID);
    // ownerId does not exists then its tenant access, service level authentication will apply.
    // ownerId matches the current loggedIn user allow access. (IMP: Assuming User profile permission Authentication is checked at service level.)
    if (ownerId == null || SecurityUtil.getUserId().equals(ownerId))
      return;

    // entity is marked for Tenant Level(Public) Access for the permissionAction
    if (entityType != null && entityType.getTenantAccess() != null && entityType.getTenantAccess().contains(permissionAction))
      return;

    // User should have READ & UPDATE on their record (USER) implicitly.
    if (entityType == EntityType.USER && SecurityUtil.getUserId().equals(entityInformation.getId(entity))
        && permissionAction == PermissionAction.UPDATE)
      return;

    // Allow update if user has updateAll admin permissionAction.
    if (hasPermission(PermissionAction.UPDATE_ALL))
      return;

    AccessDTO resolvedAccessDTO = resolveSharedAccess(permissionAction);
    Object id = entityInformation.getId(entity);
    // id of the record is present in the resolvedAccessDTO id's
    if (!ObjectUtils.isEmpty(resolvedAccessDTO.fetchRecordIds()) && resolvedAccessDTO.fetchRecordIds().contains(id))
      return;

    // ownerId is present in the shared ownerIds
    if (!ObjectUtils.isEmpty(resolvedAccessDTO.fetchOwnerIds()) && resolvedAccessDTO.fetchOwnerIds().contains(ownerId))
      return;

    throw new BaseException(ErrorCodes.COMMON_RECORD_PERMISSION_ERROR);
  }

  @Override
  protected Set<PermissionAction> getTenantAccess() {
    return (null != entityType) ? entityType.getTenantAccess() : null;
  }

  private AccessDTO resolveSharedAccess(PermissionAction permissionAction) {
    AccessDTO accessDTO = null;

    if (entityType != null && entityType.isSharable())
      accessDTO = getSharableEntityClient().resolveAccess(entityType, permissionAction);

    return accessDTO != null ? accessDTO : new AccessDTO();
  }

  private EntityShareAccessResolver getSharableEntityClient() {
    if (sharableEntityClient != null)
      return sharableEntityClient;
    return BeanUtil.getBean(EntityShareAccessResolver.class);
  }

  private boolean hasPermission(PermissionAction permissionAction) {
    return SecurityUtil.isLoggedInUser() && SecurityUtil.hasPermissionAction(applicableAccessPermission, permissionAction);
  }
}
