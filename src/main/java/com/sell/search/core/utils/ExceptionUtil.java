package com.sell.search.core.utils;

import com.sell.search.core.domain.ErrorCodes;
import com.sell.search.core.exception.AuthenticationException;
import com.sell.search.core.exception.BaseException;
import com.sell.search.core.exception.ResourceNotFoundException;
import java.util.Locale;
import org.springframework.context.ApplicationContext;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpStatus;

public class ExceptionUtil {

  private static Object[] getUniqueFieldsFromUniqueIndexName(String uniqueIndexName) {
    if (uniqueIndexName == null || uniqueIndexName.isEmpty()) {
      return new String[0];
    }
    String[] fields = uniqueIndexName.split("_");
    return getFieldsAsSingleArgument(fields);
  }

  private static Object[] getFieldsAsSingleArgument(String[] fields) {
    String[] args = null;

    if (fields.length == 2) {
      args = new String[1];
      args[0] = fields[1];
    } else if (fields.length > 2) {
      StringBuilder arg = new StringBuilder();
      args = new String[2];
      args[0] = fields[1];
      for (int i = 2; i < fields.length; i++) {
        if (i < (fields.length - 2)) {
          arg.append(fields[i]).append(", ");
        } else if (i == (fields.length - 2)) {
          arg.append(fields[i]).append(" and ");
        } else {
          arg.append(fields[i]);
        }
      }
      args[1] = arg.toString();
    } else {
      return new String[0];
    }
    return args;
  }

  public static String getDatabaseErrorMessageForConstraintViolationException(ApplicationContext applicationContext, String constraintName) {
    String errorMessage;
    Object[] args = getUniqueFieldsFromUniqueIndexName(constraintName);
    Locale locale = LocaleContextHolder.getLocale();
    if (args.length == 0) {
      errorMessage = applicationContext.getMessage(ErrorCodes.COMMON_DUPLICATE_RESOURCE.getMessage(), new Object[] {"Entity"}, locale);
    } else if (args.length == 1) {
      errorMessage = applicationContext.getMessage(ErrorCodes.COMMON_DUPLICATE_RESOURCE.getMessage(), args, locale);
    } else {
      errorMessage = applicationContext.getMessage(ErrorCodes.COMMON_DUPLICATE_RESOURCE_DETAILED.getMessage(), args, locale);
    }
    return errorMessage;
  }

  public static HttpStatus getHttpStatus(BaseException exception) {
    if (exception instanceof ResourceNotFoundException) {
      return HttpStatus.NOT_FOUND;
    } else if (exception instanceof AuthenticationException) {
      return HttpStatus.UNAUTHORIZED;
    }
    return HttpStatus.BAD_REQUEST;
  }
}
