package com.sell.search.core.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.search.core.domain.Action;
import com.sell.search.core.domain.CoreAuthentication;
import com.sell.search.core.domain.ErrorCodes;
import com.sell.search.core.domain.PermissionAction;
import com.sell.search.core.domain.PermissionDTO;
import com.sell.search.core.domain.TenantCreatedEventData;
import com.sell.search.core.exception.BaseException;
import java.io.IOException;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;

/**
 * Created by hemants on 14/02/19. Utility class so that we don't have to write repetitive code to access authentication for tenant id and user id
 */
@Slf4j
public class SecurityUtil {

  private SecurityUtil() {
    //
  }

  public static void loginAsTenantUser(Authentication coreAuthentication) {
    SecurityContextHolder.getContext().setAuthentication(coreAuthentication);
  }

  public static void loginAsTenantUser(TenantCreatedEventData tenantMessage) {
    CoreAuthentication coreAuthentication = new CoreAuthentication(Long.toString(tenantMessage.getUserId()),
        Long.toString(tenantMessage.getTenantId()), tenantMessage.getPermissions(), tenantMessage.getJwtToken());
    SecurityContextHolder.getContext().setAuthentication(coreAuthentication);
  }

  public static boolean isLoggedInUser() {
    try {
      CoreAuthentication coreAuth = (CoreAuthentication) SecurityContextHolder.getContext().getAuthentication();
      return !StringUtils.isEmpty(coreAuth.getUserId());
    } catch (Exception e) {
      // catching the exception in case of anonymous user.
      log.debug("User not loggedin");
      return false;
    }
  }

  public static CoreAuthentication getAuthentication() {
    try {
      return (CoreAuthentication) SecurityContextHolder.getContext().getAuthentication();
    } catch (ClassCastException cce) {
      log.error("User not loggedin Error");
      throw new BaseException(ErrorCodes.COMMON_INVALID_TOKEN);
    }
  }

  public static List<PermissionDTO> getUserPermissions() {
    return getAuthentication().getPermissions().stream().sorted(Comparator.comparing(PermissionDTO::getId)).collect(Collectors.toList());
  }

  public static PermissionDTO getUserPermission(String permissionName) {
    return getAuthentication().getPermissions().stream().collect(Collectors.toMap(PermissionDTO::getName, permissionDTO -> permissionDTO))
        .get(permissionName);
  }

  public static Action getUserPermissionAction(String permissionName) {
    PermissionDTO userPermission = getUserPermission(permissionName);
    if (userPermission != null)
      return userPermission.getAction();
    return null;
  }

  @SuppressWarnings("unchecked")
  public static boolean hasPermissionAction(String permissionName, PermissionAction permissionAction) {
    PermissionDTO userPermission = getUserPermission(permissionName);
    if (userPermission != null) {
      try {
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Boolean> userPermissionActions = objectMapper.readValue(objectMapper.writeValueAsString(userPermission.getAction()), Map.class);
        return userPermissionActions.get(permissionAction.getAction()) != null && userPermissionActions.get(permissionAction.getAction());
      } catch (IOException e) {
        log.error("Parsing user permissionAction {}", userPermission.getAction());
        throw new BaseException(ErrorCodes.COMMON_INTERNAL_ERROR);
      }
    }
    return false;
  }

  /**
   * Ideally, it should never by null, but temporarily throwing runtime exception.
   */
  public static Long getTenantId() {
    return toLong(checkNull(getAuthentication().getTenantId()));
  }

  /**
   * Ideally, it should never by null, but temporarily throwing runtime exception.
   */
  public static Long getUserId() {
    return toLong(checkNull(getAuthentication().getUserId()));
  }

  public static String getJwtToken() {
    String jwtToken = getAuthentication().getJwtToken();
    return checkNull(jwtToken);
  }

  public static String getJwtBearerToken() {
    String jwtToken = getAuthentication().getJwtToken();
    return "Bearer " + checkNull(jwtToken);
  }

  private static Long toLong(String id) {
    return Long.valueOf(id);
  }

  /**
   * It first checks if the id is null or empty if it not then convert it into long if it is null then raises an exception.
   */
  private static String checkNull(String id) {
    if (!StringUtils.isEmpty(id)) {
      return id;
    }
    log.error("token, tenant_id or user_id is null, You should not be getting this error. Some problem in token");
    throw new BaseException(ErrorCodes.COMMON_PERMISSION_ERROR);
  }
}
