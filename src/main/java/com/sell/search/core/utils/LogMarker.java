package com.sell.search.core.utils;

import java.util.Arrays;
import org.slf4j.Marker;
import org.slf4j.MarkerFactory;

public final class LogMarker {
  private LogMarker() {
    //
  }

  public static final Marker ACCESS_DENIED = MarkerFactory.getMarker("ACCESS_DENIED");
  public static final Marker CONSTRAINT_VOILATION = MarkerFactory.getMarker("CONSTRAINT_VOILATION");
  public static final Marker VALIDATION = MarkerFactory.getMarker("VALIDATION");
  public static final Marker DATA_CONSTRAINT = MarkerFactory.getMarker("DATA_CONSTRAINT");
  public static final Marker INVALID_JSON = MarkerFactory.getMarker("INVALID_JSON");
  public static final Marker INVALID_METHOD_ARG = MarkerFactory.getMarker("INVALID_METHOD_ARG");
  public static final Marker METHOD_NOT_SUPPORTED = MarkerFactory.getMarker("METHOD_NOT_SUPPORTED");
  public static final Marker INTER_SERVICE_CALL = MarkerFactory.getMarker("INTER_SERVICE_CALL");
  public static final Marker TENANT_REG = MarkerFactory.getMarker("TENANT_REG");

  public static Marker append(Marker marker, String... name) {
    Arrays.asList(name).forEach(n -> marker.add(MarkerFactory.getMarker(n)));
    return marker;
  }
}
