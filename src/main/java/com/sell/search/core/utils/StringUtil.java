package com.sell.search.core.utils;

import java.util.Arrays;
import java.util.stream.Collectors;
import org.apache.commons.lang.StringUtils;

public class StringUtil {

  public static String join(String delimiter, String... inputStrings) {
    if (null == inputStrings) {
      return "";
    }
    return Arrays.stream(inputStrings).filter(StringUtils::isNotBlank).map(String::trim).collect(Collectors.joining(delimiter));
  }
}
