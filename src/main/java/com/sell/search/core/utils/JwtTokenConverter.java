package com.sell.search.core.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.search.core.domain.CoreAccessToken;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;

/**
 * Created by hemants on 04/01/19.
 */
@Slf4j
public class JwtTokenConverter {

    private final String issuer;
    private final String key;

    public JwtTokenConverter(String issuer, String key) {
        this.issuer = issuer;
        this.key = key;
    }

    /**
     * Convert CoreAccessToken to JWT token in string format
     * @param coreAccessToken
     * @return
     * @throws UnsupportedEncodingException
     */
    public String convertToken(CoreAccessToken coreAccessToken) throws UnsupportedEncodingException {
        return Jwts.builder()
                .setIssuer(issuer)
                //.setSubject(user.getEmail())
                .claim("data", coreAccessToken)
                .signWith(
                        SignatureAlgorithm.HS256,
            key.getBytes(StandardCharsets.UTF_8))
                .compact();
    }

    /**
     * Convert JWT token in string format to CoreAccessToken
     * @param jwtToken
     * @return
     * @throws UnsupportedEncodingException
     */
    public CoreAccessToken getAccessTokenFromJwt(String jwtToken) throws UnsupportedEncodingException {
    Jws<Claims> claims = Jwts.parser().setSigningKey(key.getBytes(StandardCharsets.UTF_8)).parseClaimsJws(jwtToken);
        Object body = claims.getBody().get("data");
        final ObjectMapper mapper = new ObjectMapper();
        return mapper.convertValue(body, CoreAccessToken.class);
    }

    /**
     * Validate the signated with configure key.
     * @param jwtToken
     * @return
     */
    public Jws<Claims> getClaims(String jwtToken) {
    return Jwts.parser().setSigningKey(key.getBytes(StandardCharsets.UTF_8)).parseClaimsJws(jwtToken);
    }
}
