package com.sell.search.core.utils;

import java.lang.reflect.InvocationTargetException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

@Slf4j
public class ClassUtil {

  private ClassUtil() {
    //
  }

  /**
   * Using reflection checks if the given annotation is present in the class.
   *
   * @param annotationClazz
   * @return True if annotation is used in class
   */
  @SuppressWarnings({"unchecked", "rawtypes"})
  public static boolean hasAnnotation(Class clazz, Class annotationClazz) {
    return !ObjectUtils.isEmpty(clazz.getDeclaredAnnotationsByType(annotationClazz));
  }

  /**
   * Utility method to check if a field exists using reflection
   *
   * @param methodName of getter/setter
   * @return True if method exists
   */
  @SuppressWarnings({"unchecked", "rawtypes"})
  public static boolean hasField(Class clazz, String methodName) {
    try {
      return clazz.getMethod(methodName) != null;
    } catch (SecurityException | IllegalArgumentException | NoSuchMethodException e) {
      log.debug("Method {}, doesnt exist for entity {}", methodName, clazz.getSimpleName());
      return false;
    }
  }

  /**
   * Using reflection & getter method fetches data from entity
   *
   * @param obj
   * @param Getter methodName
   * @return value Object
   */
  public static Object getObjectField(Object obj, String getterMethodName) {
    try {
      return obj.getClass().getMethod(getterMethodName).invoke(obj);
    } catch (SecurityException | IllegalArgumentException | IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
      log.debug("Method {}, doesnt exist for entity {}", getterMethodName, obj.getClass().getSimpleName());
      return null;
    }
  }
}
