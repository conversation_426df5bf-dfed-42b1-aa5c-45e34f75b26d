package com.sell.search.core.exception.handler;

import com.sell.search.core.domain.ErrorCodes;
import com.sell.search.core.domain.ErrorResource;
import com.sell.search.core.domain.FieldErrorResource;
import com.sell.search.core.exception.APIException;
import com.sell.search.core.exception.BaseException;
import com.sell.search.core.utils.ExceptionUtil;
import com.sell.search.core.utils.LogMarker;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import javax.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

/**
 * Created by hemants on 11/01/19. The main exception handling class. This is where i18n message key is search and converted based upon locale.
 */
@Slf4j
public class CoreExceptionHandler extends ResponseEntityExceptionHandler {

  @Autowired
  private ApplicationContext applicationContext;

  @ExceptionHandler(AccessDeniedException.class)
  protected ResponseEntity<Object> handleAccessDeniedException(RuntimeException e, WebRequest request) {
    log.error(LogMarker.ACCESS_DENIED, "", e);

    HttpHeaders headers = getRequiredHeaders();
    Locale locale = LocaleContextHolder.getLocale();
    ErrorResource error = new ErrorResource(ErrorCodes.COMMON_PERMISSION_ERROR.getCode(),
        applicationContext.getMessage(ErrorCodes.COMMON_PERMISSION_ERROR.getMessage(), null, locale));
    return handleExceptionInternal(e, error, headers, HttpStatus.UNAUTHORIZED, request);
  }

  @ExceptionHandler(ConstraintViolationException.class)
  protected ResponseEntity<Object> handleConstraintViolationException(RuntimeException e, WebRequest request) {
    log.error(LogMarker.CONSTRAINT_VOILATION, "", e);

    HttpHeaders headers = getRequiredHeaders();
    Locale locale = LocaleContextHolder.getLocale();
    ErrorResource error = new ErrorResource(ErrorCodes.COMMON_VALIDATION_ERROR.getCode(),
        applicationContext.getMessage(ErrorCodes.COMMON_VALIDATION_ERROR.getMessage(), null, locale));

    ConstraintViolationException exception = ((ConstraintViolationException) e);
    List<FieldErrorResource> fieldErrors = exception.getConstraintViolations().stream()
        .map(v -> new FieldErrorResource().field(v.getPropertyPath().toString()).message(v.getMessage())).collect(Collectors.toList());
    error.fieldErrors(fieldErrors);
    return handleExceptionInternal(e, error, headers, HttpStatus.BAD_REQUEST, request);
  }

  @ExceptionHandler(BaseException.class)
  protected ResponseEntity<Object> handleBaseExceptions(RuntimeException e, WebRequest request) {
    log.error(LogMarker.VALIDATION, e.getMessage());

    HttpHeaders headers = getRequiredHeaders();
    Locale locale = LocaleContextHolder.getLocale();
    BaseException exception = (BaseException) e;

    ErrorResource error = new ErrorResource(exception.getErrorResource().getCode(), getLocalisedErrorMessage(exception, locale));
    error.setFieldErrors(exception.getErrorResource().getFieldErrors().stream()
        .map(err -> new FieldErrorResource(err.getField(), applicationContext.getMessage(err.getMessage(), err.getArgs(), locale)))
        .collect(Collectors.toList()));

    HttpStatus httpStatus = ExceptionUtil.getHttpStatus(exception);

    return handleExceptionInternal(e, error, headers, httpStatus, request);
  }

  @ExceptionHandler(APIException.class)
  protected ResponseEntity<Object> handleAPIException(RuntimeException e, WebRequest request) {
    log.error(LogMarker.INTER_SERVICE_CALL, e.getMessage());

    HttpHeaders headers = getRequiredHeaders();
    Locale locale = LocaleContextHolder.getLocale();
    ErrorResource error = new ErrorResource(ErrorCodes.COMMON_INTERNAL_ERROR.getCode(),
        applicationContext.getMessage(ErrorCodes.COMMON_INTERNAL_ERROR.getMessage(), null, locale));

    return handleExceptionInternal(e, error, headers, HttpStatus.INTERNAL_SERVER_ERROR, request);
  }

  private String getLocalisedErrorMessage(BaseException baseException, Locale locale) {
    try {
    return applicationContext.getMessage(baseException.getErrorResource().getMessage(), baseException.getArgs(), locale);
    } catch (Exception e) {
      log.debug("Error finding locale error message for locale: {} & message Key: \"{}\"", locale, baseException.getErrorResource().getMessage());
      return baseException.getErrorResource().getMessage();
    }
  }

  @ExceptionHandler(DataIntegrityViolationException.class)
  protected ResponseEntity<Object> handleDataIntegrityViolationException(RuntimeException e, WebRequest request) {
    log.error(LogMarker.DATA_CONSTRAINT, "", e);
    HttpHeaders headers = getRequiredHeaders();
    ErrorResource error;

    if (e.getCause() instanceof org.hibernate.exception.ConstraintViolationException) {
      org.hibernate.exception.ConstraintViolationException constraintViolationException =
          (org.hibernate.exception.ConstraintViolationException) e.getCause();
      error = new ErrorResource(ErrorCodes.COMMON_DUPLICATE_RESOURCE.getCode(),
          ExceptionUtil.getDatabaseErrorMessageForConstraintViolationException(applicationContext, constraintViolationException.getConstraintName()));
    } else {
      error = new ErrorResource(ErrorCodes.COMMON_PERMISSION_ERROR.getCode(), "Database error");
    }

    return handleExceptionInternal(e, error, headers, HttpStatus.BAD_REQUEST, request);
  }

  @Override
  protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException e, HttpHeaders headers, HttpStatus status,
      WebRequest request) {
    log.error(LogMarker.INVALID_JSON, e.getMessage());

    ErrorResource error = new ErrorResource(ErrorCodes.COMMON_PERMISSION_ERROR.getCode(), "Invalid JSON");

    return handleExceptionInternal(e, error, headers, HttpStatus.BAD_REQUEST, request);
  }


  protected HttpHeaders getRequiredHeaders() {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);

    return headers;
  }

  @Override
  protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatus status,
      WebRequest request) {
    log.warn(LogMarker.INVALID_METHOD_ARG, ex.getMessage());

    List<FieldErrorResource> fieldErrors = ex.getBindingResult().getFieldErrors().stream()
        .map(e -> new FieldErrorResource(e.getField(), e.getDefaultMessage())).collect(Collectors.toList());
    HttpHeaders headers2 = getRequiredHeaders();
    Locale locale = LocaleContextHolder.getLocale();

    ErrorResource error = new ErrorResource(ErrorCodes.COMMON_VALIDATION_ERROR.getCode(),
        applicationContext.getMessage(ErrorCodes.COMMON_VALIDATION_ERROR.getMessage(), null, locale));
    error.setFieldErrors(fieldErrors);
    return handleExceptionInternal(ex, error, headers2, HttpStatus.BAD_REQUEST, request);
  }

  @Override
  protected ResponseEntity<Object> handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException ex, HttpHeaders headers,
      HttpStatus status, WebRequest request) {
    log.warn(LogMarker.METHOD_NOT_SUPPORTED, ex.getMessage());

    Locale locale = LocaleContextHolder.getLocale();
    ErrorResource error = new ErrorResource(ErrorCodes.COMMON_METHOD_NOT_SUPPORTED.getCode(),
        applicationContext.getMessage(ErrorCodes.COMMON_METHOD_NOT_SUPPORTED.getMessage(), null, locale));

    return handleExceptionInternal(ex, error, headers, HttpStatus.BAD_REQUEST, request);
  }
}
