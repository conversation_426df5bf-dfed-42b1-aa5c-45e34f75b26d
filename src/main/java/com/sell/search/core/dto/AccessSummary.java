package com.sell.search.core.dto;

import com.sell.search.core.domain.Action;
import java.util.Collections;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public class AccessSummary {

  private Map<Long, Action> accessByOwners;

  private Map<Long, Action> accessByRecords;

  public Map<Long, Action> getAccessByRecords() {
    if (!ObjectUtils.isEmpty(accessByRecords)) {
      return accessByRecords;
    }
    return Collections.emptyMap();
  }

  public Map<Long, Action> getAccessByOwners() {
    if (!ObjectUtils.isEmpty(accessByOwners)) {
      return accessByOwners;
    }
    return Collections.emptyMap();
  }
}
