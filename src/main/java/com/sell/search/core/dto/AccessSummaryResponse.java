package com.sell.search.core.dto;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

@AllArgsConstructor
@NoArgsConstructor
@Getter
public class AccessSummaryResponse {

  private AccessSummary task;
  private AccessSummary lead;
  private AccessSummary contact;
  private AccessSummary deal;

  public List<Long> getAccessByRecordFromAccessSummary(String entity) {
    switch (entity) {
      case "LEAD": {
        if (ObjectUtils.isNotEmpty(lead) && ObjectUtils.isNotEmpty(lead.getAccessByRecords())) {
          return new ArrayList<>(lead.getAccessByRecords().keySet());
        }
        return Collections.emptyList();
      }
      case "CONTACT": {
        if (ObjectUtils.isNotEmpty(contact) && ObjectUtils.isNotEmpty(contact.getAccessByRecords())) {
          return new ArrayList<>(contact.getAccessByRecords().keySet());
        }
        return Collections.emptyList();
      }
      case "DEAL": {
        if (ObjectUtils.isNotEmpty(deal) && ObjectUtils.isNotEmpty(deal.getAccessByRecords())) {
          return new ArrayList<>(deal.getAccessByRecords().keySet());
        }
        return Collections.emptyList();
      }
      case "TASK": {
        if (ObjectUtils.isNotEmpty(task) && ObjectUtils.isNotEmpty(task.getAccessByRecords())) {
          return new ArrayList<>(task.getAccessByRecords().keySet());
        }
        return Collections.emptyList();
      }
      default: {
        return Collections.emptyList();
      }

    }
  }

  public List<Long> getAccessByOwnerFromAccessSummaryForTask() {
    if (ObjectUtils.isNotEmpty(task) && ObjectUtils.isNotEmpty(task.getAccessByOwners())) {
      return new ArrayList<>(task.getAccessByOwners().keySet());
    }
    return Collections.emptyList();
  }

}