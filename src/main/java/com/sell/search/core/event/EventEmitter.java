package com.sell.search.core.event;

import org.springframework.amqp.core.MessageListener;

/**
 * Created by hemants on 19/02/19.
 * The interface exposing functionality irrespective of underline implementation
 */
public interface EventEmitter {
    void emit(CoreEvent event, Object context);
    void on(String serviceName,String eventName, MessageListener listener);
    void on(String queueName, String serviceName, String eventName, MessageListener listener);
    void sendDirect(String serviceName,String eventName, Object context);
    String getServiceName();
}

