package com.sell.search.core.config.client;

import com.sell.search.core.utils.SecurityUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TokenRequestInterceptor implements RequestInterceptor {

  @Override
  public void apply(RequestTemplate template) {
    log.debug("Token Request Interceptor called for inter-service communication");
    template.header("Authorization", SecurityUtil.isLoggedInUser() ? SecurityUtil.getJwtBearerToken() : null);
    template.header("accept-encoding", "identity");
  }
}
