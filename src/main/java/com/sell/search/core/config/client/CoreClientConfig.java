package com.sell.search.core.config.client;

import feign.Feign;
import feign.Logger;
import feign.okhttp.OkHttpClient;
import feign.slf4j.Slf4jLogger;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.web.HttpMessageConverters;
import org.springframework.cloud.netflix.feign.support.SpringDecoder;
import org.springframework.cloud.netflix.feign.support.SpringEncoder;
import org.springframework.cloud.netflix.feign.support.SpringMvcContract;
import org.springframework.context.annotation.Bean;

public class CoreClientConfig {

  /*
  @Bean
  public OkHttpClient okHttpClient() {
    return new OkHttpClient();
  }
  */
  @Bean
  public Feign.Builder getClientBuilder(
      ObjectFactory<HttpMessageConverters> messageConverters,
      @Value("${client.retry.period:100}") long period,
      @Value("${client.retry.max.period:1000}") long maxPeriod,
      @Value("${client.retry.max.attempts:5}") int maxAttempts) { // @formatter:off
    return Feign.builder()
        .client(new OkHttpClient())
        .encoder(new SpringEncoder(messageConverters))
        .decoder(new SpringDecoder(messageConverters))
        .contract(new SpringMvcContract())
        .errorDecoder(new CustomErrorDecoder())
        .requestInterceptor(new TokenRequestInterceptor())
        .logger(new Slf4jLogger(CoreClientConfig.class))
        .logLevel(Logger.Level.BASIC)
        .retryer(new CustomRetryer(period, maxPeriod, maxAttempts));
    // @formatter:on
  }
}
