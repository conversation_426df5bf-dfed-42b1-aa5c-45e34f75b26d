package com.sell.search.core.config;

import com.sell.search.core.security.HasAdminPermissionVoter;
import com.sell.search.core.security.HasPermissionVoter;
import java.util.ArrayList;
import java.util.List;
import org.springframework.security.access.AccessDecisionManager;
import org.springframework.security.access.AccessDecisionVoter;
import org.springframework.security.access.expression.method.ExpressionBasedPreInvocationAdvice;
import org.springframework.security.access.prepost.PreInvocationAuthorizationAdviceVoter;
import org.springframework.security.access.vote.AffirmativeBased;
import org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration;

/**
 * The configuration file responsible for checking if logged in user has valid permission or not. This file is being called whenever there is @secure
 * annotation to the method.<br>
 *
 * Created by hemants on 11/01/19.
 *
 */
public class CoreMethodSecurityConfig extends GlobalMethodSecurityConfiguration {
  @Override
  protected AccessDecisionManager accessDecisionManager() {
    List<AccessDecisionVoter<? extends Object>> decisionVoters = new ArrayList<>();

    ExpressionBasedPreInvocationAdvice expressionAdvice = new ExpressionBasedPreInvocationAdvice();
    expressionAdvice.setExpressionHandler(getExpressionHandler());
    decisionVoters.add(new PreInvocationAuthorizationAdviceVoter(expressionAdvice));

    decisionVoters.add(new HasPermissionVoter());
    decisionVoters.add(new HasAdminPermissionVoter());
    return new AffirmativeBased(decisionVoters);
  }
}
