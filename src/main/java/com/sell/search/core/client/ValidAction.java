package com.sell.search.core.client;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import javax.validation.Constraint;
import javax.validation.Payload;

@Target({ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ActionValidator.class)
public @interface ValidAction {

  String message() default "Uhoh! please choose at least one permission to share";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};

}
