package com.sell.search.core.client;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sell.search.core.domain.Action;
import com.sell.search.core.domain.EntityType;
import com.sell.search.core.dto.BaseResponse;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ShareRuleResponse extends BaseResponse {
  private static final long serialVersionUID = 1L;

  private Long id;

  private String name;
  private String description;

  private EntityType fromType;
  private Long fromId;
  private EntityType toType;
  private Long toId;
  private boolean shareAllRecords;
  private EntityType entityType;
  private Long entityId;
  private EntityType[] childEntities;
  private Long entityShareRuleId;

  @JsonIgnoreProperties(value = {"write", "readAll", "updateAll"})
  private Action actions;
}
