package com.sell.search.core.client;

import com.sell.search.core.domain.Action;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.support.SpringBeanAutowiringSupport;

public class ActionValidator implements ConstraintValidator<ValidAction, Action> {

  @Override
  public void initialize(ValidAction constraintAnnotation) {
    SpringBeanAutowiringSupport.processInjectionBasedOnCurrentContext(this);
  }

  @Override
  public boolean isValid(Action value, ConstraintValidatorContext context) {
    if (null == value) {
      return true;
    } else {
      return !ObjectUtils.nullSafeEquals(value, new Action());
    }
  }
}
