package com.sell.search.core.client;

import com.sell.search.core.constants.InputSize;
import com.sell.search.core.domain.Action;
import com.sell.search.core.domain.EntityType;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UpdateShareRuleRequest {

  @Schema( required = false, example = "Sharing my leads with my friend", description = "name for this share rule")
  @Size(max = InputSize.MAX_INPUT_SIZE, message = "Name must be between 5 and " + InputSize.MAX_INPUT_SIZE + " characters")
  private String name;

  @Schema( required = false, example = "Sharing my leads with my friend", description = "Description for this share rule")
  @Size(min = 0, max = InputSize.MAX_INPUT_SIZE_DESCRIPTION, message = "Description must be less than " + InputSize.MAX_INPUT_SIZE_DESCRIPTION
      + " characters")
  private String description;

  @Schema( required = false, example = "USER", allowableValues = "USER, TEAM", description = "Entity type (sharing access)")
  private EntityType fromType;

  @Schema( required = false, example = "1", description = "Entity id (sharing access)")
  private Long fromId;

  @Schema( required = false, example = "USER", allowableValues = "USER, TEAM", description = "Entity type (getting shared access)")
  private EntityType toType;

  @Schema( required = false, example = "1", description = "Entity id (getting shared access)")
  private Long toId;

  @Schema( required = false, example = "[\"TASK\", \"NOTE\"]", allowableValues = "TASK, NOTE, MEETING",
      description = "Valid child entities of shared entity")
  private EntityType[] childEntities;

  @Schema( required = false, example = "{\"read\":true}", description = "Permission set which is being shared")
  @ValidAction
  private Action actions;
}
