package com.sell.search.core.client;

import com.sell.search.core.domain.EntityType;
import com.sell.search.core.domain.PermissionAction;
import com.sell.search.core.dto.AccessDTO;
import com.sell.search.core.dto.EntityAccessDTO;
import com.sell.search.core.exception.APIException;
import com.sell.search.core.utils.LogMarker;
import feign.Feign;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Hidden
public class InternalShareRuleService implements InternalShareRuleContract, EntityShareAccessResolver {
  
  private final InternalShareRuleContract client;

  public InternalShareRuleService(String clientBasePath, Feign.Builder clientBuilder) {
    client = clientBuilder.target(InternalShareRuleContract.class, clientBasePath);
  }

  @Override
  public ShareRuleResponse createShareRule(EntityType entityType, Long entityId, CreateShareRuleRequest createShareRuleRequest) {
    if (entityId == null) {
      return client.createShareRuleForAllEntityTypeRecords(entityType, createShareRuleRequest);
    }
    return client.createShareRule(entityType, entityId, createShareRuleRequest);
  }

  @Override
  public ShareRuleResponse createShareRuleForAllEntityTypeRecords(EntityType entityType, CreateShareRuleRequest createShareRuleRequest) {
    return null;
  }

  public ShareRuleResponse getShareRule(EntityType entityType, Long shareRuleId) {
    return client.getShareRule(entityType, shareRuleId);
  }

  public ShareRuleResponse updateShareRule(EntityType entityType, Long entityId, Long shareRuleId, UpdateShareRuleRequest updateShareRuleRequest) {
    return client.updateShareRule(entityType, entityId, shareRuleId, updateShareRuleRequest);
  }

  public ShareRuleResponse updateShareRuleForAllEntityTypeRecords(EntityType entityType, Long shareRuleId,
      UpdateShareRuleRequest updateShareRuleRequest) {
    return client.updateShareRuleForAllEntityTypeRecords(entityType, shareRuleId, updateShareRuleRequest);
  }


  public void deleteShareRule(EntityType entityType, Long shareRuleId) {
    client.deleteShareRule(entityType, shareRuleId);
  }

  @Override
  public AccessDTO resolveAccess(EntityType entityType, PermissionAction permissionAction) {
    try {
      return client.resolveAccess(entityType, permissionAction);
    } catch (APIException e) {
      log.error(LogMarker.INTER_SERVICE_CALL, "Could not resolve shareRule access, shareRule would not apply", e);
      return null;
    }
  }

  @Override
  public EntityAccessDTO resolveAccessSummary(EntityType entityType, PermissionAction permission) {
    try {
      return client.resolveAccessSummary(entityType, permission);
    } catch (APIException e) {
      log.error(LogMarker.INTER_SERVICE_CALL, "Could not resolve shareRule access summary, shareRule would not apply", e);
      return null;
    }
  }
}
