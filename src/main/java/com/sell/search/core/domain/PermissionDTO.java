package com.sell.search.core.domain;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by hemants on 02/01/19.
 * The permission DTO for permission model.
 */
@Getter
@Setter
public class PermissionDTO implements Serializable{
	private static final long serialVersionUID = 1L;

	private long id;
    private String name;
    private String description;
    private Integer limits;
    private String units;
    private Action action;

    public PermissionDTO id(long id) {
        this.id = id;
        return this;
    }

    public PermissionDTO name(String name) {
        this.name = name;
        return this;
    }

    public PermissionDTO description(String description) {
        this.description = description;
        return this;
    }

    public PermissionDTO limits(Integer limits) {
        this.limits = limits;
        return this;
    }

    public PermissionDTO units(String units) {
        this.units = units;
        return this;
    }

    public PermissionDTO action(Action action) {
        this.action = action;
        return this;
    }
}
