package com.sell.search.core.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sell.search.core.annotation.FieldAttribute;
import com.sell.search.core.utils.SecurityUtil;
import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.PrePersist;
import javax.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@MappedSuperclass
public abstract class TenantOwnerAwareBaseEntity extends TenantAwareBaseEntity {

  @Column(nullable = false)
  @FieldAttribute(displayName = "Owner")
  private Long ownerId;

  @Transient
  @JsonIgnore
  private boolean explicitOwnerId;

  @PrePersist
  public void setExplicitOwner() {
    if (!isExplicitOwnerId())
      setOwnerId(SecurityUtil.getUserId());
  }
}
