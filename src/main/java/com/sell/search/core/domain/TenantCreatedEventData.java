package com.sell.search.core.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.Set;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * Created by hemants on 03/05/19.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TenantCreatedEventData {
  @NotNull
  private Long userId;
  @NotNull
  private Long tenantId;

  private String firstName;
  private String lastName;
  private Set<PermissionDTO> permissions;
  private String jwtToken;


  public TenantCreatedEventData userId(Long userId) {
    this.userId = userId;
    return this;
  }

  public TenantCreatedEventData tenantId(Long tenantId) {
    this.tenantId = tenantId;
    return this;
  }

  public TenantCreatedEventData permissions(Set<PermissionDTO> permissions) {
    this.permissions = permissions;
    return this;
  }


  public TenantCreatedEventData jwtToken(String jwtToken) {
    this.jwtToken = jwtToken;
    return this;
  }


  public TenantCreatedEventData firstName(String firstName) {
    this.firstName = firstName;
    return this;
  }

  public TenantCreatedEventData lastName(String lastName) {
    this.lastName = lastName;
    return this;
  }
}
