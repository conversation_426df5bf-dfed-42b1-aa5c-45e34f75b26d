package com.sell.search.core.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.ArrayList;
import java.util.Map;
import java.util.Set;
import lombok.Getter;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CoreAuthentication extends UsernamePasswordAuthenticationToken {

  private static final long serialVersionUID = 1L;
  private final Set<PermissionDTO> permissions;
  private final String tenantId;
  private final String jwtToken;
  private String userId;
  private Source source;
  private Map<String,Object> meta;

  public CoreAuthentication(String userId, String tenantId, Set<PermissionDTO> permissions, String jwtToken) {
    super(userId, "[Protected]", new ArrayList<GrantedAuthority>());
    this.tenantId = tenantId;
    this.permissions = permissions;
    this.userId = userId;
    this.jwtToken = jwtToken;
  }

  public CoreAuthentication setSource(Source source){
    this.source=source;
    return this;
  }

  public CoreAuthentication setMeta(Map<String,Object> meta) {
    this.meta = meta;
    return this;
  }

  public String getUserId() {
    return userId;
  }
}
