package com.sell.search.core.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.Date;
import java.util.Map;
import java.util.Set;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Created by hemants on 31/12/18. The token class which encapsulate all information related to token
 */
@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CoreAccessToken {

    private String accessToken;
    private long expiresIn;
    private Date expiry;
    private String tokenType = "Bearer";
    private String refreshToken;
    Set<PermissionDTO> permissions;
    private String userId;
    private String username;
    private String tenantId;
    private Source source;
    private Map<String,Object> meta;

    public CoreAccessToken(String accessToken, Date expiry, String refreshToken, Set<PermissionDTO> permissions) {
        this.accessToken = accessToken;
        long now = System.currentTimeMillis();
        this.expiry = expiry;
        expiresIn  = ((expiry.getTime() - now) / 1000);

        this.refreshToken = refreshToken;
        this.permissions = permissions;
    }

    public CoreAccessToken accessToken(String accessToken) {
        this.accessToken = accessToken;
        return this;
    }

    public CoreAccessToken expiresIn(long expiresIn) {
        this.expiresIn = expiresIn;
        return this;
    }

    public CoreAccessToken tokenType(String tokenType) {
        this.tokenType = tokenType;
        return this;
    }

    public CoreAccessToken refreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
        return this;
    }

    public CoreAccessToken permissions(Set<PermissionDTO> permissions) {
        this.permissions = permissions;
        return this;
    }

    public CoreAccessToken expiry(Date expiry) {
        this.expiry = expiry;
        return this;
    }


    public CoreAccessToken userId(String userId) {
        this.userId = userId;
        return this;
    }

    public CoreAccessToken username(String username) {
        this.username = username;
        return this;
    }

    public CoreAccessToken tenantId(String tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public CoreAccessToken setMeta(Map<String,Object> meta) {
        this.meta = meta;
        return this;
    }

    public CoreAccessToken source(Source source) {
        this.source = source;
        return this;
    }
}
