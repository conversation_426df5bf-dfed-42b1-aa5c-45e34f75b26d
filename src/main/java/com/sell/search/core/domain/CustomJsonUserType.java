package com.sell.search.core.domain;


import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.HashMap;
import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SessionImplementor;
import org.hibernate.usertype.UserType;

/**
 * Created by hemants on 31/12/18. Referred : https://thoughts-on-java.org/persist-postgresqls-jsonb-data-type-hibernate/
 */
public class CustomJsonUserType implements UserType {

  @Override
  public int[] sqlTypes() {
    return new int[] {Types.JAVA_OBJECT};
  }

  @Override
  public Class returnedClass() {
    return HashMap.class;
  }

  @Override
  public boolean equals(Object obj1, Object obj2) {
    if (obj1 == null) {
      return obj2 == null;
    }
    return obj1.equals(obj2);
  }

  @Override
  public int hashCode(Object o) {
    return o.hashCode();
  }

  @Override
  public Object nullSafeGet(ResultSet resultSet, String[] strings, SessionImplementor sessionImplementor, Object o) throws SQLException {

    final String cellContent = resultSet.getString(strings[0]);
    if (cellContent == null) {
      return null;
    }
    try {
      final ObjectMapper mapper = new ObjectMapper();
      return mapper.readValue(cellContent.getBytes(StandardCharsets.UTF_8), returnedClass());
    } catch (final Exception ex) {
      throw new RuntimeException("Failed to convert String to Invoice: " + ex.getMessage(), ex);
    }
  }

  @Override
  public void nullSafeSet(PreparedStatement ps, Object value, int idx, SessionImplementor sessionImplementor) throws SQLException {
    if (value == null) {
      ps.setNull(idx, Types.OTHER);
      return;
    }
    try {
      final ObjectMapper mapper = new ObjectMapper();
      final StringWriter w = new StringWriter();
      mapper.writeValue(w, value);
      w.flush();
      ps.setObject(idx, w.toString(), Types.OTHER);
    } catch (final Exception ex) {
      throw new RuntimeException("Failed to convert Invoice to String: " + ex.getMessage(), ex);
    }
  }

  @Override
  public Object deepCopy(Object value) {
    try {
      // use serialization to create a deep copy
      ByteArrayOutputStream bos = new ByteArrayOutputStream();
      ObjectOutputStream oos = new ObjectOutputStream(bos);
      oos.writeObject(value);
      oos.flush();
      oos.close();
      bos.close();

      ByteArrayInputStream bais = new ByteArrayInputStream(bos.toByteArray());
      return new ObjectInputStream(bais).readObject();
    } catch (ClassNotFoundException | IOException ex) {
      throw new HibernateException(ex);
    }
  }

  @Override
  public boolean isMutable() {
    return true;
  }

  @Override
  public Serializable disassemble(Object o) {
    return (Serializable) this.deepCopy(o);
  }

  @Override
  public Object assemble(Serializable serializable, Object o) {
    return this.deepCopy(o);
  }

  @Override
  public Object replace(Object o, Object o1, Object o2) {
    return this.deepCopy(o);
  }
}
