package com.sell.search.core.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sell.search.core.utils.SecurityUtil;
import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.PrePersist;
import javax.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@MappedSuperclass
public abstract class TenantAwareBaseEntity extends BaseEntity {
  @Column(nullable = false)
  private Long tenantId;

  @Transient
  @JsonIgnore
  private boolean explicitTenantId;

  @PrePersist
  private void setExplicitTenant() {
    if (!isExplicitTenantId())
      setTenantId(SecurityUtil.getTenantId());
  }
}
