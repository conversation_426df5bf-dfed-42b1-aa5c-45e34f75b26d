package com.sell.search.core.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * Created by hemants on 11/01/19. This class further explain the error information
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@NoArgsConstructor
public class FieldErrorResource {

  private String field;
  private String message;

  @ToString.Exclude
  @JsonIgnore
  private Object[] args;

  public FieldErrorResource(String field, String message) {
    this.field = field;
    this.message = message;
  }

  public FieldErrorResource(String field, String message, Object... args) {
    this.field = field;
    this.message = message;
    this.args = args;
  }

  public FieldErrorResource field(String field) {
    this.field = field;
    return this;
  }

  public FieldErrorResource message(String message) {
    this.message = message;
    return this;
  }

}