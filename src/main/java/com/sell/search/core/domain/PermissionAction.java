package com.sell.search.core.domain;

import lombok.Getter;

@Getter
public enum PermissionAction { //@formatter:off
    // standard
    CREATE("write", "Create"), READ("read", "Read"), UPDATE("update", "Update"), DELETE("delete", "Delete"),

    // communication
    CALL("call", "Call"), EMAIL("email", "Email"), SMS("sms", "SMS"),

    // productivity
    TASK("task", "Task"), NOTE("note", "Note"), MEETING("meeting", "Meeting"), DOCUMENT("document", "Document"),

    // admin
    READ_ALL("readAll", "View All"), UPDATE_ALL("updateAll", "Modify All"), DELETE_ALL("deleteAll", "Delete All"), //@formatter:on

    // order management
    QUOTATION("quotation", "Quotation"),

    //reshare
    RESHARE("reshare", "Reshare"),

    //reassign
    REASSIGN("reassign", "Reassign");

    private final String action;
    private final String label;

    PermissionAction(String action, String label) {
        this.action = action;
        this.label = label;
    }
}
