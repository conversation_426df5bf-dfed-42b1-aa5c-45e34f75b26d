package com.sell.search.dto;

import com.sell.search.core.constants.InputSize;
import com.sell.search.search.core.dto.SearchRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@Data
public class CreateSearchListRequest {

  @Schema( required = true, example = "My Leads", description = "Unique name for this list")
  @NotBlank
  @Size(max = InputSize.MAX_INPUT_SIZE)
  private String name;

  @Schema( required = false, example = "All leads assigned to me", description = "Description for this list")
  @Size(max = InputSize.MAX_INPUT_SIZE_DESCRIPTION)
  private String description;

  @Schema( required = true, example = "{}", description = "Amura search query")
  @NotNull
  private SearchRequest searchRequest;
}
