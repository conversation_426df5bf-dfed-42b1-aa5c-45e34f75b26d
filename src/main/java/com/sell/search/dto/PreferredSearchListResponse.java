package com.sell.search.dto;

import com.sell.search.core.domain.EntityType;
import com.sell.search.core.dto.BaseResponse;
import com.sell.search.search.core.dto.SearchRequest;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PreferredSearchListResponse extends BaseResponse {

  private static final long serialVersionUID = 1L;

  private Long id;
  private EntityType entityType;

  private String name;
  private String description;
  private SearchRequest searchRequest;
  private String[] preferredFields;

  private boolean systemDefault;
  private int size;
  private String sort;
  private String view;
  private Long pipelineId;
  private String freezedColumn;

  public PreferredSearchListResponse(long id, EntityType entityType, String name, String description, SearchRequest searchRequest,
      String[] preferredFields, int size, String sort, String view, Long pipelineId, String freezedColumn) {
    this.id = id;
    this.entityType = entityType;
    this.name = name;
    this.description = description;
    this.searchRequest = searchRequest;
    this.preferredFields = preferredFields;
    this.size = size;
    this.sort = sort;
    this.view = view;
    this.pipelineId = pipelineId;
    this.freezedColumn = freezedColumn;
  }
}
