package com.sell.search.dto;

import lombok.Getter;

/**
 * Created by shashanks3 on 6/5/19.
 */
@Getter
public enum EsFieldType {
  TEXT("TEXT", "text"),
  KEYWORD("KEYWORD", "keyword"),
  DATE("DATE", "date"),
  LONG("LONG", "long"),
  FLOAT("FLOAT", "float"),
  INTEGER("INTEGER", "integer"),
  BOOLEAN("BOOLEAN", "boolean"),
  NESTED("NESTED", "nested");

  private final String key;
  private final String value;

  EsFieldType(String key, String value) {
    this.key = key;
    this.value = value;
  }


}
