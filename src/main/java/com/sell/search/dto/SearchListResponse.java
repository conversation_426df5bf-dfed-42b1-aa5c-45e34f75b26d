package com.sell.search.dto;

import com.sell.search.core.domain.EntityType;
import com.sell.search.core.dto.BaseResponse;
import com.sell.search.search.core.dto.SearchRequest;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SearchListResponse extends BaseResponse {

  private static final long serialVersionUID = 1L;

  private Long id;
  private EntityType entityType;

  private String name;
  private String description;
  private SearchRequest searchRequest;

  private boolean systemDefault;
  private String sort;
  private int size;
  private String view;
  private String freezedColumn;
  private Long pipelineId;
}
