package com.sell.search.dto;

import java.util.List;
import java.util.Map;
import org.elasticsearch.common.unit.TimeValue;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

/**
 * Created by hemants on 16/03/19.
 */
public class SearchPageImp<T> extends PageImpl {

  private TimeValue timeValue;
  private Map metaData;

  public SearchPageImp(List content, Pageable pageable, long total, TimeValue timeValue) {
    super(content, pageable, total);
    this.timeValue = timeValue;
  }


  public SearchPageImp(List content, Pageable pageable, long total, TimeValue timeValue, Map metaData) {
    super(content, pageable, total);
    this.timeValue = timeValue;
    this.metaData = metaData;
  }

  public SearchPageImp(List<Map<String, Object>> content, Pageable pageable) {
    super(content, pageable, content.size());
  }


  public Map getMetaData() {
    return metaData;
  }

  public void setMetaData(Map metaData) {
    this.metaData = metaData;
  }
}
