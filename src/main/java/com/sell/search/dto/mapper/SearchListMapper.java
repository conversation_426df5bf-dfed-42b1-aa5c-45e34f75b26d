package com.sell.search.dto.mapper;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.search.search.core.dto.SearchRequest;
import com.sell.search.domain.SearchList;
import com.sell.search.dto.CreateSearchListRequest;
import com.sell.search.dto.SearchListResponse;
import com.sell.search.dto.UpdateSearchListRequest;
import java.io.IOException;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * This interface is used for mapping DTO classes to entity classes and vise versa. Mapstruct framework creates corresponding mapper implementation
 * based upon provided mapper interface and its params.
 */
@Mapper(componentModel = "spring", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS, unmappedTargetPolicy = ReportingPolicy.IGNORE,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SearchListMapper {
  SearchListMapper INSTANCE = Mappers.getMapper(SearchListMapper.class);
  Logger log = LoggerFactory.getLogger(SearchListMapper.class);

  /**
   * Map {@link CreateSearchListRequest} DTO to {@link SearchList} entity
   *
   * @param request {@link CreateSearchListRequest} object
   * @return {@link SearchList} entity
   */
  @Mapping(source = "searchRequest", target = "searchRequest", qualifiedByName = "getSearchRequestJson")
  SearchList fromCreateSearchListRequest(CreateSearchListRequest request);

  /**
   * Map {@link SearchList} entity to {@link SearchListResponse} DTO
   *
   * @param searchList {@link SearchList} entity object
   * @return {@link SearchListResponse} object
   */
  @Mapping(source = "searchRequest", target = "searchRequest", qualifiedByName = "parseSearchRequest")
  @Mapping(target = "sort", expression = "java(\"updatedAt,desc\")")
  @Mapping(target = "size", expression = "java(10)")
  SearchListResponse toSearchListResponse(SearchList searchList);

  /**
   * Map source object to target object. All the null parameters are ignored along with explicitly provided {@code id} parameter
   *
   * @param request Source {@link UpdateSearchListRequest} DTO object
   * @param searchList Target {@link SearchList} entity object
   * @return {@link SearchList} entity
   */
  @Mapping(source = "searchRequest", target = "searchRequest", qualifiedByName = "getSearchRequestJson")
  SearchList fromUpdateSearchListRequest(UpdateSearchListRequest request, @MappingTarget SearchList searchList);

  /**
   * Map {@link SearchList} entity to {@link CreateSearchListRequest}
   *
   * @param searchList {@link SearchList} entity object
   * @return {@link CreateSearchListRequest} DTO object
   */
  @Mapping(source = "searchRequest", target = "searchRequest", qualifiedByName = "parseSearchRequest")
  CreateSearchListRequest toCreateSearchListRequest(SearchList searchList);

  /**
   * Map {@link SearchList} entity to {@link UpdateSearchListRequest}
   *
   * @param searchList {@link SearchList} entity object
   * @return {@link UpdateSearchListRequest} DTO object
   */
  @Mapping(source = "searchRequest", target = "searchRequest", qualifiedByName = "parseSearchRequest")
  UpdateSearchListRequest toUpdateSearchListRequest(SearchList searchList);

  @Named("searchRequestJson")
  default String getSearchRequestJson(SearchRequest searchRequest) {
    try {
      ObjectMapper mapper = new ObjectMapper();
      mapper.setSerializationInclusion(Include.NON_NULL);
      return mapper.writeValueAsString(searchRequest);
    } catch (JsonProcessingException e) {
      log.error("searchRequest serialization for SearchList", e);
      return null;
    }
  }

  @Named("parseSearchRequest")
  default SearchRequest parseSearchRequest(String searchRequestJson) {
    try {
      return new ObjectMapper().readValue(searchRequestJson, SearchRequest.class);
    } catch (IOException e) {
      log.error("searchRequest de-serialization for SearchList", e);
      return null;
    }
  }
}
