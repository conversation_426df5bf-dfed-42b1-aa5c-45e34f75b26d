package com.sell.search.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;


@Getter
public class PreferredSearchListRequest {

  private final String[] fields;
  private final int size;
  private final String sort;
  private final String view;
  private final Long pipelineId;
  private final String freezedColumn;

  @JsonCreator
  public PreferredSearchListRequest(@JsonProperty("fields") String[] fields, @JsonProperty("size") int size, @JsonProperty("sort") String sort,
      @JsonProperty("view") String view, @JsonProperty("pipelineId") Long pipelineId, @JsonProperty("freezedColumn") String freezedColumn) {
    this.fields = ObjectUtils.isEmpty(fields) ? new String[]{} : fields;
    this.size = size;
    this.sort = sort;
    this.view = view;
    this.pipelineId = pipelineId;
    this.freezedColumn = freezedColumn;
  }
}