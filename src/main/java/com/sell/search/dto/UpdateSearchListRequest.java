package com.sell.search.dto;

import com.sell.search.core.constants.InputSize;
import com.sell.search.search.core.dto.SearchRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.Size;
import lombok.Data;

@Data
public class UpdateSearchListRequest {

  @Schema( required = false, example = "My Leads", description = "Unique name for this list")
  @Size(max = InputSize.MAX_INPUT_SIZE)
  protected String name;

  @Schema( example = "All leads assigned to me", description = "Description for this list")
  @Size(max = InputSize.MAX_INPUT_SIZE_DESCRIPTION)
  protected String description;

  @Schema( required = false, example = "{}", description = "Amura search query")
  protected SearchRequest searchRequest;
}
