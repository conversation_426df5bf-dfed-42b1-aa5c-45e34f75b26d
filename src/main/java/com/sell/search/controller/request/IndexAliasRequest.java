package com.sell.search.controller.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class IndexAliasRequest {
  private final long tenantId;
  private final String planName;

  @JsonCreator
  public IndexAliasRequest(
      @JsonProperty("tenantId") long tenantId, @JsonProperty("planName") String planName) {
    this.tenantId = tenantId;
    this.planName = planName.toLowerCase();
  }
}
