package com.sell.search.controller;

import com.sell.search.service.Content;
import com.sell.search.service.SearchListService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/search/smart-list")
public class DashboardController {

  @Autowired
  private SearchListService searchListService;


  @GetMapping(value = "/leads/count", produces = MediaType.APPLICATION_JSON_VALUE)
  public List<Content> getCountByEachAccessibleSmartListForLead(@RequestParam(value = "id") List<Long> ids) {
    return searchListService.getEntityCountBySmartListId(ids, "lead");
  }

  @GetMapping(value = "/contacts/count", produces = MediaType.APPLICATION_JSON_VALUE)
  public List<Content> getCountByEachAccessibleSmartListForContact(@RequestParam(value = "id") List<Long> ids) {
    return searchListService.getEntityCountBySmartListId(ids, "contact");
  }

  @GetMapping(value = "/deals/count", produces = MediaType.APPLICATION_JSON_VALUE)
  public List<Content> getCountByEachAccessibleSmartListForDeal(@RequestParam(value = "id") List<Long> ids) {
    return searchListService.getEntityCountBySmartListId(ids, "deal");
  }

  @GetMapping(value = "/companies/count", produces = MediaType.APPLICATION_JSON_VALUE)
  public List<Content> getCountByEachAccessibleSmartListForCompany(@RequestParam(value = "id") List<Long> ids) {
    return searchListService.getEntityCountBySmartListId(ids, "company");
  }

}
