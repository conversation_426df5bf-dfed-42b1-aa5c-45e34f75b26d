package com.sell.search.controller;

import static com.sell.search.core.domain.EntityType.CONTACT;
import static com.sell.search.core.domain.EntityType.LEAD;

import com.sell.search.controller.request.AssociatedEntityRequest;
import com.sell.search.controller.response.EntitySummary;
import com.sell.search.controller.response.WhatsAppEntitySearchResponse;
import com.sell.search.core.annotation.ApiPageable;
import com.sell.search.dto.SearchPageImp;
import com.sell.search.entity.core.dto.SearchResponseWithMetaData;
import com.sell.search.exception.SearchErrorCodes;
import com.sell.search.exception.SearchException;
import com.sell.search.search.core.dto.SearchRequest;
import com.sell.search.service.SearchService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/search")
@RequiredArgsConstructor
public class SearchController {

  private final SearchService searchService;
  @Operation(description = "Get Search result based on user search request")
  @PostMapping(value = "/{entity}", produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiPageable
  public ResponseEntity getSearchResults(Pageable pageable,
      @PathVariable("entity") String entity,
      @Valid @RequestBody SearchRequest searchRequest,
      @RequestParam(value = "view",required = false) String view,
      @RequestParam(value = "pipelineId",required = false) Long pipelineId
      ) {
    if("kanban".equalsIgnoreCase(view)){
      return ResponseEntity.ok(searchService.getSearchResultsForKanbanView(entity, pageable, searchRequest,true,pipelineId));

    }
    return ResponseEntity.ok(searchService.convertToResponse(pageable,
        searchService.getSearchResultsWithMetaInfo(entity, pageable, searchRequest, true
        )));
  }

  @GetMapping(value = "/meeting-invitee/lookup", produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiPageable
  @Hidden
  public ResponseEntity<List<EntitySummary>> lookupAttendee(@RequestParam("q") String fieldAndValue) {
    return ResponseEntity.ok(searchService.getInviteeLookupFor(fieldAndValue));
  }

  @GetMapping(value = "/email-recipient/lookup", produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiPageable
  @Hidden
  public ResponseEntity<List<EntitySummary>> lookupForEmailRecipient(@RequestParam(value = "q", defaultValue = "") String fieldAndValue) {
    return ResponseEntity.ok(searchService.getEmailRecipient(fieldAndValue));
  }

  @GetMapping(value = "/contacts/email", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(description = "Get contact by email ids", hidden = true)
  @ApiPageable()
  public ResponseEntity<List<EntitySummary>> contactByEmail(@RequestParam(value = "email") List<String> emailList) {
    return ResponseEntity.ok(searchService.getEntitiesByEmail(CONTACT.getPermissionName(), emailList));
  }

  @GetMapping(value = "/leads/email", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(description =  "Get lead by email ids", hidden = true)
  @ApiPageable()
  public ResponseEntity<List<EntitySummary>> leadByEmail(@RequestParam(value = "email") List<String> emailList) {
    return ResponseEntity.ok(searchService.getEntitiesByEmail(LEAD.getPermissionName(), emailList));
  }
  
  @GetMapping(value = "/{entity}/phoneNumber", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(description =  "Get entity by phone Number", hidden = true)
  @ApiPageable()
  public ResponseEntity<List<EntitySummary>> entitiesByPhoneNumber(@PathVariable("entity") String entity,
      @RequestParam(value = "phoneNumbers") List<String> phoneNumberList) {
    return ResponseEntity.ok(searchService.getEntitiesByPhoneNumber(entity, phoneNumberList));
  }

  /**
   * Return lookup search result
   *
   * @see org.springframework.data.domain.PageRequest
   */
  @Operation(description = "Get Search result based on user search request")
  @GetMapping(value = "/{entity}/lookup", produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiPageable
  public SearchPageImp<Map<String, Object>> lookup(Pageable pageable,
      @PathVariable("entity") String entity,
      @RequestParam("q") String fieldAndValue,
      @RequestParam(value = "converted",required = false) Boolean isConverted,
      @RequestParam(value = "view",required = false) Optional<String> view) {
    Pageable newPageable = getDefaultPageRequest(fieldAndValue);
    return searchService.convertToLookupResponse(newPageable,
        searchService.lookup(entity, fieldAndValue, newPageable, isConverted, view), entity, view);
  }

  @Operation(description = "Get Contacts based on associated company and search request")
  @GetMapping(value = "/contacts/lookup", produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiPageable
  public SearchPageImp<Map<String, Object>> lookupContacts(
      @RequestParam(value = "companyId", required = false) Long companyId,
      @RequestParam(value = "name", required = false) Optional<String> name,
      @RequestParam(value = "view", required = false) Optional<String> view) {
    Pageable newPageable = getDefaultPageRequest(name);
    return searchService.lookupContacts(companyId, name, newPageable, view);
  }

  @Operation(description = "Get Companies based on associated contact and search request")
  @GetMapping(value = "/companies-lookup", produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiPageable
  public SearchPageImp<Map<String, Object>> lookupCompanies(
      @RequestParam(value = "name", required = false) Optional<String> name,
      @RequestParam(value = "contactId", required = false) List<Long> contacts) {
    Pageable newPageable = getDefaultPageRequest(name);
    return searchService.getCompanyLookupForAssociatedContacts(name, contacts, newPageable);
  }

  @Operation(description = "Get Search result based on entity name and return selected fields")
  @GetMapping(value = "/{entity}/lookup-with-fields", produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiPageable
  public SearchPageImp<Map<String, Object>> lookupWithFields(Pageable pageable,
      @PathVariable("entity") String entity,
      @RequestParam("q") String fieldAndValue,
      @RequestParam(value = "field", required = false, defaultValue = "") List<String> fields) {
    Pageable newPageable = getDefaultPageRequest(fieldAndValue);
    return searchService.convertToLookupWithFieldsResponse(newPageable,
        searchService.lookupWithFields(entity, fieldAndValue, newPageable, fields), entity, fields);
  }

  @Operation(description = "Get Search result on entity data")
  @GetMapping(value = "/{entity}", produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiPageable
  public ResponseEntity<?> getListSearch(
      Pageable pageable, @PathVariable("entity") String entity, @RequestParam(name = "text") String text,
      @RequestParam(value = "view", required = false) String view) {
    if ("integration".equalsIgnoreCase(view)) {
      SearchResponseWithMetaData results = searchService.getSearchResultsWithMetaInfo(entity, pageable);
      return ResponseEntity.ok(searchService.toIntegrationResponse(entity, results));
    }
    return ResponseEntity.ok(
        searchService.convertToResponse(pageable, searchService.getFreeTextSearch(entity, pageable, text),entity ));
  }

  @Operation(description = "Get Entities based on associated relatedEntities and search request")
  @PostMapping(value = "/{entity}/associated-with-entity", produces = MediaType.APPLICATION_JSON_VALUE)
  public SearchPageImp<Map<String, Object>> searchWithAssociatedEntities(
      @PathVariable("entity") String entity,
      @RequestParam("q") String fieldAndValue,
      @RequestParam(value = "view", required = false) Optional<String> view,
      @RequestBody AssociatedEntityRequest associatedEntityRequest) {
    Pageable pageable = getDefaultPageRequest(fieldAndValue, 10);
    return searchService.searchWithAssociatedEntity(entity, fieldAndValue, view, associatedEntityRequest, pageable);
  }

  @Operation(description = "Get Search result for whatsapp entities based on user search request")
  @PostMapping(value = "/whatsapp-entity", produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<WhatsAppEntitySearchResponse> searchWhatsAppEntity(
      @RequestBody com.sell.search.global.search.api.request.SearchRequest whatsAppEntitySearchRequest) {
    return ResponseEntity.ok(searchService.searchWhatsAppEntity(whatsAppEntitySearchRequest));
  }


  private Pageable getDefaultPageRequest(String fieldAndValue) {
    Sort sortOnUpdatedAtDesc = new Sort(Direction.DESC, "updatedAt");
    if (isValidLookupValue(fieldAndValue)) {
      return new PageRequest(0, 25, sortOnUpdatedAtDesc);
    }
    return new PageRequest(0, 10, sortOnUpdatedAtDesc);
  }

  private Pageable getDefaultPageRequest(String fieldAndValue, Integer size) {
    Sort sortOnUpdatedAtDesc = new Sort(Direction.DESC, "updatedAt");
    isValidLookupValue(fieldAndValue);
    return new PageRequest(0, size, sortOnUpdatedAtDesc);
  }

  private Pageable getDefaultPageRequest(Optional<String> value) {
    Sort sortOnUpdatedAtDesc = new Sort(Direction.DESC, "updatedAt");
    if (value.isPresent()) {
      return new PageRequest(0, 25, sortOnUpdatedAtDesc);
    }
    return new PageRequest(0, 10, sortOnUpdatedAtDesc);
  }

  private boolean isValidLookupValue(String fieldAndValue) {
    if (!fieldAndValue.contains(":")) {
      throw new SearchException(SearchErrorCodes.INVALID_SEARCH_REQUEST);
    }
    String[] splitArray = fieldAndValue.split(":");
    return splitArray.length == 2 && !"".equalsIgnoreCase(splitArray[1]);
  }

}