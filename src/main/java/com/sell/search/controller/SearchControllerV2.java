package com.sell.search.controller;

import com.sell.search.core.annotation.ApiPageable;
import com.sell.search.dto.SearchPageImp;
import com.sell.search.search.core.dto.SearchRequest;
import com.sell.search.service.SearchService;
import io.swagger.v3.oas.annotations.Operation;
import java.util.Map;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v2/search")
public class SearchControllerV2 {

  private final SearchService searchService;

  @Autowired
  public SearchControllerV2(SearchService searchService) {
    this.searchService = searchService;
  }

  @Operation(description = "Get Search result on entity data")
  @GetMapping(value = "/{entity}", produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiPageable
  public ResponseEntity<?> getListSearch(
      Pageable pageable, @PathVariable("entity") String entity, @RequestParam(name = "text") String text,
      @RequestParam(value = "view", required = false) String view) {
    if ("integration".equalsIgnoreCase(view)) {
      return ResponseEntity.ok(searchService.getSearchResultsWithMetaInfoV2(entity, pageable));
    }
    return ResponseEntity.ok().build();
  }

  @Operation(description = "Get Search result based on search request without share rule evaluation", hidden = true)
  @PostMapping(value = "/{entity}", produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiPageable
  public SearchPageImp<Map<String, Object>> getSearchResultsWithoutShareRuleEvaluation(
      Pageable pageable,
      @PathVariable("entity") String entity,
      @Valid @RequestBody SearchRequest searchRequest) {
    return searchService.convertToResponse(pageable,
        searchService.getSearchResultsWithMetaInfo(entity, pageable, searchRequest, false));
  }
}