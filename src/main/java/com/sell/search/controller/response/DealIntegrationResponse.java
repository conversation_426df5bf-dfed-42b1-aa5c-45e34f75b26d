package com.sell.search.controller.response;

import static java.util.Objects.isNull;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class DealIntegrationResponse extends IntegrationResponse {

  private static final String[] FIELDS = new String[]{
      "id", "name", "estimatedValue", "product", "estimatedClosureOn", "actualClosureDate", "actualValue", "company",
      "ownedBy","associatedContacts","pipeline","pipelineStage","forecastingType","createdBy","createdAt","updatedBy","updatedAt","campaign","source","customFieldValues"};

  private final Long id;
  private final String name;
  private final Double estimatedValue;
  private final Double actualValue;
  private final String product;
  private final String estimatedClosureDate;
  private final String actualClosureDate;
  private final String company;
  private final String ownedBy;
  private final List<IdName> associatedContacts;
  private final String pipeline;
  private final String pipelineStage;
  private final String forecastingType;
  private final String createdBy;
  private final String createdAt;
  private final String updatedBy;
  private final String updatedAt;
  private final String campaign;
  private final String source;
  private Map<String,Object> customFieldValues;

  public static DealIntegrationResponse from(Map<String, Object> dealProperties) {
    Double estimatedValue = getMoneyValue(dealProperties, "estimatedValue");
    Double actualValue = getMoneyValue(dealProperties, "actualValue");

    return new DealIntegrationResponse(
        getLong(dealProperties.get("id")),
        getString(dealProperties.get("name")),
        estimatedValue,
        actualValue,
        getNameFromIdName(dealProperties, "product"),
        getDateText(dealProperties.get("estimatedClosureOn")),
        getDateText(dealProperties.get("actualClosureDate")),
        getNameFromIdName(dealProperties, "company"),
        getNameFromIdName(dealProperties, "ownedBy"),
        getAssociatedContacts(dealProperties),
        getNameFromIdName(dealProperties,"pipeline"),
        getNameFromIdName(dealProperties,"pipelineStage"),
        getString(dealProperties.getOrDefault("forecastingType",null)),
        getNameFromIdName(dealProperties,"createdBy"),
        getDateText(dealProperties.get("createdAt")),
        getNameFromIdName(dealProperties,"updatedBy"),
        getDateText(dealProperties.get("updatedAt")),
        getNameFromIdName(dealProperties,"campaign"),
        getNameFromIdName(dealProperties,"source"),
        getCustomFieldMap(dealProperties)
        );
  }

  private static Map<String, Object> getCustomFieldMap(Map<String, Object> dealProperties) {
    if(dealProperties.containsKey("customFieldValues")){
      return (Map<String, Object>) dealProperties.get("customFieldValues");
    }
    return null;
  }

  private static List<IdName> getAssociatedContacts(Map<String, Object> dealProperties) {
    if(dealProperties.containsKey("associatedContacts")){
      return (List<IdName>) dealProperties.get("associatedContacts");
    }
    return null;
  }

  private static String getNameFromIdName(Map<String, Object> properties, String fieldName) {
    Map<String, String> idName = (Map<String, String>) properties.get(fieldName);
    if (isNull(idName)) {
      return null;
    }
    return idName.get("name");
  }

  private static Double getMoneyValue(Map<String, Object> properties, String fieldName) {
    Map<String, Object> estimatedValue = (Map<String, Object>) properties.get(fieldName);
    if (isNull(estimatedValue)) {
      return null;
    }
    return (Double) estimatedValue.get("value");
  }

  private static String getDateText(Object object) {
    if (isNull(object)) {
      return null;
    }
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    return LocalDate.parse(object.toString(), formatter).toString();
  }

  private static String getString(Object object) {
    return isNull(object) ? null : object.toString();
  }

  private static Long getLong(Object object) {
    return isNull(object) ? null : Long.parseLong(object.toString());
  }

  public static String[] getSearchFields() {
    return FIELDS;
  }
}
