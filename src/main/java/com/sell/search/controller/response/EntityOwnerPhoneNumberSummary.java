package com.sell.search.controller.response;

import com.sell.search.entity.core.model.PhoneNumber;
import lombok.Getter;

import java.util.List;

@Getter
public class EntityOwnerPhoneNumberSummary {
    private final List<EntityAndOwnerDetails> leads;
    private final List<EntityAndOwnerDetails> contacts;

    public EntityOwnerPhoneNumberSummary(List<EntityAndOwnerDetails> leads, List<EntityAndOwnerDetails> contacts) {
        this.leads = leads;
        this.contacts = contacts;
    }

    @Getter
    public static class EntityAndOwnerDetails{
        private final long id;
        private final String name;
        private final PhoneNumber[] ownerPhoneNumbers;


        public EntityAndOwnerDetails(long id, String name, PhoneNumber[] ownerPhoneNumbers) {
            this.id = id;
            this.name = name;
            this.ownerPhoneNumbers = ownerPhoneNumbers;
        }
    }
}
