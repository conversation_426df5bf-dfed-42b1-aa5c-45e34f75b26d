package com.sell.search.controller.response;

import static java.lang.String.format;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import com.sell.search.exception.SearchErrorCodes;
import com.sell.search.exception.SearchException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class IntegrationResponse {

  public static Map from(Map<String, Object> properties, Map<String, Object> metaData, String entity, List<Field> fields) {
    Map<String,Object> result = new HashMap<>();
    if("lead".equalsIgnoreCase(entity)){
      String textFormattedEmails = getTextFormattedEmails(properties);
      String textFormattedPhoneNumbers = getTextFormattedPhones(properties,"phoneNumbers");
      String textFormattedCompanyPhones = getTextFormattedPhones(properties,"companyPhones");
      String products = getTextFormattedProducts(properties);
      result.put("product",products);
      result.put("email",textFormattedEmails);
      result.put("phoneNumbers",textFormattedPhoneNumbers);
      result.put("companyPhones",textFormattedCompanyPhones);
      fields
          .stream()
          .filter(field -> field.getStandard()
              && (!FieldType.PICK_LIST.equals(field.getType())
              && !FieldType.MULTI_PICKLIST.equals(field.getType())
              && !FieldType.LOOK_UP.equals(field.getType())
              && !FieldType.EMAIL.equals(field.getType())
              && !FieldType.PHONE.equals(field.getType())
              && !FieldType.PIPELINE.equals(field.getType())
              )
            )
          .forEach(field -> {
            result.put(field.getName(), properties.getOrDefault(field.getName(), null));
          });

      fields
          .stream()
          .filter(field -> field.getStandard() && !"products".equalsIgnoreCase(field.getName())
                  && (FieldType.PICK_LIST.equals(field.getType())||FieldType.LOOK_UP.equals(field.getType())||FieldType.PIPELINE.equals(field.getType()))
          )
          .forEach(field -> {
            result.put(field.getName(), getPicklistName(properties, metaData, field.getName()));
          });

      fields
          .stream()
          .filter(field -> !field.getStandard())
          .forEach(field -> {
            if (FieldType.PICK_LIST.equals(field.getType())) {
              result.put(field.getName(),
                  getPicklistName((Map<String, Object>) properties.getOrDefault("customFieldValues", new HashMap<>()), metaData, field.getName()));
            }
            if (FieldType.MULTI_PICKLIST.equals(field.getType())) {
              result.put(field.getName(),
                  getMultiPicklistName((Map<String, Object>) properties.getOrDefault("customFieldValues", new HashMap<>()), metaData,
                      field.getName()));
            }
            if (!FieldType.MULTI_PICKLIST.equals(field.getType()) && !FieldType.PICK_LIST.equals(field.getType())) {
              result.put(field.getName(),
                  ((Map<String, Object>) properties.getOrDefault("customFieldValues", new HashMap<>())).getOrDefault(field.getName(), null));
            }
          });
      return result;
    }

    if("contact".equalsIgnoreCase(entity)){
      String textFormattedEmails = getTextFormattedEmails(properties);
      String textFormattedPhoneNumbers = getTextFormattedPhones(properties, "phoneNumbers");
      result.put("email", textFormattedEmails);
      result.put("phoneNumbers", textFormattedPhoneNumbers);
      result.put("id", properties.getOrDefault("id", null));
      fields
          .stream()
          .filter(field -> field.getStandard()
                  && (!FieldType.PICK_LIST.equals(field.getType())
                  && !FieldType.MULTI_PICKLIST.equals(field.getType())
                  && !FieldType.LOOK_UP.equals(field.getType())
                  && !FieldType.EMAIL.equals(field.getType())
                  && !FieldType.PHONE.equals(field.getType())
              )
          )
          .forEach(field -> {
            result.put(field.getName(),properties.getOrDefault(field.getName(),null));
          });

      fields
          .stream()
          .filter(field -> field.getStandard()
              && (FieldType.PICK_LIST.equals(field.getType())||FieldType.LOOK_UP.equals(field.getType()))
          )
          .forEach(field -> {
            result.put(field.getName(),getPicklistName(properties,metaData,field.getName()));
          });

      fields
          .stream()
          .filter(field -> !field.getStandard())
          .forEach(field -> {
            if(FieldType.PICK_LIST.equals(field.getType())){
              result.put(field.getName(),getPicklistName((Map<String, Object>) properties.getOrDefault("customFieldValues",new HashMap<>()),metaData,field.getName()));
            }
            if(FieldType.MULTI_PICKLIST.equals(field.getType())){
              result.put(field.getName(),getMultiPicklistName((Map<String, Object>) properties.getOrDefault("customFieldValues",new HashMap<>()),metaData,field.getName()));
            }
            if(!FieldType.MULTI_PICKLIST.equals(field.getType()) && !FieldType.PICK_LIST.equals(field.getType())){
              result.put(field.getName(),((Map<String, Object>) properties.getOrDefault("customFieldValues",new HashMap<>())).getOrDefault(field.getName(),null));
            }
          });
      return result;

    }


    return result;
  }

  static String getPicklistName(Map<String, Object> leadProperties, Map<String, Object> metaData, String fieldName) {
    Object salutationValue = leadProperties.get(fieldName);
    if (isNull(salutationValue)) {
      return null;
    }
    Map<String, Map<String, String>> idNameStore = (Map<String, Map<String, String>>) metaData.get("idNameStore");
    return idNameStore.getOrDefault(fieldName, new HashMap<>()).getOrDefault(salutationValue.toString(), null);
  }

  static String getMultiPicklistName(Map<String, Object> leadProperties, Map<String, Object> metaData, String fieldName) {
    List<Integer> multiPicklistValues = (List<Integer>) leadProperties.get(fieldName);
    if (isNull(multiPicklistValues)) {
      return null;
    }
    Map<String, Map<String, String>> idNameStore = (Map<String, Map<String, String>>) metaData.get("idNameStore");

    return multiPicklistValues
        .stream()
        .map(multiPicklist -> idNameStore.getOrDefault(fieldName, new HashMap<>()).getOrDefault(multiPicklist.toString(), null))
        .collect(Collectors.joining(","));
  }

  public static Map toDeal(Map<String, Object> properties, Map<String, Object> metaData, String entity,
      List<com.sell.search.field.domain.Field> fields) {

    Map<String, Object> result = new HashMap<>();
    List<Map<String, Object>> products = getObjectOfProducts(properties);
    result.put("products", products);
    String associatedContacts = getAssociatedContacts(properties);
    result.put("associatedContacts", associatedContacts);
    String actualValue = getMoneyValue(properties, "actualValue");
    result.put("actualValue", actualValue);
    String estimatedValue = getMoneyValue(properties, "estimatedValue");
    result.put("estimatedValue", estimatedValue);

    fields
        .stream()
        .filter(field -> field.isStandard()
                && (!com.sell.search.model.FieldType.PICK_LIST.toString().equals(field.getFieldType().toString())
                && !com.sell.search.model.FieldType.MULTI_PICKLIST.toString().equals(field.getFieldType().toString())
                && !com.sell.search.model.FieldType.LOOK_UP.toString().equals(field.getFieldType().toString())
                && !com.sell.search.model.FieldType.PIPELINE.toString().equals(field.getFieldType().toString())
                && !com.sell.search.model.FieldType.MONEY.toString().equals(field.getFieldType().toString())
            )
        )
        .forEach(field -> {
          result.put(field.getName(), properties.getOrDefault(field.getName(), null));
        });

    fields
        .stream()
        .filter(
            field -> field.isStandard() && !"products".equalsIgnoreCase(field.getName()) && !"associatedContacts".equalsIgnoreCase(field.getName())
                && !com.sell.search.model.FieldType.MONEY.toString().equals(field.getFieldType().toString())
                && (com.sell.search.model.FieldType.PICK_LIST.toString().equals(field.getFieldType().toString())
                || com.sell.search.model.FieldType.LOOK_UP.toString().equals(field.getFieldType().toString())
                || com.sell.search.model.FieldType.PIPELINE.toString().equals(field.getFieldType().toString())
                || com.sell.search.model.FieldType.PIPELINE_STAGE.toString().equals(field.getFieldType().toString()))
        )
        .forEach(field -> {
          result.put(field.getName(), getNameFromIdName(properties, field.getName()));
        });

    fields
        .stream()
        .filter(field -> !field.isStandard())
        .forEach(field -> {
          if (com.sell.search.model.FieldType.PICK_LIST.toString().equals(field.getFieldType().toString())) {
            result.put(field.getName(),
                getNameFromIdName((Map<String, Object>) properties.getOrDefault("customFieldValues", new HashMap<>()), field.getName()));
          }
          if (!com.sell.search.model.FieldType.MULTI_PICKLIST.toString().equals(field.getFieldType().toString())
              && !com.sell.search.model.FieldType.PICK_LIST.toString()
              .equals(field.getFieldType().toString())) {
            result.put(field.getName(),
                ((Map<String, Object>) properties.getOrDefault("customFieldValues", new HashMap<>())).getOrDefault(field.getName(), null));
          }
        });
    return result;
  }

  @Getter
  class MultiPicklist{
    Long id;
    @JsonCreator
    public MultiPicklist(@JsonProperty("id") long id) {
      this.id = id;
    }
  }
  public static String getSalutationName(Map<String, Object> leadProperties, Map<String, Object> metaData) {
    Object salutationValue = leadProperties.get("salutation");
    if (isNull(salutationValue)) {
      return null;
    }
    Map<String, Map<String, String>> idNameStore = (Map<String, Map<String, String>>) metaData.get("idNameStore");
    return idNameStore.get("salutation").get(salutationValue.toString());
  }

  static String getTextFormattedPhones(Map<String, Object> leadProperties,String fieldName) {
    List<Map<String, String>> phones = (List<Map<String, String>>) leadProperties.get(fieldName);
    if (isNull(phones)) {
      return null;
    }
    return phones.stream()
        .filter(emailProperties -> nonNull(emailProperties.get("dialCode")) && nonNull(emailProperties.get("value")))
        .map(phone -> format("%s %s", phone.get("dialCode"), phone.get("value")))
        .collect(Collectors.joining(", "));
  }

  public static String getTextFormattedEmails(Map<String, Object> leadProperties) {
    List<Map<String, String>> emails = (List<Map<String, String>>) leadProperties.get("emails");
    if (isNull(emails)) {
      return null;
    }
    return emails.stream()
        .filter(emailProperties -> nonNull(emailProperties.get("value")))
        .map(emailProperties -> emailProperties.get("value"))
        .collect(Collectors.joining(", "));
  }

  public static String getTextFormattedProducts(Map<String, Object> leadProperties) {
    List<Map<String, String>> products = (List<Map<String, String>>) leadProperties.get("products");
    if (isNull(products)) {
      return null;
    }
    return products.stream()
        .filter(productProperties -> nonNull(productProperties.get("name")))
        .map(productProperties -> productProperties.get("name"))
        .collect(Collectors.joining(", "));
  }

  public static List<Map<String, Object>> getObjectOfProducts(Map<String, Object> leadProperties) {
    List<Map<String, Object>> products = (List<Map<String, Object>>) leadProperties.get("products");
    if (isNull(products)) {
      return null;
    }
    return products;
  }


  private static String getAssociatedContacts(Map<String, Object> dealProperties) {
    List<Map<String, String>> associatedContacts = (List<Map<String, String>>) dealProperties.get("associatedContacts");
    if (isNull(associatedContacts)) {
      return null;
    }
    return associatedContacts.stream()
        .filter(contacts -> nonNull(contacts.get("name")))
        .map(contacts -> contacts.get("name"))
        .collect(Collectors.joining(", "));
  }

  private static String getNameFromIdName(Map<String, Object> properties, String fieldName) {
    Map<String, String> idName = (Map<String, String>) properties.get(fieldName);
    if (isNull(idName)) {
      return null;
    }
    return idName.get("name");
  }

  private static String getMoneyValue(Map<String, Object> properties, String moneyField) {
    Map<String, Object> estimatedValue = (Map<String, Object>) properties.get(moneyField);
    if (isNull(estimatedValue)) {
      return null;
    }
    return estimatedValue.get("value").toString();
  }

  private static String getDateText(Object object) {
    if (isNull(object)) {
      return null;
    }
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    return LocalDate.parse(object.toString(), formatter).toString();
  }


  public static IntegrationResponse from(Map<String, Object> properties, Map<String, Object> metaData, String entity) {
    IntegrationResponse response =
        "lead".equalsIgnoreCase(entity) ? LeadIntegrationResponse.from(properties, metaData)
            : "contact".equalsIgnoreCase(entity) ? ContactIntegrationResponse.from(properties, metaData)
                : "deal".equalsIgnoreCase(entity) ? DealIntegrationResponse.from(properties) : null;
    if (Objects.isNull(response)) {
      throw new SearchException(SearchErrorCodes.INVALID_SEARCH_REQUEST);
    }
    return response;
  }

  public static String[] getFieldsFor(String entity) {
    String[] fields =
        "lead".equalsIgnoreCase(entity) ? LeadIntegrationResponse.getSearchFields()
            : "contact".equalsIgnoreCase(entity) ? ContactIntegrationResponse.getSearchFields()
                : "deal".equalsIgnoreCase(entity) ? DealIntegrationResponse.getSearchFields() : null;
    if (Objects.isNull(fields)) {
      throw new SearchException(SearchErrorCodes.INVALID_SEARCH_REQUEST);
    }
    return fields;
  }
}
