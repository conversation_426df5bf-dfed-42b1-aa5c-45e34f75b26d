package com.sell.search.controller.response;

import static java.lang.String.format;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class LeadIntegrationResponse extends IntegrationResponse {

  private static final String[] FIELDS = new String[]{
      "id", "firstName", "lastName", "salutation", "emails", "phoneNumbers", "address", "city", "state", "zipcode", "country",
      "products", "requirementCurrency", "requirementBudget", "companyName", "department", "designation", "requirementName"};

  private final Long id;
  private final String firstName;
  private final String lastName;
  private final String salutation;
  private final String email;
  private final String phoneNumbers;
  private final String address;
  private final String city;
  private final String state;
  private final String zipcode;
  private final String country;
  private final String product;
  private final String currency;
  private final Double budget;
  private final String companyName;
  private final String department;
  private final String designation;
  private final String requirementName;

  public static LeadIntegrationResponse from(Map<String, Object> leadProperties, Map<String, Object> metaData) {
    String salutationName = getSalutationName(leadProperties, metaData);
    String textFormattedEmails = getTextFormattedEmails(leadProperties);
    String textFormattedPhoneNumbers = getTextFormattedPhones(leadProperties);
    String products = getTextFormattedProducts(leadProperties);

    return new LeadIntegrationResponse(
        getLong(leadProperties.get("id")),
        getString(leadProperties.get("firstName")),
        getString(leadProperties.get("lastName")),
        salutationName,
        textFormattedEmails,
        textFormattedPhoneNumbers,
        getString(leadProperties.get("address")),
        getString(leadProperties.get("city")),
        getString(leadProperties.get("state")),
        getString(leadProperties.get("zipcode")),
        getString(leadProperties.get("country")),
        products,
        getString(leadProperties.get("requirementCurrency")),
        getDouble(leadProperties.get("requirementBudget")),
        getString(leadProperties.get("companyName")),
        getString(leadProperties.get("department")),
        getString(leadProperties.get("designation")),
        getString(leadProperties.get("requirementName")));
  }

  private static String getString(Object object) {
    return isNull(object) ? null : object.toString();
  }

  private static Long getLong(Object object) {
    return isNull(object) ? null : Long.parseLong(object.toString());
  }

  private static Double getDouble(Object object) {
    return isNull(object) ? null : Double.parseDouble(object.toString());
  }

  public static String getSalutationName(Map<String, Object> leadProperties, Map<String, Object> metaData) {
    Object salutationValue = leadProperties.get("salutation");
    if (isNull(salutationValue)) {
      return null;
    }
    Map<String, Map<String, String>> idNameStore = (Map<String, Map<String, String>>) metaData.get("idNameStore");
    return idNameStore.get("salutation").get(salutationValue.toString());
  }

  private static String getTextFormattedPhones(Map<String, Object> leadProperties) {
    List<Map<String, String>> phones = (List<Map<String, String>>) leadProperties.get("phoneNumbers");
    if (isNull(phones)) {
      return null;
    }
    return phones.stream()
        .filter(emailProperties -> nonNull(emailProperties.get("dialCode")) && nonNull(emailProperties.get("value")))
        .map(phone -> format("%s %s", phone.get("dialCode"), phone.get("value")))
        .collect(Collectors.joining(", "));
  }

  public static String getTextFormattedEmails(Map<String, Object> leadProperties) {
    List<Map<String, String>> emails = (List<Map<String, String>>) leadProperties.get("emails");
    if (isNull(emails)) {
      return null;
    }
    return emails.stream()
        .filter(emailProperties -> nonNull(emailProperties.get("value")))
        .map(emailProperties -> emailProperties.get("value"))
        .collect(Collectors.joining(", "));
  }

  public static String getTextFormattedProducts(Map<String, Object> leadProperties) {
    List<Map<String, String>> products = (List<Map<String, String>>) leadProperties.get("products");
    if (isNull(products)) {
      return null;
    }
    return products.stream()
        .filter(productProperties -> nonNull(productProperties.get("name")))
        .map(productProperties -> productProperties.get("name"))
        .collect(Collectors.joining(", "));
  }

  public static String[] getSearchFields() {
    return FIELDS;
  }
}
