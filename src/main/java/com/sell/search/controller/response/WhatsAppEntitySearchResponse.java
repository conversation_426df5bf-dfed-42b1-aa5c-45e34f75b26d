package com.sell.search.controller.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sell.search.global.search.EntityType;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

public class WhatsAppEntitySearchResponse extends PageImpl {
  long totalElements;

  public WhatsAppEntitySearchResponse(List content, Pageable pageable, long total) {
    super(content, pageable, total);
    this.totalElements = total;
  }

  @Override
  public long getTotalElements() {
    return this.totalElements;
  }

  @Getter
  public static class Content extends PageImpl {

    private String entityType;

    public Content(String entityType, List<Map<String, Object>> content, Pageable pageable, long total) {
      super(content, pageable, total);
      this.entityType = entityType;
    }

    @JsonProperty("values")
    public List getContent() {
      return super.getContent();
    }
  }
}
