package com.sell.search.controller.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

@Getter
@Setter
public class KanbanSearchResponse extends PageImpl {
  long totalElements;
  Map<String, Object>  metadata;
  public KanbanSearchResponse(List content, Pageable pageable, long total) {
    super(content, pageable, total);
    this.totalElements = total;
  }

  @Override
  public long getTotalElements() {
    return this.totalElements;
  }

  @Getter
  public static class KanbanContent extends PageImpl{
    private long pipelineStage;
    private String name;

    public KanbanContent(long pipelineStage, List<Map<String, Object>> content, Pageable pageable, long total) {
      super(content, pageable, total);
      this.pipelineStage = pipelineStage;
    }

    @JsonProperty("values")
    public List getContent(){
      return super.getContent();
    }
  }
}
