package com.sell.search.controller.response;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@AllArgsConstructor
public class EntitySummary {

  private final Long id;
  private final String name;
  private final String entity;
  private final Long ownerId;
  private final List<Email> emails;
  private final List<PhoneNumber> phoneNumbers;

  public EntitySummary(Long id, String name, String entity,
      List<Email> emails, List<PhoneNumber> phoneNumbers) {
    this.id = id;
    this.name = name;
    this.entity = entity;
    this.emails = emails;
    this.phoneNumbers = phoneNumbers;
    this.ownerId = null;
  }

  public EntitySummary(Long id, String name, String entity,
      List<Email> emails, List<PhoneNumber> phoneNumbers, Long ownerId) {
    this.id = id;
    this.name = name;
    this.entity = entity;
    this.emails = emails;
    this.phoneNumbers = phoneNumbers;
    this.ownerId = ownerId;
  }


  @Getter
  @Setter
  @NoArgsConstructor
  public static class Email {

    private boolean primary;
    private String value;

    public Email(String value, boolean primary) {
      this.value = value;
      this.primary = primary;
    }
  }

  @Getter
  @Setter
  @NoArgsConstructor
  public static class PhoneNumber {

    private boolean primary;
    private String value;

    public PhoneNumber(String value, boolean primary) {
      this.value = value;
      this.primary = primary;
    }
  }
}
