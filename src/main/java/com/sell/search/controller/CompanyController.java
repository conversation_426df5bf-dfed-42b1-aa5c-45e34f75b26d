package com.sell.search.controller;

import com.sell.search.core.annotation.ApiPageable;
import com.sell.search.dto.SearchPageImp;
import com.sell.search.service.SearchService;
import io.swagger.v3.oas.annotations.Operation;
import java.util.Map;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/search/companies")
public class CompanyController {

  private final SearchService searchService;

  public CompanyController(SearchService searchService) {
    this.searchService = searchService;
  }

  @Operation(description = "Get Search result based on company name")
  @GetMapping(value = "/lookup", produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiPageable
  public SearchPageImp<Map<String, Object>> lookupCompanies(@RequestParam(value = "q", required = false) String name) {
    Pageable newPageable = getDefaultPageRequest();
    return searchService.getCompanyLookupFor(name, newPageable);
  }

  private Pageable getDefaultPageRequest() {
    Sort sortOnUpdatedAtDesc = new Sort(Direction.DESC, "updatedAt");
    return new PageRequest(0, 25, sortOnUpdatedAtDesc);
  }

}
