package com.sell.search.controller;

import com.sell.search.controller.request.IndexAliasRequest;
import com.sell.search.core.domain.EntityType;
import com.sell.search.es.mapping.CustomFieldEsFieldMapping;
import com.sell.search.es.mapping.ElasticsearchMappingFacade;
import com.sell.search.es.mapping.EsCustomFieldMappingService;
import com.sell.search.field.FieldFacade;
import com.sell.search.field.domain.Field;
import io.swagger.v3.oas.annotations.Hidden;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Hidden
@RestController
@Slf4j
@RequestMapping("/v1/esIndex")
public class TenantMasterIndexController {

  private final ElasticsearchMappingFacade elasticsearchMappingFacade;
  private final FieldFacade fieldFacade;
  private final EsCustomFieldMappingService esCustomFieldMappingService;

  @Autowired
  public TenantMasterIndexController(ElasticsearchMappingFacade elasticsearchMappingFacade, FieldFacade fieldFacade,
      EsCustomFieldMappingService esCustomFieldMappingService) {
    this.elasticsearchMappingFacade = elasticsearchMappingFacade;
    this.fieldFacade = fieldFacade;
    this.esCustomFieldMappingService = esCustomFieldMappingService;
  }

  @RequestMapping("/createMasterIndexAlias")
  public Map<Long, Object> createTenantAlias(@RequestBody List<IndexAliasRequest> indexAliasRequests) {
    return elasticsearchMappingFacade.createTenantAliases(indexAliasRequests);
  }

  @RequestMapping("/createMasterIndexAlias-for-entity")
  public Map<Long, Object> createTenantAliasForEntity(@RequestParam("tenantId") long tenantId, @RequestParam("entity") String entity, @RequestParam("planName") String planName) {
    return elasticsearchMappingFacade.createTenantAliasesForEntity(tenantId, entity, planName);
  }

  @RequestMapping("/create-master-index-with-alias-and-field-mapping")
  public Map<String, Object> createTenantAlias(@RequestParam("entityType") String entityType,
      @RequestParam("planName") String planName,
      @RequestParam("indexId") int indexId,
      @RequestParam(value = "deleteIfExists", defaultValue = "false") boolean deleteIfExists,
      @RequestParam(value = "creteAlias", defaultValue = "true") boolean createAlias
      ) {
    return elasticsearchMappingFacade.createMasterIndexWithAliasAndFieldMapping(entityType,planName,indexId, createAlias, deleteIfExists);
  }

  @RequestMapping("/recreate-master-index-without-alias-and-with-field-mapping")
  public Map<String, String> recreateIndexWithoutAliasAndFieldMapping(@RequestParam("indexName") String indexName,
      @RequestParam("entityType") String entityType, @RequestParam("planName") String planName, @RequestParam("indexId") int indexId) {
    return elasticsearchMappingFacade.createIndexAndFieldMappingWithoutAlias(indexName,planName,entityType, indexId);
  }

  @RequestMapping("/mapEsFieldAndCustomField")
  public Map<String, String> mapEsFieldAndTenantCustomField(@RequestParam("tenantId") long tenantId,
      @RequestParam("entityType") String entityType) {

    log.info("TenantMasterIndexController: Message received for mapping ES field and Custom Field: tenantId:{},entityType:{}", tenantId, entityType);

    Map<String, String> fieldMappedWithEsField = new HashMap<>();

    List<Field> fields = new ArrayList<>();

    if (entityType.equalsIgnoreCase("contact")) {
      fields = fieldFacade.getByTenantIdAndEntityAndStandard(tenantId, EntityType.CONTACT, false);
    }

    if (entityType.equalsIgnoreCase("lead")) {

      fields = fieldFacade.getByTenantIdAndEntityAndStandard(tenantId, EntityType.LEAD, false);
      log.info("TenantMasterIndexController: lead has {} custom fields", fields.size());
    }
    if (entityType.equalsIgnoreCase("deal")) {

      fields = fieldFacade.getByTenantIdAndEntityAndStandard(tenantId, EntityType.DEAL, false);
      log.info("TenantMasterIndexController: deal has {} custom fields", fields.size());
    }

    if (entityType.equalsIgnoreCase("company")) {

      fields = fieldFacade.getByTenantIdAndEntityAndStandard(tenantId, EntityType.COMPANY, false);
      log.info("TenantMasterIndexController: company has {} custom fields", fields.size());
    }
    for (Field field : fields) {
      log.info("TenantMasterIndexController: Mapping field with id:{} name:{} and fieldType:{}", field.getId(), field.getName(),
          field.getFieldType());
      CustomFieldEsFieldMapping customFieldEsFieldMapping = esCustomFieldMappingService.mapEsFieldAndCustomField(field);
      fieldMappedWithEsField.put(customFieldEsFieldMapping.getField().getName(), customFieldEsFieldMapping.getEsCustomField().getName());
    }
    return fieldMappedWithEsField;
  }
}
