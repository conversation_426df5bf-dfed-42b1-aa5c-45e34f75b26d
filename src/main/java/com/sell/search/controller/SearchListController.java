package com.sell.search.controller;

import com.sell.search.core.annotation.ApiPageable;
import com.sell.search.domain.SearchList;
import com.sell.search.dto.CreateSearchListRequest;
import com.sell.search.dto.PreferredSearchListRequest;
import com.sell.search.dto.PreferredSearchListResponse;
import com.sell.search.dto.SearchListResponse;
import com.sell.search.dto.UpdateSearchListRequest;
import com.sell.search.search.core.controller.LookupController;
import com.sell.search.search.core.controller.SearchController;
import com.sell.search.search.core.dto.SearchRequest;
import com.sell.search.search.core.dto.SearchResponse;
import com.sell.search.service.SearchListService;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/search-lists")
public class SearchListController implements SearchController, LookupController {

  @Autowired
  private SearchListService searchListService;

  @Operation(description = "Create a search list")
  @PostMapping(value = "/{entityType}", produces = MediaType.APPLICATION_JSON_VALUE)
  public SearchListResponse createSearchList(@PathVariable("entityType") String entityType,
      @Valid @RequestBody CreateSearchListRequest createSearchListRequest) {
    return (searchListService.createSearchList(entityType, createSearchListRequest));
  }

  @Operation(description = "Fetch all search lists containing the given value of search list field")
  @PostMapping(value = "/fetch/{entityType}", produces = MediaType.APPLICATION_JSON_VALUE)
  public Page<SearchListResponse> fetchSearchLists(@PathVariable("entityType") String entityType,
      @Valid @RequestBody SearchRequest searchRequest, @PageableDefault(sort = {"name"}) Pageable pageable) {
    return (searchListService.fetchSearchListsContainingTheFieldValue(entityType, searchRequest, pageable));
  }

  @Operation(description = "Save user's preferred search list")
  @PostMapping(value = "/{searchListId}/mark-as-preferred", produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity savePreferredSearchList(
      @PathVariable("searchListId") Long searchListId,
      @RequestParam("entityType") String entityType,
      @RequestBody(required = false) PreferredSearchListRequest request) {
    if (request == null) {
      return searchListService.savePreferredSearchList(entityType, searchListId, new String[]{}, 0, null, null, null, null);
    }
      return searchListService.savePreferredSearchList(entityType, searchListId, request.getFields(), request.getSize(), request.getSort(), request.getView(),
          request.getPipelineId(), request.getFreezedColumn());
  }

  @Operation(description = "Get user's preferred search list")
  @GetMapping(value = "/preferred", produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<PreferredSearchListResponse> getPreferredSearchList(@RequestParam("entityType") String entityType) {
    return searchListService.getPreferredSearchList(entityType);
  }

  /**
   * Get all available (active & inactive) {@link SearchList} records.
   *
   * @param pageable PageRequest params representing offset, pageSize and pageNumber
   * @return Paginated list of SearchListResponse records - {@link Page}
   * @see org.springframework.data.domain.PageRequest
   */
  @Operation(description = "Get all available search lists")
  @GetMapping(value = "/{entityType}", produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiPageable
  public Page<SearchListResponse> getAllSearchLists(@PathVariable("entityType") String entityType,
      @PageableDefault(sort = {"name"}) Pageable pageable) {
    return (searchListService.getAllSearchLists(entityType, pageable));
  }

  /**
   * Get a {@link SearchList}
   *
   * @return {@link SearchListResponse}
   */
  @Operation(description = "Get a search list")
  @GetMapping(value = "/{entityType}/{searchListId}", produces = MediaType.APPLICATION_JSON_VALUE)
  public SearchListResponse getSearchList(@PathVariable("entityType") String entityType,
      @PathVariable("searchListId") Long searchListId) {
    return (searchListService.getSearchList(entityType, searchListId));
  }

  /**
   * Update a {@link SearchList}
   *
   * @param updateSearchListRequest {@link UpdateSearchListRequest}
   * @return Updated {@link SearchListResponse}
   */
  @Operation(description = "Update a search list")
  @PutMapping(value = "/{entityType}/{searchListId}", produces = MediaType.APPLICATION_JSON_VALUE)
  public SearchListResponse updateSearchList(@PathVariable("entityType") String entityType,
      @PathVariable("searchListId") Long searchListId,
      @Valid @RequestBody UpdateSearchListRequest updateSearchListRequest) {
    return (searchListService.updateSearchList(entityType, searchListId, updateSearchListRequest));
  }

  /**
   * Delete a {@link SearchList}
   *
   * @return Deleted {@link SearchListResponse}
   */
  @Operation(description = "Delete a search list by search list id")
  @DeleteMapping(value = "/{entityType}/{searchListId}")
  public void deleteSearchList(@PathVariable("entityType") String entityType, @PathVariable(value = "searchListId") Long searchListId) {
    searchListService.deleteSearchList(entityType, searchListId);
  }

  @Operation(description = "Get smart lists details by ids")
  @GetMapping("/email")
  public List<SearchListResponse> getSearchListsByIds(@RequestParam("id")List<Long> ids){
    return searchListService.getEmailSearchListsByIds(ids);
  }

  @Override
  public Page<SearchResponse> getResults(Pageable pageable, @Valid @RequestBody SearchRequest searchRequest) {
    return searchListService.getResults(pageable, searchRequest);
  }

  @Override
  public Page<SearchResponse> getLookupResults(Pageable pageable, @RequestParam("q") String fieldAndValue) {
    return searchListService.lookup(fieldAndValue, pageable);
  }
}