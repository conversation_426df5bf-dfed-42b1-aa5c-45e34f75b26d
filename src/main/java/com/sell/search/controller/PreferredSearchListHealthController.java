package com.sell.search.controller;

import com.sell.search.service.PreferredSearchListHealthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/vC2VHCMNOBGLZDGHL/search")
public class PreferredSearchListHealthController {

  private final PreferredSearchListHealthService preferredSearchListHealthService;

  @Autowired
  public PreferredSearchListHealthController(PreferredSearchListHealthService searchListHealthService) {
    this.preferredSearchListHealthService = searchListHealthService;
  }

  @GetMapping(value = "/health", produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity getContactHealth() {
    int searchListHealth = preferredSearchListHealthService.getPreferredSearchListHealth();
    if (searchListHealth == 200) {
      return ResponseEntity.status(HttpStatus.OK).build();
    }
    return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).build();
  }
}
