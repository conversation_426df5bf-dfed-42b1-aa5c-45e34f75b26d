package com.sell.search.controller;

import static org.mapstruct.ap.internal.util.Collections.asSet;

import com.sell.search.controller.response.EntityDetail;
import com.sell.search.controller.response.EntityOwnerPhoneNumberSummary;
import com.sell.search.controller.response.EntitySummary;
import com.sell.search.controller.response.LookUpResponse;
import com.sell.search.core.annotation.ApiPageable;
import com.sell.search.core.domain.EntityType;
import com.sell.search.service.SearchService;
import io.swagger.v3.oas.annotations.Operation;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/summaries")
@Slf4j
public class SummaryController {
  private final SearchService searchService;

  @Autowired
  public SummaryController(SearchService searchService) {
    this.searchService = searchService;
  }

  @Operation(description = "Get summary of users by their Ids")
  @GetMapping(value = "/users", produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiPageable
  public List<LookUpResponse> getUserSummaries(@RequestParam Set<Long> id) {
    return searchService.getLookUpResponseFor(id, EntityType.USER);
  }

  @Operation(description = "Get summaries by their id")
  @GetMapping(value = "/{entityPlural}", produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiPageable
  public ResponseEntity<?> getSummaries(@PathVariable String entityPlural,
      @RequestParam(value = "view", required = false) Optional<String> view,
      @RequestParam(value = "includeConverted", required = false) Boolean includeConverted,
      @RequestParam List<Long> id) {
    
    if (view.isPresent() && "notification".equalsIgnoreCase(view.get())) {
      List<EntityDetail> entityDetails = searchService.getSummaryWithOwnerId(id, entityPlural, view, includeConverted);
      return entityDetails.size() != id.size()
          ? ResponseEntity.notFound().build() : ResponseEntity.ok(entityDetails);
    }
    List<EntitySummary> responses = searchService.getSummary(id, entityPlural, view, includeConverted);
    return responses.size() != id.size()
        ? ResponseEntity.notFound().build() : ResponseEntity.ok(responses);
  }

  @Operation(description = "Get summary of user by id")
  @GetMapping(value = "/user", produces = MediaType.APPLICATION_JSON_VALUE)
  public LookUpResponse getUserSummary(@RequestParam Long id) {
    return getLookUpResponse(id, EntityType.USER);
  }

  @Operation(description = "Get summary of pipeline by id")
  @GetMapping(value = "/pipeline", produces = MediaType.APPLICATION_JSON_VALUE)
  public LookUpResponse getPipelineSummary(@RequestParam Long id) {
    return getLookUpResponse(id, EntityType.PIPELINE);
  }

  @Operation(description = "Get summary of pipeline stage by id")
  @GetMapping(value = "/pipeline-stage", produces = MediaType.APPLICATION_JSON_VALUE)
  public LookUpResponse getPipelineStageSummary(@RequestParam Long id) {
    return getLookUpResponse(id, EntityType.PIPELINE_STAGE);
  }

  @Operation(description = "Get summary of owner phone numbers for lead and contact")
  @GetMapping(value = "/owner-phone-numbers", produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiPageable
  public EntityOwnerPhoneNumberSummary getOwnerPhoneNumbers(Pageable pageable, @RequestParam("phoneNumber") String entityPhoneNumber) {
    return searchService.getOwnerPhoneNumbersForEntities(pageable, entityPhoneNumber);
  }

  @Operation(description = "Get summary of pipelines by ids")
  @GetMapping(value = "/pipelines", produces = MediaType.APPLICATION_JSON_VALUE)
  public List<LookUpResponse> getPipelinesSummaries(@RequestParam List<Long> id) {
    return searchService.getLookUpResponseFor(new HashSet<>(id), EntityType.PIPELINE);
  }

  @Operation(description = "Get summary of pipeline stages by ids")
  @GetMapping(value = "/pipeline-stages", produces = MediaType.APPLICATION_JSON_VALUE)
  public List<LookUpResponse> getPipelineStagesSummaries(@RequestParam List<Long> id) {
    return searchService.getLookUpResponseFor(new HashSet<>(id), EntityType.PIPELINE_STAGE);
  }

  @Operation(description = "Get summary of companies by ids")
  @GetMapping(value = "/companies/idName", produces = MediaType.APPLICATION_JSON_VALUE)
  public List<LookUpResponse> getCompanySummaries(@RequestParam List<Long> id) {
    return searchService.getLookUpResponseFor(new HashSet<>(id), EntityType.COMPANY);
  }

  private LookUpResponse getLookUpResponse(@RequestParam Long id, EntityType entityType) {
    List<LookUpResponse> lookUpResponse = searchService.getLookUpResponseFor(asSet(id), entityType);
    if (lookUpResponse.isEmpty()) {
      log.error("Id name resolution not found for " + entityType.getLabel() + ": " + id);
      return new LookUpResponse(id, "");
    }
    return lookUpResponse.get(0);
  }
}
