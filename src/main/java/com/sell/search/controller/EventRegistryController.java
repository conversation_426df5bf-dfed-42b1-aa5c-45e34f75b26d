package com.sell.search.controller;

import com.sell.search.controller.request.CreateEventRegistry;
import com.sell.search.controller.response.EventRegistryResponse;
import com.sell.search.service.EventRegistryService;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by hemants on 19/02/19. This controller is for registering/listing and de-registering events for syncing with indexer
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/internal/events")
public class EventRegistryController {

  private final EventRegistryService eventRegistryService;

  @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
  @Deprecated
  public boolean createEventRegistry(@Valid @RequestBody CreateEventRegistry createEventRegistry) {
    return true;
  }

  @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
  public List<EventRegistryResponse> getAllEventRegistry() {
    return eventRegistryService.findAll().stream()
        .map(eventRegistry -> new EventRegistryResponse(eventRegistry.getId(), eventRegistry.getServiceName(), eventRegistry.getEventName()))
        .collect(Collectors.toList());
  }
}
