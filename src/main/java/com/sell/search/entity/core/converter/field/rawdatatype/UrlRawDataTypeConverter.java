package com.sell.search.entity.core.converter.field.rawdatatype;

import com.sell.search.entity.core.converter.field.rawdatatype.RawDataTypeConverter;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import org.springframework.util.ObjectUtils;

import java.net.URL;
import java.util.regex.Pattern;

/**
 * Created by hemants on 19/06/19.
 */
public class UrlRawDataTypeConverter implements RawDataTypeConverter {

  private final String urlRegex = "^(https?|ftp|file)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]";


  @Override
  public boolean canConvert(Field definedDataType) {
    return !ObjectUtils.isEmpty(definedDataType.getMultiValue()) && !definedDataType.getMultiValue() && FieldType.URL
        .equals(definedDataType.getType());
  }


  @Override
  public Object convert(Object rawObject, Field field) {
    try {
      boolean validUrl = Pattern.compile(urlRegex).matcher(rawObject.toString()).matches();
      if (validUrl) {
        return new URL(rawObject.toString());
      } else {
        throwException(field.getName(), rawObject, field.getType().toString());
      }
    } catch (Exception e) {
      throwException(field.getName(), rawObject, field.getType().toString());
    }
    return rawObject;
  }

  private void throwException(String k, Object object, String converterName) {
    RawDatatypeExceptionHelper.throwException(k, object, converterName, "field.conversion.error.message");
  }
}
