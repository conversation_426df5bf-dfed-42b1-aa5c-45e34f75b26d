package com.sell.search.entity.core.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

@Slf4j
public class ReadFileUtil {

  public static String readFromFile(String resourcePath) {
    Resource resource = new ClassPathResource(resourcePath);
    InputStream inputStream;
    try {
      inputStream = resource.getInputStream();
      return readFromInputStream(inputStream);
    } catch (IOException ex) {
      log.error("Error occurred while reading from config file.", ex);
    }
    return "";
  }

  private static String readFromInputStream(InputStream inputStream) throws IOException {
    StringBuilder resultStringBuilder = new StringBuilder();
    try (BufferedReader br = new BufferedReader(new InputStreamReader(inputStream))) {
      String line;
      while ((line = br.readLine()) != null) {
        resultStringBuilder.append(line).append("\n");
      }
    }
    return resultStringBuilder.toString();
  }
}
