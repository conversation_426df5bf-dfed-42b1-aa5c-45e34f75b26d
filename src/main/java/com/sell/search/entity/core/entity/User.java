package com.sell.search.entity.core.entity;

import com.sell.search.core.annotation.FieldAttribute;
import com.sell.search.core.domain.EntityType;
import com.sell.search.entity.core.annotation.ExtendedFields;
import com.sell.search.entity.core.annotation.FieldRegex;
import com.sell.search.entity.core.annotation.Internal;
import com.sell.search.entity.core.annotation.LookupField;
import com.sell.search.entity.core.annotation.PicklistField;
import com.sell.search.entity.core.annotation.StringField;
import com.sell.search.entity.core.model.CustomFieldAwareBaseEntity;
import com.sell.search.entity.core.model.DeactivatedReason;
import com.sell.search.entity.core.model.PhoneJsonUserType;
import com.sell.search.entity.core.model.PhoneNumber;
import com.sell.search.entity.core.model.StringTypeField;
import com.sell.search.entity.core.service.IdNameEntry;
import java.util.Date;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

/**
 * Created by hemants on 09/04/19.
 */
@MappedSuperclass
@Getter
@Setter
@TypeDefs({@TypeDef(name = "PhoneJsonUserType", typeClass = PhoneJsonUserType.class)})
@ExtendedFields(value = {"ownerId", "createdAt", "updatedAt", "createdBy", "updatedBy"})
public abstract class User extends CustomFieldAwareBaseEntity implements IdNameEntry {

  private String firstName;

  @NotNull
  private String lastName;

  @PicklistField(value = {"Mr", "Mrs", "Miss"})
  private Long salutation;

  @NotNull
  @FieldRegex(value = "^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$")
  private String email;

  @NotNull
  @Type(type = "PhoneJsonUserType")
  @Column(name = "phone_numbers", columnDefinition = "jsonb")
  private PhoneNumber[] phoneNumbers;

  private String designation;

  @Internal
  @PicklistField(name = "LANG")
  private String language;

  @Internal
  @PicklistField(name = "CURRENCY")
  private String currency;

  @Internal
  @PicklistField(name = "COUNTRY")
  private String country;

  @Internal
  @PicklistField(name = "DATE_FORMAT")
  private String dateFormat;

  @NotNull
  @Internal
  @PicklistField(name = "TIMEZONE")
  private String timezone;

  @StringField(StringTypeField.RICH_TEXT)
  private String signature;

  private String title;
  private String department;

  @Internal
  private Integer failedAttempts;
  @Internal
  private boolean locked;
  @Internal
  private Date lockedAt;

  @Internal
  private boolean active;

  @Enumerated(EnumType.STRING)
  @Internal
  private DeactivatedReason deactivatedReason = DeactivatedReason.PASSWORD_NOT_SET;

  @Internal
  private boolean emailVerified;
  @Internal
  private Date verifiedAt;
  @Internal
  private Date verificationTokenSentAt;

  @Internal
  private Date resetPasswordTokenSentAt;
  @Internal
  private Date unlockTokenSentAt;

  @Transient
  @LookupField(EntityType.PROFILE)
  @FieldAttribute(displayName = "Profile")
  private Long profileId;

  @Override
  public String getName() {
    return Stream.of(firstName, lastName).filter(s -> !StringUtils.isBlank(s)).collect(Collectors.joining(" "));
  }

  public void setEmail(@NotNull String email) {
    this.email = email.toLowerCase();
  }

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getOwnerId() {
    return super.getOwnerId();
  }

  @Override
  @Internal
  public Date getCreatedAt() {
    return super.getCreatedAt();
  }

  @Override
  @Internal
  public Date getUpdatedAt() {
    return super.getUpdatedAt();
  }

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getCreatedBy() {
    return super.getCreatedBy();
  }

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getUpdatedBy() {
    return super.getUpdatedBy();
  }

  abstract public Long getProfileId();

}
