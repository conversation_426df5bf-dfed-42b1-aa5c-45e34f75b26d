package com.sell.search.entity.core.exception;

import com.sell.search.entity.core.exception.ValidationExceptionType;
import java.io.IOException;

/**
 * Created by hemants on 02/04/19.
 * TODO: Need to check if this is required when we start working on field validation
 */
public class FieldValidationException extends IOException {
    ValidationExceptionType validationExceptionType;

    public FieldValidationException(ValidationExceptionType validationExceptionType){
        this.validationExceptionType = validationExceptionType;
    }
}
