package com.sell.search.entity.core.strategy;


import com.sell.search.entity.core.model.DisplayNameAware;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

public class DisplayNameToValueStrategy<T extends DisplayNameAware> {

  public String getValue(T displayNameAware) {
      String[] split = displayNameAware.getDisplayName().replaceAll("[^a-zA-Z0-9]+", " ").trim().split(" ");

      StringBuilder result = new StringBuilder(split[0].toLowerCase());

      Arrays.asList(split).stream().skip(1).forEach(s -> {
          result.append(StringUtils.capitalize(StringUtils.lowerCase(s)));
      });

      return result.toString();
  }
}