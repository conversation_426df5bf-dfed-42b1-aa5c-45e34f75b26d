package com.sell.search.entity.core.validator.field;

import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import com.sell.search.entity.core.validator.field.DataTypeValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by hemants on 19/04/19.
 * The validator class for length validation
 */
@Slf4j
public class LengthValidator implements DataTypeValidator {

  /**
   * Applicable for these field type
   */
  private final List<FieldType> lengthApplicableList = Arrays.asList(
      FieldType.TEXT_FIELD,
      FieldType.SINGLE_LINE_TEXT,
      FieldType.PARAGRAPH_TEXT,
      FieldType.RICH_TEXT);


  /**
   * Validate the custom fields
   *
   * @param customFields
   * @param allowedCustomFields
   */
  @Override
  public boolean validate(Map<String, Object> customFields, Map<String, Field> allowedCustomFields) {
    allowedCustomFields.entrySet().stream()
        .filter(entry -> lengthApplicableList.contains(entry.getValue().getType()))
        .forEach(entry -> {
          if (customFields.containsKey(entry.getKey()) && !ObjectUtils.isEmpty(allowedCustomFields.get(entry.getKey()).getLength())) {
            Field field = allowedCustomFields.get(entry.getKey());
            validate(customFields.get(entry.getKey()), field, entry.getKey());
          }
        });
    return true;
  }

  /**
   * Validate based on if it multi value or not
   *
   * @param value
   * @param field
   * @param key
   */
  private void validate(Object value, Field field, String key) {
    if (!ObjectUtils.isEmpty(field) && !ObjectUtils.isEmpty(field.getMultiValue()) && field.getMultiValue()){
      List<String> listOfString = (List<String>) value;
      listOfString.forEach(s -> validateSingle(s, field, key));
    } else {
      validateSingle((String) value, field, key);
    }
  }

  /**
   * Validate single value
   *
   * @param value
   * @param field
   * @param key
   */
  private void validateSingle(String value, Field field, String key) {
    if (value.length() > field.getLength()) {
      throwException(key, field.getLength());
    }
  }

  /**
   * A common method to throw an exception
   *
   * @param k
   * @param length
   * @return
   */
  private void throwException(String k, int length) {
    ValidatorExceptionHelper.throwException(k, length, "field.validation.constraints.DecimalMax.message");
  }

}
