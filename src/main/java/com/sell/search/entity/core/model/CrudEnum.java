package com.sell.search.entity.core.model;

/**
 * Created by hemants on 27/02/19.
 * crud constant
 */
public enum CrudEnum {
    CREATED("created"),
    UPDATED("updated"),
    DELETED("deleted"),
    OWNER_UPDATED("owner_updated"); //TODO: change to its own separate class
    private final String name;

    CrudEnum(String s) {
        this.name = s;
    }

    @Override
    public String toString() {
        return this.name;
    }

}
