package com.sell.search.entity.core.service;


import java.util.Map;
import java.util.Set;

/**
 * Created by hemants on 27/05/19.
 */
public interface IdNameSearchService {


  /**
   * Takes input for type and its id, example: {"LEAD" : [1,2,3], "PICK_LIST" : [88,23,43]}
   *
   * Return Map group by for type, example: { "LEAD" : {"1" : "abc" , "2" : "cef"}, "PICK_LIST" : {}}
   *
   * @param forTypeAndItsIdList
   * @return
   */
  Map<String, Map<String, Object>> search(Map<String, Set> forTypeAndItsIdList);
}
