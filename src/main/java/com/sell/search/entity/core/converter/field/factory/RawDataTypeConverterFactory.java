package com.sell.search.entity.core.converter.field.factory;

import static com.sell.search.entity.core.model.FieldType.PICK_LIST;
import static java.util.Objects.isNull;

import com.sell.search.core.domain.ErrorCodes;
import com.sell.search.core.exception.BaseException;
import com.sell.search.entity.core.converter.field.rawdatatype.CheckboxRawDataTypeConverter;
import com.sell.search.entity.core.converter.field.rawdatatype.DateRawDataTypeConverter;
import com.sell.search.entity.core.converter.field.rawdatatype.DateTimeRawDataTypeConverter;
import com.sell.search.entity.core.converter.field.rawdatatype.EmailRawDataTypeConverter;
import com.sell.search.entity.core.converter.field.rawdatatype.EntityLookUpRawDataTypeConverter;
import com.sell.search.entity.core.converter.field.rawdatatype.MultiValueRawDataTypeConverter;
import com.sell.search.entity.core.converter.field.rawdatatype.NumberRawDataTypeConverter;
import com.sell.search.entity.core.converter.field.rawdatatype.PhoneRawDataTypeConverter;
import com.sell.search.entity.core.converter.field.rawdatatype.PipelineDataTypeConverter;
import com.sell.search.entity.core.converter.field.rawdatatype.RawDataTypeConverter;
import com.sell.search.entity.core.converter.field.rawdatatype.StringRawDataTypeConverter;
import com.sell.search.entity.core.converter.field.rawdatatype.TimeRawDataTypeConverter;
import com.sell.search.entity.core.converter.field.rawdatatype.ToggleRawDataTypeConverter;
import com.sell.search.entity.core.converter.field.rawdatatype.UrlRawDataTypeConverter;
import com.sell.search.entity.core.entity.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

/** Created by hemants on 19/04/19. The factory class which contains all raw data converter */
@Slf4j
public class RawDataTypeConverterFactory implements RawDataTypeConverter {

  List<RawDataTypeConverter> allConverter = new ArrayList<>();

  /** Initialized all converter which is currently supported in our system */
  public RawDataTypeConverterFactory() {
    allConverter.add(new StringRawDataTypeConverter());
    allConverter.add(new NumberRawDataTypeConverter());
    allConverter.add(new MultiValueRawDataTypeConverter(this));
    allConverter.add(new EmailRawDataTypeConverter());
    allConverter.add(new PhoneRawDataTypeConverter());
    allConverter.add(new DateTimeRawDataTypeConverter());
    allConverter.add(new DateRawDataTypeConverter());
    allConverter.add(new TimeRawDataTypeConverter());
    allConverter.add(new CheckboxRawDataTypeConverter());
    allConverter.add(new ToggleRawDataTypeConverter());
    allConverter.add(new UrlRawDataTypeConverter());
    allConverter.add(new PipelineDataTypeConverter());
    allConverter.add(new EntityLookUpRawDataTypeConverter());
  }

  /**
   * convert the field based on supplied values and configured custom field. It store the converted
   * value in the customFields Map itself so that further code can convert it directly
   *
   * @param customFields
   * @param allowedCustomFields
   */
  public void convert(Map<String, Object> customFields, Map<String, Field> allowedCustomFields) {
    if (ObjectUtils.isEmpty(customFields)) {
      return;
    }
    customFields.forEach((key, value) -> {
      Field field = allowedCustomFields.get(key);
      if (field != null) {
        Object convertedValue = isNull(value) && field.getType().equals(PICK_LIST) ? null
            : convert(value, field);
        customFields.put(key, convertedValue);
      }
    });
  }

  @Override
  public boolean canConvert(Field definedDataType) {
    return false;
  }

  /**
   * Call each converter, check if it is able to convert or not, if able to convert then call it's
   * convert method
   *
   * @param rawObject
   * @param field
   * @return
   */
  @Override
  public Object convert(Object rawObject, Field field) {
    Optional<RawDataTypeConverter> optionalConverter =
        allConverter.stream()
            .filter(
                converter -> {
                  if (ObjectUtils.isEmpty(field.getMultiValue())) {
                    field.setMultiValue(false);
                  }
                  return converter.canConvert(field);
                })
            .findFirst();
    return optionalConverter
        .map(converter -> converter.convert(rawObject, field))
        .orElseThrow(
            () -> {
              log.error(
                  "No converter found to convert field: {} of data type: {}",
                  field.getDisplayName(),
                  field.getType());
              return new BaseException(ErrorCodes.COMMON_INTERNAL_ERROR);
            });
  }
}
