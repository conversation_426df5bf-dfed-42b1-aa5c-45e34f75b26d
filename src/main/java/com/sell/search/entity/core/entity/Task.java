package com.sell.search.entity.core.entity;

import com.sell.search.core.domain.EntityType;
import com.sell.search.entity.core.annotation.EntityLookupField;
import com.sell.search.entity.core.annotation.ExtendedFields;
import com.sell.search.entity.core.annotation.Internal;
import com.sell.search.entity.core.annotation.LookupField;
import com.sell.search.entity.core.annotation.PicklistField;
import com.sell.search.entity.core.annotation.StringField;
import com.sell.search.entity.core.model.CustomFieldAwareBaseEntity;
import com.sell.search.entity.core.model.StringTypeField;
import java.util.Date;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ExtendedFields(value = {"ownerId", "createdAt", "updatedAt", "createdBy", "updatedBy"})
public class Task extends CustomFieldAwareBaseEntity {

  private String name;

  @StringField(StringTypeField.PARAGRAPH_TEXT)
  private String description;

  @PicklistField(value = {"Call", "Follow Up", "Reminder", "Todo"})
  private Long type;
  private Date dueDate;

  @PicklistField(value = {"Open", "In Progress", "Completed","Cancelled"})
  private Long status;

  @PicklistField(value = {"High", "Medium", "Low"})
  private Long priority;

  @LookupField(EntityType.USER)
  private Long assignedTo;

  @EntityLookupField(name = "relation", displayName = "Related Entities")
  private List relation;

  @Internal
  private Date originalDueDate;

  @Internal
  private Date completedAt;

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getOwnerId() {
    return super.getOwnerId();
  }

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getCreatedBy() {
    return super.getCreatedBy();
  }

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getUpdatedBy() {
    return super.getUpdatedBy();
  }

  @Override
  @Internal
  public Date getCreatedAt() {
    return super.getCreatedAt();
  }

  @Override
  @Internal
  public Date getUpdatedAt() {
    return super.getUpdatedAt();
  }
}
