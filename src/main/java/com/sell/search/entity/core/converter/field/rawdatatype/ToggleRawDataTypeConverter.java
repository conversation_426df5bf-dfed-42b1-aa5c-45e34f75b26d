package com.sell.search.entity.core.converter.field.rawdatatype;

import com.sell.search.entity.core.converter.field.rawdatatype.RawDataTypeConverter;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import org.springframework.util.ObjectUtils;

/**
 * Created by shashanks3 on 29/5/19.
 */
public class ToggleRawDataTypeConverter implements RawDataTypeConverter {

  @Override
  public boolean canConvert(Field definedDataType) {
    return !ObjectUtils.isEmpty(definedDataType.getMultiValue()) && !definedDataType.getMultiValue() && FieldType.TOGGLE
        .equals(definedDataType.getType());
  }

  @Override
  public Object convert(Object rawObject, Field field) {

    if (!(rawObject instanceof Boolean) &&
        !(rawObject instanceof String &&
            ((String) rawObject).equalsIgnoreCase("true") || ((String) rawObject).equalsIgnoreCase("false"))) {
      throwException(field.getName(), rawObject, field.getType().toString());
    }
    return rawObject;
  }


  private void throwException(String k, Object object, String converterName) {
    RawDatatypeExceptionHelper.throwException(k, object, converterName, "field.conversion.error.message");
  }
}
