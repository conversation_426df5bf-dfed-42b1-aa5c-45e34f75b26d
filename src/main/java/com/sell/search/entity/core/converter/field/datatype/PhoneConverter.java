package com.sell.search.entity.core.converter.field.datatype;

import com.sell.search.entity.core.converter.field.datatype.DataTypeConverter;
import com.sell.search.entity.core.converter.field.datatype.FieldContext;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import com.sell.search.entity.core.model.PhoneNumber;

/**
 * Created by hemants on 17/04/19.
 */
public class PhoneConverter implements DataTypeConverter {


  /**
   * It supports only below listed java phone classes
   *
   * @param
   * @return
   */
  @Override
  public boolean canConvert(com.sell.search.entity.core.converter.field.datatype.FieldContext fieldContext) {
    return fieldContext.getFieldType().equals(PhoneNumber.class);
  }

  /**
   * return valid field type
   *
   * @param
   * @return
   */
  @Override
  public Field convert(FieldContext fieldContext) {
    return new Field().type(FieldType.PHONE);
  }
}
