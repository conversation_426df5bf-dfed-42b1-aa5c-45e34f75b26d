package com.sell.search.entity.core.converter.field.datatype;


import com.sell.search.entity.core.converter.field.datatype.DataTypeConverter;
import com.sell.search.entity.core.converter.field.datatype.FieldContext;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.Email;
import com.sell.search.entity.core.model.FieldType;

/**
 * Created by hemants on 17/04/19.
 * The email converter
 */
public class EmailConverter implements DataTypeConverter {


  /**
   * It supports only email data type
   *
   * @param
   * @return
   */
  @Override
  public boolean canConvert(com.sell.search.entity.core.converter.field.datatype.FieldContext fieldContext) {
    return fieldContext.getFieldType().equals(Email.class);
  }


  /**
   * return valid field type
   *
   * @param fieldContext
   * @return
   */
  @Override
  public Field convert(FieldContext fieldContext) {
    return new Field().type(FieldType.EMAIL);
  }
}
