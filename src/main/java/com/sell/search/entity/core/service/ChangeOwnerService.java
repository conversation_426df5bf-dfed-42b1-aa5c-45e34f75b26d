package com.sell.search.entity.core.service;

import com.sell.search.core.domain.EntityType;
import com.sell.search.core.domain.TenantOwnerAwareBaseEntity;
import com.sell.search.entity.core.dto.ChangeOwnerResponse;
import com.sell.search.entity.core.exception.OwnerDoesNotExist;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * Created by hemants on 05/04/19. Interface for change of ownership functionality
 */
public interface ChangeOwnerService<T extends TenantOwnerAwareBaseEntity, ID extends Serializable> {

  ChangeOwnerResponse<ID> updateEntityOwner(Long ownerId, ID entityId, List<EntityType> childEntities, boolean skipOwnerCheck)
      throws OwnerDoesNotExist;

  Collection<ChangeOwnerResponse<ID>> updateEntityOwner(ID ownerId, Collection<ID> entityIds, List<EntityType> childEntities)
      throws OwnerDoesNotExist;

}
