package com.sell.search.entity.core.converter.field.datatype;


import com.sell.search.entity.core.converter.field.datatype.DataTypeConverter;
import com.sell.search.entity.core.converter.field.datatype.FieldContext;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;

/**
 * Created by hemants on 17/04/19.
 */
public class ToggleConverter implements DataTypeConverter {


  /**
   * It supports only below listed java boolean classes
   *
   * @param
   * @return
   */
  @Override
  public boolean canConvert(FieldContext fieldContext) {
    return fieldContext.getFieldType().equals(Boolean.class) || fieldContext.getFieldType().equals(boolean.class);
  }

  /**
   * return valid field type
   *
   * @param
   * @return
   */
  @Override
  public Field convert(FieldContext fieldContext) {
    return new Field().type(FieldType.TOGGLE);
  }

}
