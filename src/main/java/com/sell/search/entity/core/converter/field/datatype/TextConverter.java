package com.sell.search.entity.core.converter.field.datatype;


import com.sell.search.entity.core.annotation.FieldRegex;
import com.sell.search.entity.core.annotation.StringField;
import com.sell.search.entity.core.converter.field.datatype.DataTypeConverter;
import com.sell.search.entity.core.converter.field.datatype.FieldContext;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import org.springframework.util.ObjectUtils;

import java.lang.annotation.Annotation;
import java.util.Arrays;

/**
 * Created by hemants on 16/04/19.
 */
public class TextConverter implements DataTypeConverter {

  /**
   * It supports only supports string
   *
   * @param
   * @return
   */
  @Override
  public boolean canConvert(FieldContext fieldContext) {
    return fieldContext.getFieldType().equals(String.class);
  }

  /**
   * return valid field type
   *
   * @param
   * @return
   */
  @Override
  public Field convert(FieldContext fieldContext) {
    Annotation[] annotations = fieldContext.getField().getAnnotationsByType(StringField.class);
    String textType;
    if (annotations.length == 0) {
      textType = FieldType.TEXT_FIELD.name();
    } else {
      textType = ((StringField) annotations[0]).value().name();
    }
    Field field =  new Field().type(findByName(textType)).isMultiValue(false);
    enhanceBasedOnAnnotation(field, fieldContext);
    field.setLength(getLength(field.getType()));
    return field;
  }

  private Integer getLength(FieldType type) {
    if(FieldType.TEXT_FIELD.equals(type)){
      return 255;
    }
    if(FieldType.PARAGRAPH_TEXT.equals(type)){
      return 2550;
    }
    return null;
  }

  private void enhanceBasedOnAnnotation(Field field, FieldContext fieldContext){
    Annotation[] annotations = fieldContext.getField().getAnnotationsByType(FieldRegex.class);
    if(!ObjectUtils.isEmpty(annotations) && annotations.length > 0){
      FieldRegex fieldRegex = (FieldRegex) annotations[0];
      String regex = fieldRegex.value();
      field.setRegex(regex);
    }
  }

  public static FieldType findByName(final String abbr) {
    return Arrays.stream(FieldType.values()).filter(value -> value.name().equals(abbr)).findFirst().orElse(null);
  }
}


