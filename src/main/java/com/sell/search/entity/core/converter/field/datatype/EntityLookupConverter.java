package com.sell.search.entity.core.converter.field.datatype;

import static com.sell.search.entity.core.converter.field.datatype.ConverterUtil.getAnnotationFromFieldGetter;

import com.sell.search.entity.core.annotation.EntityLookupField;
import com.sell.search.entity.core.converter.field.datatype.DataTypeConverter;
import com.sell.search.entity.core.converter.field.datatype.FieldContext;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import java.lang.annotation.Annotation;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class EntityLookupConverter implements DataTypeConverter {

  @Override
  public boolean canConvert(com.sell.search.entity.core.converter.field.datatype.FieldContext fieldContext) {
    return fieldContext.getField().getAnnotationsByType(EntityLookupField.class).length > 0
        || getAnnotationFromFieldGetter(fieldContext.getField(), fieldContext.getFieldClass(), EntityLookupField.class).length > 0;
  }

  @Override
  public Field convert(FieldContext fieldContext) {
    Annotation[] annotations = fieldContext.getField().getAnnotationsByType(EntityLookupField.class);
    Field fieldDataType = new Field().type(FieldType.ENTITY_LOOKUP);
    fieldDataType.setDisplayName(((EntityLookupField) annotations[0]).displayName());
    fieldDataType.setName(((EntityLookupField) annotations[0]).name());
    return fieldDataType;
  }

}
