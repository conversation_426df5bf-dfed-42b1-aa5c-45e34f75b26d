package com.sell.search.entity.core.converter.field.rawdatatype;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.search.entity.core.converter.field.rawdatatype.RawDataTypeConverter;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.Email;
import com.sell.search.entity.core.model.FieldType;
import java.util.Set;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import org.springframework.util.ObjectUtils;

/**
 * Created by hemants on 22/04/19.
 * The Email raw data type converter
 */
public class EmailRawDataTypeConverter implements RawDataTypeConverter {


  /**
   * Only for {@link Email}
   *
   * @param definedDataType
   * @return
   */
  @Override
  public boolean canConvert(Field definedDataType) {
    return !ObjectUtils.isEmpty(definedDataType.getMultiValue()) && !definedDataType.getMultiValue() && FieldType.EMAIL.equals(definedDataType.getType());
  }

  /**
   * try to convert into {@link Email} else raise exception
   *
   * @param rawObject
   * @param definedDataType
   * @return
   */
  @Override
  public Email convert(Object rawObject, Field definedDataType) {
    ObjectMapper objectMapper = new ObjectMapper();
    try {
      Email email =  objectMapper.convertValue(rawObject, Email.class);
      Set<ConstraintViolation<Email>> constraintViolations =  getValidator().validate(email);
      if(!constraintViolations.isEmpty()){
        throwException(definedDataType.getName(), rawObject, "email");
      }
      return email;
    } catch (Exception e) {
      throwException(definedDataType.getName(), rawObject, "email");
      return new Email();
    }
  }

  private Validator getValidator() {
    return Validation.buildDefaultValidatorFactory().getValidator();
  }

  private void throwException(String k, Object object, String converterName) {
    RawDatatypeExceptionHelper.throwException(k, object, converterName, "field.conversion.error.message" );
  }

}
