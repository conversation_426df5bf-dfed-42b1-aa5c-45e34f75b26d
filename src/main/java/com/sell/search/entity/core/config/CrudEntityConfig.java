package com.sell.search.entity.core.config;

import com.sell.search.entity.core.service.DefaultIdNameSearchService;
import com.sell.search.entity.core.service.IdNameSearchService;
import com.sell.search.entity.core.service.LocalFieldDefinitionIdNameResolver;
import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

public class CrudEntityConfig {

  @Value("${newElasticsearch.host:localhost}")
  public String newElasticsearchHost;

  @Value("${newElasticsearch.port:9200}")
  public int newElasticsearchPort;

  @Bean
  public IdNameSearchService idNameSearchService() {
    RestHighLevelClient restHighLevelClient =
        new RestHighLevelClient(
            RestClient.builder(new HttpHost(newElasticsearchHost, newElasticsearchPort, "http")));
    return new DefaultIdNameSearchService(restHighLevelClient);
  }

  @Bean
  public LocalFieldDefinitionIdNameResolver getLocalFieldDefinitionIdNameResolver() {
    return new LocalFieldDefinitionIdNameResolver(idNameSearchService());
  }
}
