package com.sell.search.entity.core.entity;

import com.sell.search.core.annotation.AccessPermission;
import com.sell.search.core.domain.Action;
import com.sell.search.core.domain.EntityType;
import com.sell.search.core.utils.SecurityUtil;
import com.sell.search.entity.core.annotation.Eventable;
import com.sell.search.entity.core.model.DisplayNameAware;
import com.sell.search.entity.core.model.FieldType;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;

@Setter
@Getter
@Eventable
@Where(clause = "deleted=false")
@AccessPermission("config")
public class Field implements Serializable, DisplayNameAware {

  private Long id;

  protected boolean deleted;

  private Integer version;

  private Action recordActions;

  private Map<String, Object> metaData;
  private Long tenantId;

  private boolean explicitTenantId;


  private Date createdAt;

  private Date updatedAt;

  private Long createdBy;

  private Long updatedBy;
  private String displayName;

  private String description;

  private FieldType type;

  private Class internalType;

  private String name;

  private EntityDef entityDef;

  private EntityType entityType;

  private Boolean standard;

  private Boolean sortable;
  private Boolean filterable;
  private Boolean required;
  private boolean important;
  private boolean active;

  private Boolean multiValue;

  private Integer length;

  private Boolean isUnique;

  private String greaterThan; //String so that it can be used for various type

  private String lessThan; //String so that it can be used for various type

  private String lookupForEntity;

  private Boolean internal;

  private String lookupUrl;

  private boolean skipIdNameResolution;

  private Picklist picklist;

  private String regex;

  private String primaryField;

  private ColorConfiguration colorConfiguration;

  private MaskConfiguration maskConfiguration;

  private void setExplicitTenant() {
    if (!isExplicitTenantId())
      setTenantId(SecurityUtil.getTenantId());
  }
  public Field type(FieldType type) {
    this.type = type;
    return this;
  }

  public Field isMultiValue(boolean isMultiValue) {
    multiValue = isMultiValue;
    return this;
  }

}
