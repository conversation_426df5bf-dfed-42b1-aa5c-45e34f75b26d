package com.sell.search.entity.core.validator.field;

import com.sell.search.core.domain.ErrorResource;
import com.sell.search.core.domain.FieldErrorResource;
import com.sell.search.entity.core.exception.CoreEntityErrorCodes;
import com.sell.search.entity.core.exception.EntityException;

import java.util.Arrays;

/**
 * Created by hemants on 29/04/19.
 */
class ValidatorExceptionHelper {

  /**
   * A common method to throw an exception
   *
   * @param k
   * @param value
   * @param messageKey
   * @return
   */
  static void throwException(String k, Object value, String messageKey) {
    ErrorResource eR = new ErrorResource(CoreEntityErrorCodes.COMMON_VALIDATION_ERROR.getCode(),
        CoreEntityErrorCodes.COMMON_VALIDATION_ERROR.getMessage());
    eR.fieldErrors(Arrays.asList(new FieldErrorResource(k, messageKey, value)));
    throw new EntityException(eR);
  }
}
