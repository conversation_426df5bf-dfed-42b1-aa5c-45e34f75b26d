package com.sell.search.entity.core.model;

import com.sell.search.entity.core.model.Email;
import com.sell.search.entity.core.model.JsonType;
import java.util.Arrays;

/**
 * Created by hemants on 08/04/19.
 */
public class EmailJsonUserType extends JsonType {

  @Override
  public Class returnedClass() {
    return Email[].class;
  }

  @Override
  public boolean equals(Object obj1, Object obj2) {
    Email[] e1 = (Email[]) obj1;
    Email[] e2 = (Email[]) obj2;
    return Arrays.equals(e1, e2);
  }

  @Override
  public int hashCode(Object o) {
    return super.hashCode(o);
  }
}
