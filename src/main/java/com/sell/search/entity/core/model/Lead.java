package com.sell.search.entity.core.model;

import com.sell.search.core.annotation.FieldAttribute;
import com.sell.search.core.domain.EntityType;
import com.sell.search.entity.core.annotation.ExtendedFields;
import com.sell.search.entity.core.annotation.Internal;
import com.sell.search.entity.core.annotation.LookupField;
import com.sell.search.entity.core.annotation.PicklistField;
import com.sell.search.entity.core.annotation.PipelineField;
import com.sell.search.entity.core.annotation.SkipIdNameResolution;
import com.sell.search.entity.core.annotation.UrlField;
import com.sell.search.entity.core.model.Product;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

@TypeDefs({
    @TypeDef(name = "PhoneJsonUserType", typeClass = PhoneJsonUserType.class),
    @TypeDef(name = "EmailJsonUserType", typeClass = EmailJsonUserType.class),
    @TypeDef(name = "ProductJsonType", typeClass = ProductJsonType.class)
})
@Getter
@Setter
public class Lead extends CustomFieldAwareBaseEntity {

  private String firstName;
  private String lastName;

  @Type(type = "PhoneJsonUserType")
  @Column(name = "phone_numbers", columnDefinition = "jsonb")
  private PhoneNumber[] phoneNumbers;

  // TODO: Add validation to the following
  @PicklistField(value = {"Mr", "Mrs", "Miss"})
  private Long salutation;


  @Type(type = "EmailJsonUserType")
  @Column(name = "emails", columnDefinition = "jsonb")
  private Email[] emails;

  @Internal
  @PicklistField(name = "TIMEZONE")
  private String timezone;

  private String city;
  private String state;
  private String zipcode;

  @Internal
  @PicklistField(name = "COUNTRY")
  private String country;

  // End todo add validation
  private String department;

  @FieldAttribute(displayName = "Do Not Disturb")
  private Boolean dnd;

  @UrlField
  private String facebook;
  @UrlField
  private String twitter;
  @UrlField
  private String linkedIn;

  private String address;

  // pipeline & stages
  @PipelineField(fieldType = FieldType.PIPELINE,lookupFor = EntityType.PIPELINE)
  private Long pipeline;
  // TODO: PipelineStage has to be a dependent lookup of pipeline
  @LookupField(EntityType.PIPELINE_STAGE)
  @FieldAttribute(displayName = "Pipeline Stage")
  private Long pipelineStage;

  private String pipelineStageReason;

  // company
  private String companyName;
  private String companyAddress;
  private String companyCity;
  private String companyState;
  private String companyZipcode;

  @Internal
  @PicklistField(name = "COUNTRY")
  private String companyCountry;


  @PicklistField(value = {"1-4", "5-9", "10-19", "20-49", "50-99", "100-249", "250-499", "500-999", "1000+"})
  private Integer companyEmployees;
  private Double companyAnnualRevenue;

  @UrlField
  private String companyWebsite;

  @Type(type = "PhoneJsonUserType")
  @Column(columnDefinition = "jsonb")
  private PhoneNumber[] companyPhones;

  @Internal
  @PicklistField(name = "INDUSTRY")
  private String companyIndustry;

  @Internal
  @PicklistField(name = "BUSINESS_TYPE")
  @FieldAttribute(displayName = "Business Type")
  private String companyBusinessType;

  // requirement
  @FieldAttribute(displayName = "Requirement")
  private String requirementName;

  @Internal
  @PicklistField(name = "CURRENCY")
  @FieldAttribute(displayName = "Currency")
  private String requirementCurrency;

  @FieldAttribute(displayName = "Budget")
  private Double requirementBudget;
  private Date expectedClosureOn;
  
  private Date actualClosureDate;

  @LookupField(value = EntityType.PRODUCT, internalType = Product.class)
  @SkipIdNameResolution
  @FieldAttribute(displayName = "Products or Services")
  private Product[] products;

  @Internal
  private Date convertedAt;

  @Internal
  @LookupField(EntityType.USER)
  private Long convertedBy;

  private String designation;

  @PicklistField(value = {"Organic"})
  private String campaign;

  @PicklistField(value = {"Google", "Facebook", "LinkedIn", "Exhibition", "Cold Calling"})
  private String source;

  @Internal
  @LookupField(EntityType.USER)
  private Long importedBy;

  @Internal
  private String createdViaId;

  @Internal
  private String createdViaName;

  @Internal
  private String createdViaType;

  @Internal
  private String updatedViaId;

  @Internal
  private String updatedViaName;

  @Internal
  private String updatedViaType;

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getOwnerId() {
    return super.getOwnerId();
  }

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getCreatedBy() {
    return super.getCreatedBy();
  }

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getUpdatedBy() {
    return super.getUpdatedBy();
  }

  @Override
  @Internal
  public Date getCreatedAt() {
    return super.getCreatedAt();
  }

  @Override
  @Internal
  public Date getUpdatedAt() {
    return super.getUpdatedAt();
  }
}
