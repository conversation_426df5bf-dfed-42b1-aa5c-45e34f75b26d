package com.sell.search.entity.core.service;


import com.sell.search.core.utils.SecurityUtil;
import com.sell.search.entity.core.config.IdNameConfig;
import com.sell.search.entity.core.service.AbstractIdNameService;
import com.sell.search.entity.core.service.IdNameSearchService;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.Strings;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;

@Slf4j
public class DefaultIdNameSearchService extends AbstractIdNameService implements IdNameSearchService {

  public DefaultIdNameSearchService(RestHighLevelClient newClient) {
    super(newClient);
  }

  @Override
  public Map<String, Map<String, Object>> search(Map<String, Set> forTypeAndItsIdList) {
    if(forTypeAndItsIdList == null || forTypeAndItsIdList.isEmpty()){
      return new HashMap<>();
    }
    int totalDocs = forTypeAndItsIdList.entrySet()
        .stream()
        .map(stringSetEntry -> stringSetEntry.getValue().size())
        .mapToInt(Integer::intValue)
        .sum();
    SearchRequest searchRequest = new SearchRequest(getIndexName());

    SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
    searchSourceBuilder.size(totalDocs);
    BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();

    forTypeAndItsIdList.keySet().stream().filter(type -> !"COMPANY".equalsIgnoreCase(type))
        .filter(type -> !"DEAL".equalsIgnoreCase(type))
        .forEach(k ->
            mainQueryBuilder.should(
                QueryBuilders.boolQuery()
                    .must(QueryBuilders.termsQuery(IdNameConfig.ID, forTypeAndItsIdList.get(k)))
                    .must(QueryBuilders.termQuery(IdNameConfig.FOR_TYPE, k)
                    )));

    searchSourceBuilder.query(
        mainQueryBuilder
    );
    searchRequest.source(searchSourceBuilder);
    try {
      SearchResponse searchResponse = newClient.search(searchRequest, RequestOptions.DEFAULT);

      Map<String, Map<String, Object>> idNameMapping = groupByForType(searchResponse);

      forTypeAndItsIdList.entrySet()
          .stream()
          .filter(stringSetEntry -> stringSetEntry.getKey().equals("COMPANY") || stringSetEntry.getKey().equals("DEAL"))
          .forEach(entry -> {
            idNameMapping.put(entry.getKey(), getIdNameForCompanyOrDealEntity(forTypeAndItsIdList.get(entry.getKey()), entry.getKey().toLowerCase(),totalDocs ));
          });
      return idNameMapping;
    } catch (IOException e) {
      log.error("Error while fetching name for ids {} , {} ", forTypeAndItsIdList, e);
      return new HashMap<>();
    }

  }

  private Map<String, Object> getIdNameForCompanyOrDealEntity(Set ids, String indexName, int totalDocs) {
    Map<String, Object> mapping = new HashMap<>();
    if(ids.isEmpty()){
      return mapping;
    }
    SearchRequest searchRequest = new SearchRequest(SecurityUtil.getTenantId() + "-" + indexName);
    SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
    searchSourceBuilder.size(totalDocs);
    BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();

    mainQueryBuilder.should(
        QueryBuilders.boolQuery()
            .must(QueryBuilders.termsQuery(IdNameConfig.ID, ids)));

    searchSourceBuilder.query(
        mainQueryBuilder
    );

    FetchSourceContext fetchSourceContext =
        new FetchSourceContext(
            true, new String[]{"id", "name"}, Strings.EMPTY_ARRAY);
    searchSourceBuilder.fetchSource(fetchSourceContext);

    searchRequest.source(searchSourceBuilder);

    try {
      SearchResponse searchResponse = getSearchResponse(searchRequest);

      List<Map<String, Object>> searchResponseAsMap = fromEsSearchResponseToMap(searchResponse);
      searchResponseAsMap.forEach(m -> {
        String id = m.get("id").toString();
        String name = m.get("name").toString();
        mapping.put(id, name);
      });
    } catch (ElasticsearchStatusException esse) {
      log.error("Error while fetching company name for ids {} ,{} ", ids, esse);
      return new HashMap<>();
    } catch (IOException e) {
      log.error("Error while fetching company name for ids {} ,{} ", ids, e);
      return new HashMap<>();
    }
    return mapping;
  }

  private SearchResponse getSearchResponse(SearchRequest searchRequest) throws IOException {
      return newClient.search(searchRequest, RequestOptions.DEFAULT);
  }

  public Map<String, Map<String, Object>> groupByForType(SearchResponse searchResponse) {
    List<Map<String, Object>> searchResponseAsMap = fromEsSearchResponseToMap(searchResponse);
    Map<String, Map<String, Object>> result = new HashMap<>();
    searchResponseAsMap.forEach(m -> {
      String forType = m.get(IdNameConfig.FOR_TYPE).toString();
      if (!result.containsKey(forType)) {
        result.put(forType, new HashMap<>());
      }
      result.get(forType).put(m.get(IdNameConfig.ID).toString(), m.get(IdNameConfig.NAME));
    });
    return result;

  }

  public List<Map<String, Object>> fromEsSearchResponseToMap(SearchResponse searchResponse) {
    return Arrays.stream(searchResponse.getHits().getHits())
        .map(SearchHit::getSourceAsMap).collect(Collectors.toList());
  }


}
