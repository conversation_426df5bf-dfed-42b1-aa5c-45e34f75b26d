package com.sell.search.entity.core.converter.field.datatype;

import static com.sell.search.entity.core.converter.field.datatype.ConverterUtil.getAnnotationFromFieldGetter;

import com.sell.search.entity.core.annotation.LookupField;
import com.sell.search.entity.core.annotation.PicklistField;
import com.sell.search.entity.core.annotation.PipelineField;
import com.sell.search.entity.core.converter.field.datatype.DataTypeConverter;
import com.sell.search.entity.core.converter.field.datatype.FieldContext;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import java.lang.annotation.Annotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

@Slf4j
public class NumberConverter implements DataTypeConverter {

  @Override
  public boolean canConvert(FieldContext fieldContext) {
    java.lang.reflect.Field field = fieldContext.getField();
    Class fieldType = fieldContext.getFieldType();
    Annotation[] picklistAnnotations = field.getAnnotationsByType(PicklistField.class);
    Annotation[] lookupAnnotations = field.getAnnotationsByType(LookupField.class);
    Annotation[] pipelineAnnotations = field.getAnnotationsByType(PipelineField.class);
    lookupAnnotations = ObjectUtils.isEmpty(lookupAnnotations) ? getAnnotationFromFieldGetter(field, fieldContext.getFieldClass(), LookupField.class)
        : lookupAnnotations;

    return (fieldType.equals(long.class) ||
        fieldType.equals(Long.class) ||
        fieldType.equals(double.class) ||
        fieldType.equals(Double.class) ||
        fieldType.equals(float.class) ||
        fieldType.equals(Float.class) ||
        fieldType.equals(int.class) ||
        fieldType.equals(Integer.class)) && picklistAnnotations.length == 0 && lookupAnnotations.length == 0 && pipelineAnnotations.length == 0;
  }

  @Override
  public Field convert(FieldContext fieldContext) {
    return new Field().type(FieldType.NUMBER);
  }

}