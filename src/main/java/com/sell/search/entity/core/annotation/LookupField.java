package com.sell.search.entity.core.annotation;

import com.sell.search.core.domain.EntityType;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.math.BigDecimal;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.METHOD})
public @interface LookupField {

  public EntityType value();

  Class internalType() default BigDecimal.class;
}
