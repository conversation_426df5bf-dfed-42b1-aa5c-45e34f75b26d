package com.sell.search.entity.core.util;

import javax.xml.bind.DatatypeConverter;
import org.springframework.security.core.Authentication;
import org.springframework.util.SerializationUtils;

public class AuthenticationSerializer {

  public static String serialize(Authentication authentication) {
    byte[] bytes = SerializationUtils.serialize(authentication);
    return DatatypeConverter.printBase64Binary(bytes);
  }

  public static Authentication deserialize(String authentication) {
    byte[] decoded = DatatypeConverter.parseBase64Binary(authentication);
    return (Authentication) SerializationUtils.deserialize(decoded);
  }
}