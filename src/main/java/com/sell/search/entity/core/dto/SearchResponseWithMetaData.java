package com.sell.search.entity.core.dto;

import com.sell.search.entity.core.dto.MetaData;
import org.elasticsearch.common.unit.TimeValue;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by hemants on 29/05/19.
 */
public class SearchResponseWithMetaData implements MetaData<Map<String, Object>> {
  private Map<String, Object> metaData;
  private List<Map<String, Object>> searchResponse;
  private List<String> fields;
  private TimeValue timeValue;
  private long total;


  public SearchResponseWithMetaData() {
    metaData = new HashMap<>();
    searchResponse = new ArrayList<>();
  }

  public List<Map<String, Object>> getSearchResponse() {
    return searchResponse;
  }

  public void setSearchResponse(List<Map<String, Object>> searchResponse) {
    this.searchResponse = searchResponse;
  }

  @Override
  public void setMetaData(Map<String, Object> metaData) {
    this.metaData = metaData;
  }

  @Override
  public Map<String, Object> getMetaData() {
    return this.metaData;
  }

  public TimeValue getTimeValue() {
    return timeValue;
  }

  public void setTimeValue(TimeValue timeValue) {
    this.timeValue = timeValue;
  }

  public List getFields() {
    return fields;
  }

  public void setFields(List<String> fields) {
    this.fields = fields;
  }

  public long getTotal() {
    return total;
  }

  public void setTotal(long total) {
    this.total = total;
  }
}
