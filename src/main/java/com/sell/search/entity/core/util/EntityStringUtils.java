package com.sell.search.entity.core.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * Created by hemants on 26/04/19.
 */
public class EntityStringUtils {

  private EntityStringUtils() {
    //
  }

  public static final UnaryOperator<String[]> lowerCase = strArray -> {
    List<String> list = Arrays.stream(strArray).map(s -> s.toLowerCase()).collect(Collectors.toList());
    return list.toArray(new String[list.size()]);
  };

  public static final Function<String[], String> joinBySpace = strArray -> {
    if (ObjectUtils.isEmpty(strArray)) {
      return "";
    }
    return String.join(" ", strArray);
  };

  public static final Function<String[], String> joinByUnderscore = strArray -> {
    if (ObjectUtils.isEmpty(strArray)) {
      return "";
    }
    return String.join("_", strArray);
  };

  public static final UnaryOperator<String[]> capitalizeFirstCharacterOfFirstString = strArray -> {
    if (ObjectUtils.isEmpty(strArray)) {
      return strArray;
    }
    String result[] = new String[strArray.length];

    if (ObjectUtils.isEmpty(strArray) || strArray.length == 0) {
      return strArray;
    }
    result[0] = StringUtils.capitalize(strArray[0]);
    System.arraycopy(strArray, 1, result, 1, strArray.length - 1);
    return result;
  };

  public static final Function<String, String[]> splitByCamelCase = str -> {
    if (str == null) {
      return null;
    }
    if (str.length() == 0) {
      return new String[0];
    }
    char[] c = str.toCharArray();
    List<String> list = new ArrayList<>();
    int tokenStart = 0;
    int currentType = Character.getType(c[tokenStart]);
    for (int pos = tokenStart + 1; pos < c.length; pos++) {
      int type = Character.getType(c[pos]);
      if (type == currentType) {
        continue;
      }
      if (type == Character.LOWERCASE_LETTER && currentType == Character.UPPERCASE_LETTER) {
        int newTokenStart = pos - 1;
        if (newTokenStart != tokenStart) {
          list.add(new String(c, tokenStart, newTokenStart - tokenStart));
          tokenStart = newTokenStart;
        }
      } else {
        list.add(new String(c, tokenStart, pos - tokenStart));
        tokenStart = pos;
      }
      currentType = type;
    }
    list.add(new String(c, tokenStart, c.length - tokenStart));
    return list.toArray(new String[list.size()]);
  };

}
