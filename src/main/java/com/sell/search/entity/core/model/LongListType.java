package com.sell.search.entity.core.model;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.search.entity.core.model.JsonType;
import java.io.IOException;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import org.hibernate.engine.spi.SessionImplementor;

public class LongListType extends JsonType {

  @Override
  public Class<List<Long>> returnedClass() {
    return (Class<List<Long>>) (Class<?>) List.class;
  }

  @Override
  public Object nullSafeGet(
      ResultSet resultSet, String[] strings, SessionImplementor sessionImplementor, Object o)
      throws SQLException {

    final String cellContent = resultSet.getString(strings[0]);
    if (cellContent == null) {
      return null;
    }
    try {
      final ObjectMapper mapper = new ObjectMapper();
      return mapper.readValue(cellContent.getBytes("UTF-8"), new TypeReference<List<Long>>(){});
    } catch (final IOException e) {
      throw new RuntimeException("Failed to convert String to Invoice: " + e.getMessage(), e);
    }
  }
}
