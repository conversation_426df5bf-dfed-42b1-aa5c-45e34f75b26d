package com.sell.search.entity.core.config;

import com.sell.search.entity.core.config.CrudEntityConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.util.ObjectUtils;

/**
 * Created by hemants on 04/04/19.
 * The configuration class for custom field functionality
 */
public class CustomFieldEntityConfig extends CrudEntityConfig {

  @Autowired(required = false)
  public void updateMessageSource(ReloadableResourceBundleMessageSource messageSource) {
    if (!ObjectUtils.isEmpty(messageSource)) {
      messageSource.addBasenames("classpath:entityCoreMessages");
    }
  }
}

