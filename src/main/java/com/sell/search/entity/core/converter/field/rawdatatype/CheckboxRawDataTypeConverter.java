package com.sell.search.entity.core.converter.field.rawdatatype;

import com.sell.search.entity.core.converter.field.rawdatatype.RawDataTypeConverter;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import org.springframework.util.ObjectUtils;

/**
 * Created by hemants on 30/04/19.
 * The checkbox raw data type converter
 */
public class CheckboxRawDataTypeConverter implements RawDataTypeConverter {

  @Override
  public boolean canConvert(Field definedDataType) {
    return !ObjectUtils.isEmpty(definedDataType.getMultiValue()) && !definedDataType.getMultiValue() && FieldType.CHECKBOX.equals(definedDataType.getType());
  }


  //TODO: Implement checkbox once picklist is done
  @Override
  public Object convert(Object rawObject, Field definedDataType) {
    return rawObject;
  }
}
