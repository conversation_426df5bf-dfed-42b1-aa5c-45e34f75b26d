package com.sell.search.entity.core.annotation;

import com.sell.search.core.domain.EntityType;
import com.sell.search.entity.core.model.FieldType;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.math.BigDecimal;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.METHOD})
public @interface PipelineField {

  public FieldType fieldType();
  public EntityType lookupFor();
  Class internalType() default BigDecimal.class;
}
