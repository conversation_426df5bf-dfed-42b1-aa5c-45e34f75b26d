package com.sell.search.entity.core.converter.field.datatype;


import com.sell.search.entity.core.converter.field.datatype.DataTypeConverter;
import com.sell.search.entity.core.converter.field.datatype.FieldContext;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.Date;

/**
 * Created by hemants on 17/04/19.
 * The date converter
 */
public class DateConverter implements DataTypeConverter {

  /**
   * It supports only below listed java data classes
   *
   * @param
   * @return
   */
  @Override
  public boolean canConvert(FieldContext fieldContext) {
    Class clazz = fieldContext.getFieldType();
    return clazz.equals(Date.class) ||
        clazz.equals(LocalDateTime.class) ||
        clazz.equals(LocalDate.class) ||
        clazz.equals(OffsetDateTime.class);
  }

  /**
   * return valid field type
   *
   * @param
   * @return
   */
  @Override
  public Field convert(FieldContext fieldContext) {
    return new Field().type(FieldType.DATETIME_PICKER);
  }
}