package com.sell.search.entity.core.validator.field;

import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import com.sell.search.entity.core.validator.field.DataTypeValidator;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by hemants on 22/04/19.
 * The validator class for greater than and less than validation
 */
public class GreaterLessThanValidator implements DataTypeValidator {

  /**
   * Only work for those fieldType
   */
  private final List<FieldType> greaterLessThanApplicableList = Arrays.asList(
      FieldType.NUMBER,
      FieldType.DATE_PICKER,
      FieldType.TIME_PICKER
  );

  /**
   * Validate the custom fields
   *
   * @param customFields
   * @param allowedCustomFields
   */
  @Override
  public boolean validate(Map<String, Object> customFields, Map<String, Field> allowedCustomFields) {
    allowedCustomFields.entrySet().stream()
        .filter(entry -> greaterLessThanApplicableList.contains(entry.getValue().getType()))
        .forEach(entry -> {
          if (customFields.containsKey(entry.getKey())) {
            Field field = allowedCustomFields.get(entry.getKey());
            validateBasedOnType(customFields.get(entry.getKey()), field, entry.getKey());
          }
        });
    return true;
  }

  /**
   * Split the validation based on its type
   *
   * @param value
   * @param field
   * @param key
   */
  private void validateBasedOnType(Object value, Field field, String key) {
    switch (field.getType()) {
      case NUMBER:
        validateNumber(value, field, key);
        break;
      case DATE_PICKER:
        validateDate(value, field, key);
        break;
      case TIME_PICKER:
        validateTime(value, field, key);
        break;
      default:
        throw new RuntimeException("Not a validate type to validate. Type is : " + field.getType()); //TODO: handle exception properly
    }
  }


  /**
   * Validation for number
   *
   * @param valueToValidate
   * @param field
   * @param key
   */
  private void validateNumber(Object valueToValidate, Field field, String key) {
    if (!ObjectUtils.isEmpty(field.getMultiValue()) && field.getMultiValue()) {
      Field copied = cloneForNonMultiValue(field);
      List<BigDecimal> listOfValues = (List<BigDecimal>) valueToValidate;
      listOfValues.forEach(v -> validateNumber(v, copied, key));
    } else {
      BigDecimal value = (BigDecimal) valueToValidate;
      if (!ObjectUtils.isEmpty(field.getLessThan())) {
        compareLessThan(new BigDecimal(field.getLessThan()), value, key);
      }
      if (!ObjectUtils.isEmpty(field.getGreaterThan())) {
        compareGreaterThan(new BigDecimal(field.getGreaterThan()), value, key);
      }
    }
  }


  /**
   * Validation for date
   *
   * @param valueToValidate
   * @param field
   * @param key
   */
  private void validateDate(Object valueToValidate, Field field, String key) {
    LocalDate value = (LocalDate) valueToValidate;
    if (!ObjectUtils.isEmpty(field.getLessThan())) {
      compareLessThan(LocalDate.parse(field.getLessThan()), value, key);
    }
    if (!ObjectUtils.isEmpty(field.getGreaterThan())) {
      compareGreaterThan(LocalDate.parse(field.getGreaterThan()), value, key);
    }
  }

  /**
   * Validation for time
   *
   * @param valueToValidate
   * @param field
   * @param key
   */
  private void validateTime(Object valueToValidate, Field field, String key) {
    OffsetTime value = (OffsetTime) valueToValidate;
    if (!ObjectUtils.isEmpty(field.getLessThan())) {
      compareLessThan(OffsetTime.parse(field.getLessThan()), value, key);
    }
    if (!ObjectUtils.isEmpty(field.getGreaterThan())) {
      compareGreaterThan(OffsetTime.parse(field.getGreaterThan()), value, key);
    }
  }


  /**
   * For clonning a existing field, this is required in multivalue field
   *
   * @param field
   * @return
   */
  private Field cloneForNonMultiValue(Field field) {
    Field copied = new Field();
    copied.setName(field.getName());
    copied.setMultiValue(false);
    copied.setType(field.getType());
    copied.setGreaterThan(field.getGreaterThan());
    copied.setLessThan(field.getLessThan());
    return copied;
  }


  /**
   * A common method to throw an exception
   *
   * @param k
   * @param length
   * @param messageKey
   * @return
   */
  private void throwException(String k, String length, String messageKey) {
    ValidatorExceptionHelper.throwException(k, length, messageKey);
  }

  /**
   * parametrised Compare less than value based supplied value
   *
   * @param lessThanValue
   * @param value
   * @param key
   * @param <T>
   */
  private <T extends Comparable> void compareLessThan(T lessThanValue, T value, String key) {
    if (value.compareTo(lessThanValue) > 0) {
      throwException(key, lessThanValue.toString(), "field.validation.constraints.DecimalMax.message");
    }
  }

  /**
   * parametrised Compare greater than value based supplied value
   *
   * @param greaterThanValue
   * @param value
   * @param key
   * @param <T>
   */
  private <T extends Comparable> void compareGreaterThan(T greaterThanValue, T value, String key) {
    if (value.compareTo(greaterThanValue) < 0) {
      throwException(key, greaterThanValue.toString(), "field.validation.constraints.DecimalMin.message");
    }
  }

}
