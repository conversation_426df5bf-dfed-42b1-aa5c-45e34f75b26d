package com.sell.search.entity.core.converter.field.rawdatatype;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.search.entity.core.converter.field.rawdatatype.RawDataTypeConverter;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import com.sell.search.entity.core.model.PhoneNumber;
import org.springframework.util.ObjectUtils;

/**
 * Created by hemants on 26/04/19.
 * The phone raw data type converter
 */
public class PhoneRawDataTypeConverter implements RawDataTypeConverter {

  /**
   * Only for {@link PhoneNumber}
   * @param definedDataType
   * @return
   */
  @Override
  public boolean canConvert(Field definedDataType) {
    return !ObjectUtils.isEmpty(definedDataType.getMultiValue()) && !definedDataType.getMultiValue() && FieldType.PHONE.equals(definedDataType.getType());
  }

  /**
   * try to convert into {@link PhoneNumber} else raise exception
   * @param rawObject
   * @param definedDataType
   * @return
   */
  //TODO: Pending google phone lib integration to check if phone number is valid or not.
  @Override
  public Object convert(Object rawObject, Field definedDataType) {
    ObjectMapper objectMapper = new ObjectMapper();
    try {
      return objectMapper.convertValue(rawObject, PhoneNumber.class);
    } catch (Exception e) {
      throwException(definedDataType.getName(), rawObject, "Phone");
    }
    return rawObject;
  }


  private void throwException(String k, Object object, String converterName) {
    RawDatatypeExceptionHelper.throwException(k, object, converterName, "field.conversion.error.message" );
  }
}
