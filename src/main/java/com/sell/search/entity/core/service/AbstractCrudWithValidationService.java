package com.sell.search.entity.core.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.search.core.domain.BaseEntity;
import com.sell.search.core.domain.ErrorCodes;
import com.sell.search.core.exception.ResourceNotFoundException;
import com.sell.search.core.repository.BaseJpaRepository;
import com.sell.search.entity.core.dto.SearchResponseDetailsDTO;
import com.sell.search.entity.core.dto.SearchResponseWithMetaData;
import com.sell.search.entity.core.exception.CoreEntityErrorCodes;
import com.sell.search.entity.core.exception.EntityException;
import com.sell.search.entity.core.service.CrudWithValidationService;
import com.sell.search.entity.core.service.LocalFieldDefinitionIdNameResolver;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

/**
 * Created by hemants on 06/04/19.
 * The abstract implementation of change of crud functionality with validation
 */
@Slf4j
public abstract class AbstractCrudWithValidationService<T extends BaseEntity, ID extends Serializable> implements CrudWithValidationService<T, ID> {

  BaseJpaRepository<T, ID> jpaRepository;

  @Autowired
  LocalFieldDefinitionIdNameResolver localFieldDefinitionIdNameResolver;

  /**
   * Constructor
   * @param jpaRepository Needs to be qualified with @Qualifier annotation
   */
  public AbstractCrudWithValidationService(BaseJpaRepository<T, ID> jpaRepository) {
    Assert.notNull(jpaRepository, "The 'JpaRepository' argument must not be null.");
    this.jpaRepository = jpaRepository;
  }

  /**
   * It first validate then save the entity
   * @param entity
   * @return
   */
  @Override
  @Transactional
  public T create(T entity) {
    validateCreate(entity);
    jpaRepository.save(entity);
    postCreate(entity);
    return entity;
  }

  protected void postCreate(T entity) { }

  /**
   * Return the entity based on ID. If not found will throw ResourceNotFoundException.
   * @param id
   * @throws ResourceNotFoundException {@link ErrorCodes.COMMON_RESOURCE_NOT_FOUND}
   * @return entity
   */
  @Override
  public T get(ID id) {
    T entity = getEntity(id);
    appendMetadata(entity);
    return entity;
  }

  protected T getEntity(ID id) {
    T entity = jpaRepository.findOne(id);
    if (ObjectUtils.isEmpty(entity)) {
      throw new ResourceNotFoundException(ErrorCodes.COMMON_RESOURCE_NOT_FOUND, "Resource");
    }
    return entity;
  }

  protected void appendMetadata(T entity){
    if(!ObjectUtils.isEmpty(entity)) {
      SearchResponseWithMetaData searchResponseWithMetaData = localFieldDefinitionIdNameResolver
          .getSearchResultWithIdNameStoreMap(toSearchResponseDetailsDTO(entity), entity.getClass());
      entity.setMetaData(searchResponseWithMetaData.getMetaData());
    }
  }

  SearchResponseDetailsDTO toSearchResponseDetailsDTO(T entity){
      try {
        return new SearchResponseDetailsDTO(Arrays.asList(toMap(entity)), 1);
      } catch (Exception e) {
        log.error("Error while executing search against postgres search : {} ", e);
        return new SearchResponseDetailsDTO(new ArrayList<>(), 0);
      }
  }

  protected Map toMap(T entity) {
    return new ObjectMapper().convertValue(entity, Map.class);
  }

  /**
   * Will get the entity based on id. If not found return null.
   * @param id
   * @return entity or null
   */
  @Override
  public T getOrNull(ID id) {
    return jpaRepository.findOne(id);
  }

  /**
   * Return the entities based on pageable
   * @param pageable
   * @return
   */
  @Override
  public Page<T> getList(Pageable pageable) {
    return jpaRepository.findAll(pageable);
  }

  /**
   * If first check if entity exists or not, If it does not exists it raise exception. If it exists it update the entity
   * @param entity
   * @return
   */
  @Override
  public T update(T entity) {
    log.debug("Entity : {}", entity.toString());
    checkIfEntityExists(entity);
    entity = beforeUpdate(entity);
    log.debug("Entity : {}", entity.toString());
    validateUpdate(entity);
    T updatedEntity = jpaRepository.save(entity);
    jpaRepository.flush();
    appendMetadata(updatedEntity);
    return updatedEntity;
  }

  /**
   *  It check if entity exists or not, raise exception if entity does not exist else return the entity
   *
   * @param entity
   * @return
   */
  protected T checkIfEntityExists(T entity){
    T persistedEntity = getEntity((ID) entity.getId());
    if(ObjectUtils.isEmpty(persistedEntity)){
      throw new EntityException(CoreEntityErrorCodes.COMMON_RESOURCE_NOT_FOUND, entity.getClass().getSimpleName());
    }
    return persistedEntity;
  }

  /**
   * Delete the entity after calling validate method
   * @param entity
   * @return
   */
  @Override
  public T delete(T entity) {
    validateDelete(entity);
    entity = checkIfEntityExists(entity);
    jpaRepository.delete(entity);
    return entity;
  }

  public BaseJpaRepository<T, ID> getJpaRepository(){
    return this.jpaRepository;
  }
}
