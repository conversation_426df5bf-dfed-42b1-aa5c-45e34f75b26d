package com.sell.search.entity.core.service;

import com.sell.search.entity.core.dto.SearchResponseDetailsDTO;
import com.sell.search.entity.core.dto.SearchResponseWithMetaData;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import com.sell.search.entity.core.service.IdNameSearchService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.task.DelegatingSecurityContextAsyncTaskExecutor;
import org.springframework.util.ObjectUtils;

/**
 * Created by hemants on 16/07/19.
 */
@Slf4j

public class AbstractIdNameResolver {

  private final IdNameSearchService idNameSearchService;

  protected final List<String> pickListTypeSet = Arrays.asList(
      FieldType.PICK_LIST.name(),
      FieldType.CHECKBOX.name(),
      FieldType.RADIO_BUTTON.name(),
      FieldType.ENTITY_LOOKUP.name());

  protected final List<String> lookupTypeSet = Arrays.asList(
      FieldType.PIPELINE.name());

  public AbstractIdNameResolver(IdNameSearchService idNameSearchService) {
    this.idNameSearchService = idNameSearchService;
  }


  protected SearchResponseWithMetaData executeSearchResultAndAppendIdNameStoreMap(CompletableFuture<SearchResponseDetailsDTO> searchResponseFuture,
      CompletableFuture<Map<String, Map<Field, List>>> fieldDefinitionFuture) {

    CompletableFuture<Void> allFutures = CompletableFuture.allOf(fieldDefinitionFuture, searchResponseFuture);
    SearchResponseWithMetaData searchResponseWithMetaData = new SearchResponseWithMetaData();

    return allFutures.thenApplyAsync(v -> {

      //fetch fields with required data structure
      Map<String, Map<Field, List>> forTypeWiseFieldAndItsValues = fieldDefinitionFuture.join();
      log.debug("got the forTypeWiseFieldAndItsValues result : {} ", forTypeWiseFieldAndItsValues.keySet().size());

      //execute search
      SearchResponseDetailsDTO searchResponseDetails = searchResponseFuture.join();
      List<Map<String, Object>> result = searchResponseDetails.getContent();
      searchResponseWithMetaData.setTotal(searchResponseDetails.getTotal());
      log.debug("got the search response : {} ", result.size());
      if (result.isEmpty()) {
        return searchResponseWithMetaData;
      }
      Map<String, List<String>> standardFieldAndItsValue = new HashMap<>();
      Map<String, List<Object>> customFieldAndItsValue = new HashMap<>();

      //Filter fields into standard and custom fields map
      filterIntoStandardAndCustomMap(forTypeWiseFieldAndItsValues, standardFieldAndItsValue, customFieldAndItsValue);
      log.debug("user is having {} standard fields and {} custom fields ", standardFieldAndItsValue.keySet().size(),
          customFieldAndItsValue.keySet().size());

      populateStandardFieldIdAndCustomFieldId(forTypeWiseFieldAndItsValues, result, standardFieldAndItsValue, customFieldAndItsValue);

      Map<String, Set> idNameSearchRequest = toIdNameSearchRequest(forTypeWiseFieldAndItsValues);

      log.debug("making id name search with request {}", idNameSearchRequest);

      Map<String, Map<String, Object>> idNameResponse = idNameSearchService.search(idNameSearchRequest);

      log.debug("id name search response {}", idNameResponse);

      Map<String, Map<String, String>> idNameStore = toIdNameStore(forTypeWiseFieldAndItsValues, idNameResponse);

      Map<String, Object> idNameStoreMetaData = new HashMap<>();

      idNameStoreMetaData.put("idNameStore", idNameStore);
      searchResponseWithMetaData.setMetaData(idNameStoreMetaData);
      searchResponseWithMetaData.setSearchResponse(result);

      return searchResponseWithMetaData;

    }, getDelegatingSecurityContextAsyncTaskExecutor()).join();

  }



  /**
   * Filter fields based on if it standard field or not. While filtering keep the list of Ids, which needs to resolve, same so that it
   * can be populated by further pipeline
   *
   * @param forTypeWiseFieldAndItsValues
   * @param standardFieldAndItsValue
   * @param customFieldAndItsValue
   */
  private void filterIntoStandardAndCustomMap(Map<String, Map<Field, List>> forTypeWiseFieldAndItsValues,
      Map<String, List<String>> standardFieldAndItsValue, Map<String, List<Object>> customFieldAndItsValue) {
    forTypeWiseFieldAndItsValues.keySet().stream().forEach(forType -> {

      Map<Field, List> fieldAndItsValueMap = forTypeWiseFieldAndItsValues.get(forType);
      fieldAndItsValueMap.keySet().forEach(f -> {
        if (f.getStandard()) {
          standardFieldAndItsValue.put(f.getName(), fieldAndItsValueMap.get(f));
        } else {
          customFieldAndItsValue.put(f.getName(), fieldAndItsValueMap.get(f));
        }
      });
    });
  }

  private void populateStandardFieldIdAndCustomFieldId(
          Map<String, Map<Field, List>> forTypeWiseFieldAndItsValues,
          List<Map<String, Object>> esResult,
          Map<String, List<String>> standardFieldAndItsValue,
          Map<String, List<Object>> customFieldAndItsValue) {

    esResult.stream()
            .forEach(
                    m -> {
                      standardFieldAndItsValue
                              .keySet()
                              .forEach(
                                      k -> {
                                        forTypeWiseFieldAndItsValues
                                                .keySet()
                                                .forEach(
                                                        forType -> {
                                                          Map<Field, List> fieldAndItsIdMap =
                                                                  forTypeWiseFieldAndItsValues.get(forType);
                                                          fieldAndItsIdMap
                                                                  .keySet()
                                                                  .forEach(
                                                                          f -> {
                                                                            if (!forType.equals("PICK_LIST")
                                                                                    && f.getName().equals(k)
                                                                                    && null != m.get(k)) {
                                                                              if(m.get(k) instanceof List){
                                                                                ((List)m.get(k)).stream()
                                                                                    .forEach(o -> fieldAndItsIdMap.get(f).add(o.toString()));
                                                                              }else {
                                                                                fieldAndItsIdMap.get(f).add(m.get(k).toString());
                                                                              }
                                                                            }
                                                                          });
                                                        });
                                        Object toAdd = m.get(k);
                                        Optional<String> toAddAsString = asString(toAdd);
                                        toAddAsString.ifPresent(s -> standardFieldAndItsValue.get(k).add(s));
                                      });

                      if (!ObjectUtils.isEmpty(m.get("customFieldValues"))) {
                        customFieldAndItsValue
                                .keySet()
                                .forEach(
                                        k -> {
                                          Object toAdd = ((Map) m.get("customFieldValues")).get(k);
                                          Optional<String> toAddAsString = asString(toAdd);
                                          if(toAdd instanceof List){
                                            ((List)toAdd)
                                                .stream()
                                                .forEach(o -> {
                                                  customFieldAndItsValue.get(k).add(o.toString());
                                                });
                                          }
                                          toAddAsString.ifPresent(s -> customFieldAndItsValue.get(k).add(s));
                                        });
                      }
                    });
  }

  private Optional<String> asString(Object toConvert) {
    if (!ObjectUtils.isEmpty(toConvert)) {
      boolean isDigit = toConvert.toString().chars().allMatch(Character::isDigit);
      if (isDigit) {
        return Optional.of(toConvert.toString());
      } else {
        log.debug("ignoring field \'{}\' as it is not a digit", toConvert);
      }
    }
    return Optional.empty();
  }

  private Map<String, Set> toIdNameSearchRequest(Map<String, Map<Field, List>> forTypeWiseFieldAndItsValues) {
    Map<String, Set> forTypeAndIdsResult = new HashMap<>();
    forTypeWiseFieldAndItsValues.keySet().forEach(k -> {
      if (!forTypeAndIdsResult.containsKey(k)) {
        forTypeAndIdsResult.put(k, new HashSet<String>());
      }
      forTypeWiseFieldAndItsValues.get(k).keySet().forEach(f ->
          forTypeAndIdsResult.get(k).addAll(forTypeWiseFieldAndItsValues.get(k).get(f))
      );

    });
    return forTypeAndIdsResult.entrySet().stream()
        .filter(stringSetEntry -> !stringSetEntry.getValue().isEmpty())
        .collect(Collectors.toMap(stringSetEntry -> stringSetEntry.getKey(),stringSetEntry -> stringSetEntry.getValue()));
  }


  /**
   * Convert it into id name map which can be used to resolve the ids.
   *
   * @param forTypeWiseFieldAndItsValues
   * @param idNameResponse
   * @return
   */
  private Map<String, Map<String, String>> toIdNameStore(Map<String, Map<Field, List>> forTypeWiseFieldAndItsValues,
      Map<String, Map<String, Object>> idNameResponse) {
    Map<String, Map<String, String>> idNameStore = new HashMap<>();

    forTypeWiseFieldAndItsValues.keySet().forEach(forType -> {

      Map<Field, List> fieldAndItsIdMap = forTypeWiseFieldAndItsValues.get(forType);

      fieldAndItsIdMap.keySet().forEach(f -> {
        String fieldName = f.getName();
        List<Object> ids = fieldAndItsIdMap.get(f);
        if (!idNameStore.containsKey(fieldName)) {
          idNameStore.put(fieldName, new HashMap<>());
        }
        ids.forEach(id -> {
              if (idNameResponse != null && idNameResponse.get(forType) != null && idNameResponse.get(forType).get(id) != null) {
                idNameStore.get(fieldName).put(id.toString(), idNameResponse.get(forType).get(id).toString());
              }
            }
        );
      });
    });
    return idNameStore;
  }


  /**
   * Make following structure out of list of fields.
   * <p>
   * (LEAD, forType)
   * |---------------------------------
   * |          |           |         |
   * Field1    Field2      Field3    Field4
   * |         |           |         |
   * |         |           |         |
   * (id1, id2) (id3, id4) (id5, id6) (id7, id8)       //List of Ids for which names to be looked up
   *
   * @param validFields
   * @return
   */
  protected Map<String, Map<Field, List>> makeRequiredDataStructure(List<Field> validFields) {
    Map<String, Map<Field, List>> forTypeWiseFieldAndItsValues = new HashMap<>();
    validFields.forEach(f -> {
      String fieldTypeToUse = "";
      if (pickListTypeSet.contains(f.getType().name())) {
        fieldTypeToUse = FieldType.PICK_LIST.name();
      } else if (lookupTypeSet.contains(f.getType().name())) {
        fieldTypeToUse = FieldType.PIPELINE.name();
      } else if (FieldType.MULTI_PICKLIST.name().equals(f.getType().name())) {
        fieldTypeToUse = FieldType.MULTI_PICKLIST.name();
      } else {
        fieldTypeToUse = f.getLookupForEntity();
      }
      if (!forTypeWiseFieldAndItsValues.containsKey(fieldTypeToUse)) {
        forTypeWiseFieldAndItsValues.put(fieldTypeToUse, new HashMap<>());
      }
      forTypeWiseFieldAndItsValues.get(fieldTypeToUse).put(f, new ArrayList<String>());
    });
    return forTypeWiseFieldAndItsValues;
  }

  protected DelegatingSecurityContextAsyncTaskExecutor getDelegatingSecurityContextAsyncTaskExecutor() {
    return new DelegatingSecurityContextAsyncTaskExecutor(new SimpleAsyncTaskExecutor(), SecurityContextHolder.getContext());
  }


}
