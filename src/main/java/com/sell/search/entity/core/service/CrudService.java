package com.sell.search.entity.core.service;

import com.sell.search.core.domain.BaseEntity;
import java.io.Serializable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Created by hemants on 06/04/19.
 * Interface for Crud service
 */
public interface CrudService <T extends BaseEntity, ID extends Serializable> {

  T create(T entity);

  T get(ID id);

  T getOrNull(ID id);

  Page<T> getList(Pageable pageable);

  T update(T entity);

  T delete(T entity);

}
