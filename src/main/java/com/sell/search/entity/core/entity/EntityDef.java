package com.sell.search.entity.core.entity;


import com.sell.search.core.annotation.AccessPermission;
import com.sell.search.core.domain.TenantAwareBaseEntity;
import com.sell.search.core.domain.EntityType;
import com.sell.search.entity.core.entity.Field;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by hemants on 02/04/19.
 */
@Getter
@Setter
@AccessPermission("config")
public class EntityDef extends TenantAwareBaseEntity {

  @Column(nullable = false)
  @Enumerated(value = EnumType.STRING)
  private EntityType name;

  private String displayName;
  private String displayNamePlural;

  private List<Field> fields;
}
