package com.sell.search.entity.core.annotation;

import com.sell.search.entity.core.model.StringTypeField;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created by shashanks3 on 19/6/19.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
public @interface StringField {

  public StringTypeField value();
}