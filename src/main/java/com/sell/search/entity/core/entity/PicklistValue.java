package com.sell.search.entity.core.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sell.search.core.annotation.AccessPermission;
import com.sell.search.core.domain.TenantAwareBaseEntity;
import com.sell.search.entity.core.entity.Picklist;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Where;

/**
 * Created by shashanks3 on 26/4/19.
 */
@Setter
@Getter
@Where(clause = "deleted=false")
@NoArgsConstructor
@AccessPermission("config")
public class PicklistValue extends TenantAwareBaseEntity {

  private String name;
  private String displayName;
  private boolean disabled;

  private com.sell.search.entity.core.entity.Picklist picklist;

  public PicklistValue(String displayName, Picklist picklist) {
    super();
    this.displayName = displayName;
    this.picklist = picklist;
  }
}
