package com.sell.search.entity.core.converter.field.rawdatatype;

import com.sell.search.core.domain.ErrorResource;
import com.sell.search.core.domain.FieldErrorResource;
import com.sell.search.entity.core.exception.CoreEntityErrorCodes;
import com.sell.search.entity.core.exception.EntityException;

import java.util.Arrays;

/**
 * Created by hemants on 29/04/19.
 */
class RawDatatypeExceptionHelper {

  /**
   * A common method to throw an exception
   *
   * @param key
   * @param object
   * @param converterName
   * @param messageKey
   */
  static void throwException(String key, Object object, String converterName, String messageKey) {
    ErrorResource eR = new ErrorResource(CoreEntityErrorCodes.COMMON_VALIDATION_ERROR.getCode(),
        CoreEntityErrorCodes.COMMON_VALIDATION_ERROR.getMessage());
    eR.fieldErrors(Arrays.asList(new FieldErrorResource(key, messageKey, object, converterName)));
    throw new EntityException(eR);
  }
}
