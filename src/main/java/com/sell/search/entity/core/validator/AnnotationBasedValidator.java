package com.sell.search.entity.core.validator;

import com.sell.search.core.domain.BaseEntity;

import com.sell.search.entity.core.validator.BaseValidator;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Set;

/**
 * Created by hemants on 04/04/19.
 * Basic Annotation based validator
 */
public class AnnotationBasedValidator<T extends BaseEntity> implements BaseValidator<T> {

  @Override
  public Set<ConstraintViolation<T>> validate(T object) {
    Validator validator = getValidator();
    return validator.validate(object);
  }

  /**
   * @return java's validator object
   */
  private static Validator getValidator() {
    return Validation.buildDefaultValidatorFactory().getValidator();
  }
}
