package com.sell.search.entity.core.converter.field.strategy;

import com.sell.search.entity.core.converter.field.strategy.DisplayNameNamingStrategy;
import com.sell.search.entity.core.util.EntityStringUtils;
import org.springframework.util.ObjectUtils;

/**
 * Created by hemants on 17/04/19.
 * DisplayName Strategy to convert field name like firstName into display name like First Name
 */
public class SimpleDisplayNameNamingStrategy implements DisplayNameNamingStrategy {

  /**
   * First split based on came case then, capitalize the first character and finally join with space
   *
   * @param name
   * @return
   */
  @Override
  public String getDisplayName(String name) {
    if (ObjectUtils.isEmpty(name)) {
      return name;
    }
    return EntityStringUtils.splitByCamelCase
        .andThen(EntityStringUtils.capitalizeFirstCharacterOfFirstString)
        .andThen(EntityStringUtils.joinBySpace).apply(name);
  }
}
