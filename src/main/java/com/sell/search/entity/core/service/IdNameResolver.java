package com.sell.search.entity.core.service;

import com.sell.search.core.domain.EntityType;
import com.sell.search.core.exception.BaseException;
import com.sell.search.core.utils.EntityUtil;
import com.sell.search.entity.core.dto.SearchResponseDetailsDTO;
import com.sell.search.entity.core.dto.SearchResponseWithMetaData;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import com.sell.search.entity.core.service.AbstractIdNameResolver;
import com.sell.search.entity.core.service.IdNameSearchService;
import com.sell.search.entity.core.service.client.IEntityClient;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/** Created by hemants on 26/06/19. */
@Slf4j
public class IdNameResolver extends AbstractIdNameResolver {

  private final IEntityClient entityClient;

  public IdNameResolver(IEntityClient entityClient, IdNameSearchService idNameSearchService) {
    super(idNameSearchService);
    this.entityClient = entityClient;
  }

  /**
   * Using esSearchFuture and fieldDefinitionFuture, run both request at same time for faster
   * response. Once both request are execute merge both response to get idNameSearchRequest and
   * search for idName resolution
   *
   * @param searchResponseFuture
   * @param resourceName
   * @return
   */
  public SearchResponseWithMetaData getSearchResultWithIdNameStoreMap(
      CompletableFuture<SearchResponseDetailsDTO> searchResponseFuture, String resourceName) {

    EntityType entityType = EntityUtil.getEntityType(resourceName);
    CompletableFuture<Map<String, Map<Field, List>>> fieldDefinitionFuture =
        getCustomFieldFuture(entityType);
    return executeSearchResultAndAppendIdNameStoreMap(searchResponseFuture, fieldDefinitionFuture);
  }

  /**
   * Fetch entities and filter those field type which are relevant to id/name resolution. Prepare a
   * following structure which will be helpful to be used in further pipeline
   *
   * <p>entity_type -> map of fields -> each field has list of ids to be resolved
   *
   * <p>(LEAD, forType) | | Field1 Field2 Field3 Field4 | | | | | | | | (id1, id2) (id3, id4) (id5,
   * id6) (id7, id8) //List of Ids for which names to be looked up
   *
   * <p>This function just create new Empty arrayList for ids so that once search is executed ids
   * can be added to the list.
   *
   * @param entityType
   * @return
   */
  public CompletableFuture<Map<String, Map<Field, List>>> getCustomFieldFuture(
      EntityType entityType) {
    return CompletableFuture.supplyAsync(
        () -> makeRequiredDataStructure(getFieldsEligibleForIdNameResolution(entityType)),
        getDelegatingSecurityContextAsyncTaskExecutor());
  }

  /**
   * Only those type are valid which defined in pickListTypeSet
   *
   * @return fields of entity
   */
  private List<Field> getFieldsEligibleForIdNameResolution(EntityType entityType) {
    // TODO: Fetch only those fields which required
    try {
      return entityClient.getFields(entityType).stream()
          .filter(
              field ->
                  pickListTypeSet.contains(field.getType().name()) || lookupTypeSet.contains(field.getType().name())
                      || FieldType.MULTI_PICKLIST.name().equals(field.getType().name())
                      || FieldType.LOOK_UP.name().equals(field.getType().name())
                          && !field.isSkipIdNameResolution())
          .collect(Collectors.toList());
    } catch (BaseException ex) {
      return new ArrayList<>();
    }
  }
}
