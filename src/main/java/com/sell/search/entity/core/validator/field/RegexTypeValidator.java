package com.sell.search.entity.core.validator.field;

import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import com.sell.search.entity.core.validator.field.DataTypeValidator;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.springframework.util.ObjectUtils;

/**
 * Created by shashanks3 on 29/5/19.
 */
public class RegexTypeValidator implements DataTypeValidator {

  private final List<FieldType> regexApplicableList = Arrays.asList(
      FieldType.TEXT_FIELD,
      FieldType.SINGLE_LINE_TEXT,
      FieldType.PARAGRAPH_TEXT,
      FieldType.RICH_TEXT);


  @Override
  public boolean validate(Map<String, Object> customFields, Map<String, Field> allowedCustomFields) {
    allowedCustomFields.entrySet().stream()
        .filter(entry -> regexApplicableList.contains(entry.getValue().getType()))
        .forEach(entry -> {
          if (customFields.containsKey(entry.getKey()) && !ObjectUtils.isEmpty(allowedCustomFields.get(entry.getKey()).getRegex())) {
            Field field = allowedCustomFields.get(entry.getKey());
            validate(customFields.get(entry.getKey()), field, entry.getKey());
          }
        });
    return true;
  }

  /**
   * Validate based on if it multi value or not
   */
  private void validate(Object value, Field field, String key) {
    if (!ObjectUtils.isEmpty(field) && field.getMultiValue()) {
      List<String> listOfString = (List<String>) value;
      listOfString.forEach(s -> validateSingle(s, field, key));
    } else {
      validateSingle((String) value, field, key);
    }
  }

  /**
   * Validate single value
   */
  private void validateSingle(String value, Field field, String key) {
    Pattern pattern = Pattern.compile(field.getRegex(), Pattern.CASE_INSENSITIVE);
    Matcher matcher = pattern.matcher(value);
    if (!matcher.find()) {
      throwException(key, field.getRegex());
    }
  }

  /**
   * A common method to throw an exception
   */
  private void throwException(String k, String regex) {
    ValidatorExceptionHelper.throwException(k, regex, "field.validation.constraints.regex.message");
  }

}
