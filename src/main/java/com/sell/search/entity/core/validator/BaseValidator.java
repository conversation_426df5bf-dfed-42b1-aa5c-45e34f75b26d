package com.sell.search.entity.core.validator;

import com.sell.search.core.domain.BaseEntity;

import javax.validation.ConstraintViolation;
import java.util.Set;

/**
 * Created by hemants on 04/04/19.
 */
public interface BaseValidator <T extends BaseEntity> {
  /**
   *
   * @param object The object which needs to be validated. Validation Annotation on the object will be used to validate the object
   */
  public Set<ConstraintViolation<T>> validate(T object);

}
