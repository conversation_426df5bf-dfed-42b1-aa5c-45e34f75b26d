package com.sell.search.entity.core.converter;

import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.exception.FieldValidationException;
import com.sell.search.entity.core.exception.ValidationExceptionType;
import java.time.Instant;
import java.time.format.DateTimeParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * Created by hemants on 28/02/19.
 * This converter class will convert the custom field into its data type defined by a tenant
 */
@Slf4j
@Deprecated
public class CustomFieldTypeConverter {

  /**
   * Return Map with converted type.
   * This method is in progress
   * @param allowedFields
   * @param requestAttributes
   * @return
   * @throws FieldValidationException
   */
  public static HashMap<String, Object> getTypeConvertedAttributeMap(List<Field> allowedFields,
      Map<String, Object> requestAttributes) throws FieldValidationException {

    HashMap<String, Object> customFieldsMap = new HashMap<>();

    // create map for better performance while conditional traversing
    Map<String, Field> customFieldMap = allowedFields
        .stream()
        .collect(Collectors.toMap(Field::getName, cf -> cf));

    Iterator<String> iterable = requestAttributes.keySet().iterator();
    while (iterable.hasNext()) {
      String k = iterable.next();
      if (!customFieldMap.containsKey(k)) {
        throw new FieldValidationException(ValidationExceptionType.MANDATORY_FIELD_MISSING);
      }
      customFieldsMap.put(k, getCustomField(customFieldMap.get(k), getSanitizedFieldValue(requestAttributes.get(k))));
    }
    return customFieldsMap;
  }

  private static Object getCustomField(Field customField, Object input) throws FieldValidationException {
    // treat all null or empty values as null
    // this shall not parse isRequired field, since those are handle before this method call
    if (input == null || input instanceof String && StringUtils.isEmpty(input)) {
      return null;
    }

    switch (customField.getType()) {
      case TEXT_FIELD:
        if (!(input instanceof String)) {
          throw new FieldValidationException(ValidationExceptionType.INVALID_ATTRIBUTE);
        }
        return input;

      case NUMBER:
        // if number is in string format
        Number number;
        if (input instanceof String) {
          try {
            // comparing simple decimal places double
            if (((String) input).contains(".")) {
              number = Double.valueOf((String) input);
            } else {
              number = Integer.valueOf((String) input);
            }
            return number;
          } catch (NumberFormatException nfe) {
            log.error("Failed to convert {} into number", input, nfe);
            throw new FieldValidationException(ValidationExceptionType.INVALID_ATTRIBUTE);
          }
        } else if (input instanceof Number) {
          return input;
        }
        throw new FieldValidationException(ValidationExceptionType.INVALID_ATTRIBUTE);

      case DATE_PICKER:
        Date date;
        if (input instanceof String) {
          try {
            // strictly checks for UTC time / Zulu time in ISO.8601 format
            date = Date.from(Instant.parse((String) input));
            return date;
          } catch (DateTimeParseException dtpe) {
            log.error("Failed to convert {} into date", input, dtpe);
            throw new FieldValidationException(ValidationExceptionType.INVALID_ATTRIBUTE);
          }
        }
        throw new FieldValidationException(ValidationExceptionType.INVALID_ATTRIBUTE);
    }
    throw new FieldValidationException(ValidationExceptionType.INVALID_ATTRIBUTE);
  }

  private static Object getSanitizedFieldValue(Object value) {
    return value instanceof String ? ((String) value).trim() : value;
  }

}
