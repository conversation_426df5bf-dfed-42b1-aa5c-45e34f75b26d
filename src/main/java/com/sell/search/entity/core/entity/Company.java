package com.sell.search.entity.core.entity;

import com.sell.search.core.annotation.FieldAttribute;
import com.sell.search.core.domain.EntityType;
import com.sell.search.entity.core.annotation.ExtendedFields;
import com.sell.search.entity.core.annotation.Internal;
import com.sell.search.entity.core.annotation.LookupField;
import com.sell.search.entity.core.annotation.PicklistField;
import com.sell.search.entity.core.model.CustomFieldAwareBaseEntity;
import com.sell.search.entity.core.model.Email;
import com.sell.search.entity.core.model.EmailJsonUserType;
import com.sell.search.entity.core.model.LogoUrlJsonUserType;
import com.sell.search.entity.core.model.PhoneJsonUserType;
import com.sell.search.entity.core.model.PhoneNumber;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import java.util.Date;

@MappedSuperclass
@Getter
@Setter
@TypeDefs({@TypeDef(name = "PhoneJsonUserType", typeClass = PhoneJsonUserType.class),
    @TypeDef(name = "EmailJsonUserType", typeClass = EmailJsonUserType.class),
    @TypeDef(name = "LogoUrlJsonUserType", typeClass = LogoUrlJsonUserType.class),})
@ExtendedFields(value = {"ownerId", "createdAt", "updatedAt", "createdBy", "updatedBy"})
public class Company extends CustomFieldAwareBaseEntity {

	private String name;

	@Type(type = "LogoUrlJsonUserType")
	@Column(name = "logo_urls", columnDefinition = "jsonb")
	private String[] logoUrls;

  private int numberOfEmployees;
	private Double annualRevenue;
	private String website;

  @Internal
  @PicklistField(name = "INDUSTRY")
  private String industry;
	private String businessType;

	// Communication
	@Type(type = "EmailJsonUserType")
	@Column(name = "emails", columnDefinition = "jsonb")
	private Email[] emails;

	@Type(type = "PhoneJsonUserType")
	@Column(name = "phone_numbers", columnDefinition = "jsonb")
	private PhoneNumber[] phoneNumbers;
    @FieldAttribute(displayName = "Do Not Disturb")
	private boolean dnd;
	private String timezone;

	// Location
	private String address;
	private String city;
	private String state;
	private String zipcode;

	@Internal
	@PicklistField(name = "COUNTRY")
	private String country;

	// Social
	private String facebook;
	private String twitter;
	private String linkedin;

	// Internals
	@Override
	@Internal
	@LookupField(EntityType.USER)
	public Long getOwnerId() {
		return super.getOwnerId();
	}

	@Override
	@Internal
	@LookupField(EntityType.USER)
	public Long getCreatedBy() {
		return super.getCreatedBy();
	}

	@Override
	@Internal
	@LookupField(EntityType.USER)
	public Long getUpdatedBy() {
		return super.getUpdatedBy();
	}

	@Override
	@Internal
	public Date getCreatedAt() {
		return super.getCreatedAt();
	}

	@Override
	@Internal
	public Date getUpdatedAt() {
		return super.getUpdatedAt();
	}

}
