package com.sell.search.entity.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created by hemants on 18/02/19.
 * Entity having this annotation will start generating events on 'create', 'delete' and 'update'. As service need
 * to extends {@link com.sell.entity.core.service.AbstractEventableCrudService}
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface Eventable {
}
