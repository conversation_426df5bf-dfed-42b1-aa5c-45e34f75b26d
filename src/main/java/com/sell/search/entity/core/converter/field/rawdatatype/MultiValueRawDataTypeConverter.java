package com.sell.search.entity.core.converter.field.rawdatatype;

import com.sell.search.entity.core.converter.field.rawdatatype.RawDataTypeConverter;
import com.sell.search.entity.core.entity.Field;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.util.ObjectUtils;

/**
 * Created by hemants on 19/04/19.
 * The array raw data type converter
 */
@RequiredArgsConstructor
public class MultiValueRawDataTypeConverter implements RawDataTypeConverter {

  private final RawDataTypeConverter converterFactory;


  /**
   * If it is multi value
   *
   * @param definedDataType
   * @return
   */
  @Override
  public boolean canConvert(Field definedDataType) {
    return !ObjectUtils.isEmpty(definedDataType.getMultiValue()) && definedDataType.getMultiValue();
  }

  /**
   * As definedDataType has multi value as true, we need to create copy of that having value as false.
   * Then call factory method to call individual converter to convert rawObject based on its type.
   *
   * @param rawObject
   * @param definedDataType
   * @return
   */
  @Override
  public Object convert(Object rawObject, Field definedDataType) {
    Field field = new Field();
    field.setType(definedDataType.getType());
    field.setMultiValue(false);
    field.setInternalType(definedDataType.getInternalType());
    List<Object> list = getListOfObject(definedDataType, rawObject);
    field.setPicklist(definedDataType.getPicklist());
    return list.stream().map(o -> converterFactory.convert(o, field)).collect(Collectors.toList());
  }

  /**
   * First try with comma separated string if not able to parse then try converting it into list<object>
   *
   * @param rawObject
   * @return
   */
  private List<Object> getListOfObject(Field field, Object rawObject) {
    //First try with comma separated string
    if (!(rawObject instanceof List)) {
      throwException(field.getName(), rawObject, "multiValue");
    }
    /*
    try {
      return Arrays.asList(((String) rawObject).split(","));
    } catch (ClassCastException e) {
      log.debug("Not able to convert object : {} to string ", rawObject);
    }
    log.debug("trying list of object directly");
    //Then try with list of object directly
    */
    return (List<Object>) rawObject;
  }

  private void throwException(String k, Object object, String converterName) {
    RawDatatypeExceptionHelper.throwException(k, object, converterName, "field.conversion.error.message");
  }
}
