package com.sell.search.entity.core.validator.field;

import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.CustomFieldAwareBaseEntity;

import com.sell.search.entity.core.validator.field.DataTypeValidator;
import com.sell.search.entity.core.validator.field.GreaterLessThanValidator;
import com.sell.search.entity.core.validator.field.LengthValidator;
import com.sell.search.entity.core.validator.field.PickListValidator;
import com.sell.search.entity.core.validator.field.RegexTypeValidator;
import com.sell.search.entity.core.validator.field.UniqueValidator;
import javax.persistence.EntityManager;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by hemants on 19/04/19.
 * The factory class having collection of all datatype validators
 */

public class DataTypeValidatorFactory<T extends CustomFieldAwareBaseEntity, ID extends Serializable> implements DataTypeValidator {

  List<DataTypeValidator> allDataTypeValidators = new ArrayList<>();


  /**
   * Constructor to initialize the validators
   *
   * @param entityClass
   * @param em
   */
  public DataTypeValidatorFactory(Class<T> entityClass, EntityManager em) {
    allDataTypeValidators.add(new LengthValidator());
//    allDataTypeValidators.add(new RequiredValidator());
    allDataTypeValidators.add(new UniqueValidator<>(entityClass, em));
    allDataTypeValidators.add(new GreaterLessThanValidator());
    allDataTypeValidators.add(new PickListValidator());
    allDataTypeValidators.add(new RegexTypeValidator());

  }

  /**
   * Additional method to add more validators
   *
   * @param validator
   */
  public void addDataTypeValidator(DataTypeValidator validator) {
    allDataTypeValidators.add(validator);
  }

  /**
   * Call each validator to validate the fields based on created fields
   *
   * @param customFields
   * @param allowedCustomFields
   */
  @Override
  public boolean validate(Map<String, Object> customFields, Map<String, Field> allowedCustomFields) {
    if (customFields == null) {
      throw new RuntimeException("Map cannot be null"); //TODO: send proper exception
    }
    allDataTypeValidators.forEach(validator -> validator.validate(customFields, allowedCustomFields));
    return true;
  }

}
