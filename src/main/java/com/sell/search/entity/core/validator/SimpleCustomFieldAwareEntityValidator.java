package com.sell.search.entity.core.validator;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.search.core.domain.ErrorResource;
import com.sell.search.core.domain.FieldErrorResource;
import com.sell.search.entity.core.converter.field.factory.RawDataTypeConverterFactory;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.exception.CoreEntityErrorCodes;
import com.sell.search.entity.core.exception.EntityException;
import com.sell.search.entity.core.model.CustomFieldAwareBaseEntity;
import com.sell.search.core.domain.EntityType;
import com.sell.search.entity.core.service.client.IEntityClient;
import com.sell.search.entity.core.validator.CustomFieldAwareValidator;
import com.sell.search.entity.core.validator.field.DataTypeValidatorFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import javax.validation.ConstraintViolation;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by hemants on 19/04/19.
 */
@Slf4j
public class SimpleCustomFieldAwareEntityValidator<T extends CustomFieldAwareBaseEntity, ID extends Serializable> implements
    CustomFieldAwareValidator<T> {

  private final IEntityClient entityClient;
  private final DataTypeValidatorFactory<T, ID> dataTypeValidatorFactory;
  RawDataTypeConverterFactory rawDataTypeConverterFactory;
  private final String CUSTOM_FIELD_KEY = "customFieldValues";

  public SimpleCustomFieldAwareEntityValidator(IEntityClient entityClient,
      DataTypeValidatorFactory<T, ID> dataTypeValidatorFactory) {
    this.entityClient = entityClient;
    this.dataTypeValidatorFactory = dataTypeValidatorFactory;
    this.rawDataTypeConverterFactory = new RawDataTypeConverterFactory();
  }

  public Set<ConstraintViolation<T>> validate(T entity) {
    List<Field> configuredEntityFieldList = entityClient.getFields(getEntityType(entity));

    Map<String, Object> entityFieldValueMap = getAllEntityFieldAndValues(entity);

    if (entityFieldValueMap == null) {
      entityFieldValueMap = new HashMap<>();
    }
    // create map for better performance while conditional traversing
    Map<String, Field> allowedCustomFields = configuredEntityFieldList
        .stream()
        .collect(Collectors.toMap(Field::getName, cf -> cf));
    validateIfFieldsDefined(allowedCustomFields, entity.getCustomFieldValues());
    rawDataTypeConverterFactory.convert(entityFieldValueMap, allowedCustomFields);
    dataTypeValidatorFactory.validate(entityFieldValueMap, allowedCustomFields);

    return new HashSet<>();
  }

  private void validateIfFieldsDefined(Map<String, Field> allowedCustomFields , Map<String, Object> customFieldValues){
    if(ObjectUtils.isEmpty(customFieldValues)){
      return;
    }
    customFieldValues.keySet().forEach(k ->{
      if(!allowedCustomFields.containsKey(k)){
        throwException(k);
      }
    });
  }


  /**
   * A method to throw an exception
   * @param fieldName
   * @return
   */
  private EntityException throwException(String fieldName) {
    ErrorResource eR = new ErrorResource(CoreEntityErrorCodes.COMMON_VALIDATION_ERROR.getCode(),
        CoreEntityErrorCodes.COMMON_VALIDATION_ERROR.getMessage());
    eR.fieldErrors(Arrays.asList(new FieldErrorResource(fieldName, "field.definition.error")));
    throw new EntityException(eR);
  }


  public Map<String, Object> getAllEntityFieldAndValues(T entity){
    Map<String, Object> standardAndCustomFieldMap = new ObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_NULL).convertValue(entity, Map.class);
    standardAndCustomFieldMap.remove(CUSTOM_FIELD_KEY);
    Map<String, Object> customFieldMap = entity.getCustomFieldValues();
    if(!ObjectUtils.isEmpty(customFieldMap)){
      Iterator<String> keyIterator =  entity.getCustomFieldValues().keySet().iterator();
      while(keyIterator.hasNext()){
        String key = keyIterator.next();
        standardAndCustomFieldMap.put(key, customFieldMap.get(key));
      }
    }
    /*
    Map<String, Object> customFieldMap = (Map<String, Object>) standardAndCustomFieldMap.get(CUSTOM_FIELD_KEY);
    if(customFieldMap == null){
      customFieldMap = new HashMap<>();
    }


    Iterator<String> keyIterator = customFieldMap.keySet().iterator();
    while(keyIterator.hasNext()){
      String key = keyIterator.next();
      standardAndCustomFieldMap.put(key, customFieldMap.get(key));
    }
    */
    return standardAndCustomFieldMap;
  }

  /**
   * Return entityType based on entity
   *
   * @param entity
   * @return
   */
  private EntityType getEntityType(T entity) {
    try {
      return EntityType.valueOf(entity.getClass().getSimpleName().toUpperCase());
    } catch (Exception e) {
      throw new RuntimeException("Invalid entity"); //This is not consumer facing exception so need not to wrap in error resource.
    }
  }

}
