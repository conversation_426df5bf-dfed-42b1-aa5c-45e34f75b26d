package com.sell.search.entity.core.converter.field.datatype;

import static com.sell.search.entity.core.converter.field.datatype.ConverterUtil.getAnnotationFromFieldGetter;

import com.sell.search.entity.core.annotation.PipelineField;
import com.sell.search.entity.core.converter.field.datatype.DataTypeConverter;
import com.sell.search.entity.core.converter.field.datatype.FieldContext;
import com.sell.search.entity.core.entity.Field;
import java.lang.annotation.Annotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;


@Slf4j
public class PipelineConverter implements DataTypeConverter {

  @Override
  public boolean canConvert(com.sell.search.entity.core.converter.field.datatype.FieldContext fieldContext) {
    return fieldContext.getField().getAnnotationsByType(PipelineField.class).length > 0
        || getAnnotationFromFieldGetter(fieldContext.getField(), fieldContext.getFieldClass(), PipelineField.class).length > 0;
  }

  @Override
  public Field convert(FieldContext fieldContext) {
    Annotation[] annotations = fieldContext.getField().getAnnotationsByType(PipelineField.class);
    if (ObjectUtils.isEmpty(annotations)) {
      annotations = getAnnotationFromFieldGetter(fieldContext.getField(), fieldContext.getFieldClass(), PipelineField.class);
    }
    Field fieldDataType = new Field().type(((PipelineField) annotations[0]).fieldType());
    fieldDataType.setLookupForEntity(((PipelineField) annotations[0]).lookupFor().name());
    fieldDataType.setInternalType(((PipelineField) annotations[0]).internalType());
    return fieldDataType;
  }

}
