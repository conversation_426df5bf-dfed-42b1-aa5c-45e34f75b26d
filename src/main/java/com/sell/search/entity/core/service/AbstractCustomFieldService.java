package com.sell.search.entity.core.service;


import com.sell.search.core.domain.ErrorCodes;
import com.sell.search.core.exception.ResourceNotFoundException;
import com.sell.search.core.repository.BaseJpaRepository;
import com.sell.search.entity.core.dto.SearchResponseDetailsDTO;
import com.sell.search.entity.core.dto.SearchResponseWithMetaData;
import com.sell.search.entity.core.model.CustomFieldAwareBaseEntity;
import com.sell.search.entity.core.service.AbstractCrudWithValidationService;
import com.sell.search.entity.core.service.IdNameResolver;
import com.sell.search.entity.core.service.IdNameSearchService;
import com.sell.search.entity.core.service.client.IEntityClient;
import com.sell.search.entity.core.validator.CustomFieldAwareValidator;
import com.sell.search.entity.core.validator.SimpleCustomFieldAwareEntityValidator;
import com.sell.search.entity.core.validator.field.DataTypeValidatorFactory;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;
import javax.annotation.PostConstruct;
import javax.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

/**
 * Created by hemants on 06/04/19.
 * The abstract implementation of custom field functionality
 */
@Slf4j
public abstract class AbstractCustomFieldService<T extends CustomFieldAwareBaseEntity, ID extends Serializable> extends
    AbstractCrudWithValidationService<T, ID> {

  private CustomFieldAwareValidator<T> customFieldAwareValidator;
  private final Class<T> entityClass;
  private final IEntityClient entityClient;

  private com.sell.search.entity.core.service.IdNameResolver idNameResolver;

  @Autowired
  private EntityManager em;

  @Autowired
  IdNameSearchService idNameSearchService;

  /**
   * Constructor
   * @param jpaRepository Needs to be qualified with @Qualifier annotation
   */
  public AbstractCustomFieldService(Class<T> entityClass, BaseJpaRepository<T, ID> jpaRepository, IEntityClient entityClient) {
    super(jpaRepository);
    this.entityClient = entityClient;
    this.entityClass = entityClass;
  }


  @PostConstruct
  private void setCustomFieldAwareValidator(){
    Assert.notNull(em, "The 'EntityManager' must not be null.");
    this.customFieldAwareValidator = new SimpleCustomFieldAwareEntityValidator<T, ID>(
        this.entityClient,
        new DataTypeValidatorFactory<T, ID>(this.entityClass, em));
    this.idNameResolver = new IdNameResolver(entityClient, idNameSearchService);
  }

  /**
   * Return the entity based on ID. If not found will throw ResourceNotFoundException.
   *
   * @param id
   * @return entity
   * @throws ResourceNotFoundException {@link ErrorCodes.COMMON_RESOURCE_NOT_FOUND}
   */
  @Override
  public T get(ID id) {
    T entity = super.getEntity(id);
    appendMetadata(entity);
    return entity;
  }

  @Override
  protected void appendMetadata(T entity){
    if(!ObjectUtils.isEmpty(entity)) {
      SearchResponseWithMetaData searchResponseWithMetaData = idNameResolver
          .getSearchResultWithIdNameStoreMap(toCompletableFuture(entity), entity.getClass().getSimpleName());
      entity.setMetaData(searchResponseWithMetaData.getMetaData());
    }
  }

  CompletableFuture<SearchResponseDetailsDTO> toCompletableFuture(T entity){
    return CompletableFuture.supplyAsync(() -> {
      try {
        return new SearchResponseDetailsDTO(Arrays.asList(toMap(entity)), 1);
      } catch (Exception e) {
        log.error("Error while executing search against postgres search : {} ", e);
        return new SearchResponseDetailsDTO(new ArrayList<>(), 0);
      }
    });
  }


  /**
   * Validate entity before delete it
   * @param entity
   *
   */
  @Override
  public void validateDelete(T entity){
    //do nothing for custom fields
    validateEntityDelete(entity);
  }

  /**
   * Validate entity before create
   * @param entity
   */
  @Override
  public void validateCreate(T entity){
    validate(entity);
    validateEntityCreate(entity);
  }

  /**
   * Validate entity before update
   * @param entity
   */
  @Override
  public void validateUpdate(T entity){
    validate(entity);
    validateEntityUpdate(entity);
  }


  /**
   * call validate method of customFieldAwareValidator
   * @param entity
   */
  public void validate(T entity) {
    customFieldAwareValidator.validate(entity);
  }

  /**
   * Before update, allow entity to be change/update by child class
   * @param entity
   * @return
   */
  @Override
  public T beforeUpdate(T entity){
    entity = beforeEntityUpdate(entity);
    return entity;
  }

  /**
   * Abstract method needs to implemented by child class
   * @param entity
   */
  public abstract void validateEntityUpdate(T entity);

  /**
   * Abstract method needs to implemented by child class
   * @param entity
   */
  public abstract void validateEntityCreate(T entity);

  /**
   * Abstract method needs to implemented by child class
   * @param entity
   */
  public abstract void validateEntityDelete(T entity);
  /**
   * Abstract method needs to implemented by child class
   * @param entity
   */
  public abstract T beforeEntityUpdate(T entity);


}
