package com.sell.search.entity.core.service;

import com.sell.search.core.domain.BaseEntity;
import com.sell.search.entity.core.service.CrudService;
import java.io.Serializable;

/**
 * Created by hemants on 06/04/19.
 * Interface of crud service with validation
 */
public interface CrudWithValidationService <T extends BaseEntity, ID extends Serializable>  extends CrudService<T, ID> {
  T beforeUpdate(T entity); //TODO: is there another approach

  void validateUpdate(T entity);

  void validateDelete(T entity);

  void validateCreate(T entity);

}
