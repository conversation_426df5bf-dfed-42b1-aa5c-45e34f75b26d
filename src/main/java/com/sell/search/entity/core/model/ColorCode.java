package com.sell.search.entity.core.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ColorCode implements Serializable {

  private final String hexCode;
  private final String operator;
  private final Number from;
  private final Number to;
  private final Number value;

  @JsonCreator
  public ColorCode(
      @JsonProperty("hexCode") String hexCode,
      @JsonProperty("operator") String operator,
      @JsonProperty("from") Number from,
      @JsonProperty("to") Number to,
      @JsonProperty("value") Number value) {
    this.hexCode = hexCode;
    this.operator = operator;
    this.from = from;
    this.to = to;
    this.value = value;
  }
}