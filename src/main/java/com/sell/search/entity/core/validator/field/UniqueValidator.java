package com.sell.search.entity.core.validator.field;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.search.core.domain.ErrorResource;
import com.sell.search.core.domain.FieldErrorResource;
import com.sell.search.core.utils.SecurityUtil;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.exception.CoreEntityErrorCodes;
import com.sell.search.entity.core.exception.EntityException;
import com.sell.search.entity.core.exception.UniqueValidationException;
import com.sell.search.entity.core.model.CustomFieldAwareBaseEntity;
import com.sell.search.entity.core.model.Email;
import com.sell.search.entity.core.model.FieldType;
import com.sell.search.entity.core.model.PhoneNumber;
import com.sell.search.entity.core.util.EntityStringUtils;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.persistence.Entity;
import javax.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

/**
 * Created by hemants on 19/04/19.
 * The validator class for isUnique validation
 */
@Slf4j
public class UniqueValidator<T extends CustomFieldAwareBaseEntity> implements DataTypeValidator {

  private final Class<T> entityClass;
  private final EntityManager em;

  /**
   * Constructor
   *
   * @param entityClass required to build select statement
   * @param em          required to execute the statement
   */
  public UniqueValidator(Class<T> entityClass, EntityManager em) {
    this.entityClass = entityClass;
    this.em = em;
  }

  /**
   * Applicable for those fields only
   */
  private final List<FieldType> uniqueApplicableList = Arrays.asList(
      FieldType.TEXT_FIELD,
      FieldType.NUMBER,
      FieldType.EMAIL,
      FieldType.PHONE,
      FieldType.URL, //TODO: Pending
      FieldType.LOOK_UP //TODO: Pending
  );

  /**
   * Validate the custom field
   *
   * @param customFields
   * @param allowedCustomFields
   */
  @Override
  public boolean validate(Map<String, Object> customFields, Map<String, Field> allowedCustomFields) {

    allowedCustomFields.entrySet().stream()
        .filter(entry -> uniqueApplicableList.contains(entry.getValue().getType()) && !ObjectUtils.isEmpty(entry.getValue().getIsUnique()) && entry.getValue().getIsUnique())
        .forEach(entry -> {
          String key = entry.getKey();
          if (customFields.containsKey(key)) {
            Field field = allowedCustomFields.get(key);
            String idToIgnore = getIdToIgnore(customFields);
            Object value = customFields.get(entry.getKey());
            Map<String, Object> toCompare = new HashMap<>();
            toCompare.put(key, value);
            validate(field, toCompare, key, idToIgnore);
          }
        });
    return true;
  }

  private String getIdToIgnore(Map<String, Object> customFields){
    String idToIgnore = "";
    if(!ObjectUtils.isEmpty(customFields.get("id"))){
      idToIgnore = customFields.get("id").toString(); //for ignoring update
    }else{
      //just log that message
      log.error("Supplied entity does not have id field and its value. This might disable isUnique field value update");
    }
    return idToIgnore;
  }

  /**
   * Validate based on if it is multi valued or not
   *
   * @param field
   * @param value
   * @param key
   */
  private void validate(Field field, Map<String, Object> value, String key, String idToIgnore) {
    if (!ObjectUtils.isEmpty(field.getMultiValue()) && field.getMultiValue()) {
      List<Object> listOfValues = (List<Object>) value.get(field.getName());
      listOfValues.forEach(o -> {
        Map<String, Object> mapHavingSingleListObject = new HashMap<>();
        mapHavingSingleListObject.put(field.getName(), Arrays.asList(o));
        validateSingle(field, mapHavingSingleListObject, key, idToIgnore);
      });
    } else {
      validateSingle(field, value, key, idToIgnore);
    }
  }

  /**
   * Validate single field. First build the query using {@link JsonQueryBuilder}, then execute the query
   * based on query execution result raise exception if required.
   *
   * @param field
   * @param value
   * @param key
   */
  private void validateSingle(Field field, Map<String, Object> value, String key, String idToIgnore) {
    JsonQueryBuilder jsonQueryBuilder = new JsonQueryBuilder(entityClass, field);
    Map<String, Object> valueObjectToCompare = getValueObjectToCompare(field, value);
    jsonQueryBuilder.value(valueObjectToCompare);
    String query;
    try {
      query = jsonQueryBuilder.build();
    } catch (JsonProcessingException e) {
      log.error("Error while processing JSON", e);
      throw new UniqueValidationException("Invalid Json");
    }
    executeAndRaiseExceptionIfRequired(query, key, valueObjectToCompare, idToIgnore);
  }

  /**
   * Execute the query, if result is not empty raise the exception
   *
   * @param query
   * @param key
   * @param valueObjectToCompare
   */
  private void executeAndRaiseExceptionIfRequired(String query, String key, Object valueObjectToCompare, String idToIgnore) {
    List resultList = em.createNativeQuery(query).getResultList();
    if(!resultList.isEmpty()){
      int size = resultList.size();
      if(size == 1){
        String id = ((BigInteger) resultList.get(0)).toString();
        if (!idToIgnore.equals(id)){
          throwException(key, valueObjectToCompare);
        }
      }else{
        log.error("Unique field : {}  is having multiple data, Ideally this should never happen.", key);
        throwException(key, valueObjectToCompare);
      }
    }
  }


  /**
   * Return the value to compare based on field type
   *
   * @param field
   * @param value
   * @return
   */
  private Map<String, Object> getValueObjectToCompare(Field field, Map<String, Object> value) {
    switch (field.getType()) {
      case NUMBER:
      case TEXT_FIELD:
        return value;
      case EMAIL:
        List<Email> emails = getListBasedOnType(field, value);
        List<Map<String, Object>> emailsToReturn = emails
            .parallelStream()
            .map(this::getEmailToSearchAgainst)
            .collect(Collectors.toList());
        updateValueBasedOnMultiValue(field, value, emailsToReturn);

        return value;
      case PHONE:
        List<PhoneNumber> phoneNumbers = getListBasedOnType(field, value);
        List<Map<String, Object>> phoneNumbersToReturn = phoneNumbers
            .parallelStream()
            .map(this::getPhoneToSearchAgainst)
            .collect(Collectors.toList());
        updateValueBasedOnMultiValue(field, value, phoneNumbersToReturn);
        return value;
      default:
        throw new UniqueValidationException("Invalid field type " + field.getType()); //Internal exception no need to have error code and all.
    }
  }


  /**
   * Update the value to compare based on if it multivalue or not
   *
   * @param field
   * @param value
   * @param emailsToReturn
   */
  private void updateValueBasedOnMultiValue(Field field, Map<String, Object> value, List<Map<String, Object>> emailsToReturn) {
    if (!ObjectUtils.isEmpty(field.getMultiValue()) && field.getMultiValue()) {
      value.put(field.getName(), emailsToReturn);
    } else {
      value.put(field.getName(), emailsToReturn.get(0));
    }
  }

  /**
   * Convert value to list so the we can handle it in common way
   *
   * @param field
   * @param value
   * @param <U>
   * @return
   */
  private <U> List<U> getListBasedOnType(Field field, Map<String, Object> value) {
    if (!ObjectUtils.isEmpty(field.getMultiValue()) && field.getMultiValue()) {
      return (List<U>) value.get(field.getName());
    } else {
      return Arrays.asList((U) value.get(field.getName()));
    }
  }

  /**
   * As email is object, need to pick those values inside the object which relevant for search
   *
   * @param email
   * @return
   */
  private Map<String, Object> getEmailToSearchAgainst(Email email) {
    Map<String, Object> emailToReturn = new HashMap<>();
    emailToReturn.put("value", email.getValue());
    return emailToReturn;
  }

  /**
   * As phoneNumber is object, need to pick those values inside the object which relevant for search
   *
   * @param phoneNumber
   * @return
   */
  private Map<String, Object> getPhoneToSearchAgainst(PhoneNumber phoneNumber) {
    Map<String, Object> phoneNumberToReturn = new HashMap<>();
    phoneNumberToReturn.put("code", phoneNumber.getCode());
    phoneNumberToReturn.put("value", phoneNumber.getValue());
    return phoneNumberToReturn;
  }

  /**
   * A common method to throw an exception
   *
   * @param k
   * @return
   */
  private EntityException throwException(String k, Object value) {
    ErrorResource eR = new ErrorResource(CoreEntityErrorCodes.COMMON_VALIDATION_ERROR.getCode(),
        CoreEntityErrorCodes.COMMON_VALIDATION_ERROR.getMessage());
    eR.fieldErrors(Arrays.asList(new FieldErrorResource(k, "field.validation.constraints.Unique.message", value)));
    throw new EntityException(eR);
  }


  /**
   * The class required to build custom field query which is need to check the uniqueness. As the scope is
   * limited to this validator only this class is inner class to the validator
   */
  public class JsonQueryBuilder {

    public JsonQueryBuilder(Class<T> forEntity, Field field){
      this.forEntity = forEntity;
      if(!ObjectUtils.isEmpty(field.getStandard())){
        this.isStandard = field.getStandard();
      }
      this.type = field.getType();
    }

    /**
     * The final query will be store in this variable
     */
    StringBuilder query = new StringBuilder();

    private static final String SELECT_FROM = "select id from ";
    private final String where = " where deleted = false and tenant_id = " + SecurityUtil.getTenantId() ;

    Map<String, Object> value = null;
    Class<T> forEntity = null;
    boolean isStandard = false;
    FieldType type;

    /**
     * Fetch table name from class. First check if it entity or not, then check if entity has table annotation on it or not
     * If no table annotation create table name from class using {@link EntityStringUtils}
     *
     * @return
     */
    public String getTableName() {
      if (!ObjectUtils.isEmpty(forEntity)) {
        if (forEntity.isAnnotationPresent(Entity.class)) {
          Entity entityAnnotation = forEntity.getAnnotation(Entity.class);
          String t = entityAnnotation.name();
          return (ObjectUtils.isEmpty(t)) ? EntityStringUtils.splitByCamelCase
              .andThen(EntityStringUtils.lowerCase)
              .andThen(EntityStringUtils.joinByUnderscore).apply(forEntity.getSimpleName()) : t;
        } else {
          throw new UniqueValidationException("Supplied class is not an entity");
        }
      }
      throw new UniqueValidationException("Entity is null");
    }

    /**
     * Fluent api to set value
     *
     * @param value
     * @return
     */
    public JsonQueryBuilder value(Map<String, Object> value) {
      this.value = value;
      return this;
    }

    /**
     * Fluent api to set entity class
     *
     * @param forEntity
     * @return
     */
    public JsonQueryBuilder forEntity(Class<T> forEntity) {
      this.forEntity = forEntity;
      return this;
    }

    /**
     * Fluent api to set isStandard
     *
     * @param isStandard
     * @return
     */
    public JsonQueryBuilder isStandard(boolean isStandard) {
      this.isStandard = isStandard;
      return this;
    }

    /**
     * Fluent api to set type
     *
     * @param type
     * @return
     */
    public JsonQueryBuilder type(FieldType type) {
      this.type = type;
      return this;
    }

    /**
     * The build method which construct and return the query
     *
     * @return
     * @throws JsonProcessingException
     */
    public String build() throws JsonProcessingException {
      if (ObjectUtils.isEmpty(value)) {
        throw new UniqueValidationException("Invalid field Name or value");
      }
      String tableName = getTableName();

      return query
          .append(SELECT_FROM)
          .append(tableName)
          .append(where)
          .append(getWhereCondition())
          .toString();
    }

    private String getWhereCondition() throws JsonProcessingException {
      if(isStandard){
        return getWhereBasedOnType();
      }else{
        //For custom field
        return " and custom_field @> '" + getValueAsString(value) + "'";
      }
    }

    private String getWhereBasedOnType() throws JsonProcessingException {
      String key = value.keySet().iterator().next();
      switch (type){
        case EMAIL:
        case PHONE:
          return " and  " + getColumnName(key) + " @> '" + getValueAsString(value.get(key)) + "'";
        case TEXT_FIELD:
          return " and " + getColumnName(key) + " = '" + value.get(key) + "'";
        default:
          return " and " + getColumnName(key) + " = " + getValueAsString(value.get(key));
      }
    }

    private String getColumnName(String javaFieldName){
      return EntityStringUtils.splitByCamelCase
          .andThen(EntityStringUtils.lowerCase)
          .andThen(EntityStringUtils.joinByUnderscore)
          .apply(javaFieldName);
    }

    private String getValueAsString(Object value) throws JsonProcessingException {
      return new ObjectMapper().writeValueAsString(value);
    }

  }

}
