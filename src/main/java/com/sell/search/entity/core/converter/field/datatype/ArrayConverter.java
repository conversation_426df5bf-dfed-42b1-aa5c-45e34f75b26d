package com.sell.search.entity.core.converter.field.datatype;

import com.sell.search.entity.core.converter.field.datatype.DataTypeConverter;
import com.sell.search.entity.core.converter.field.datatype.FieldContext;
import com.sell.search.entity.core.converter.field.factory.FieldConverterFactory;
import com.sell.search.entity.core.entity.Field;


/**
 * Created by hemants on 16/04/19.
 */
public class ArrayConverter implements DataTypeConverter {

  FieldConverterFactory fieldConverterFactory;

  public ArrayConverter(FieldConverterFactory fieldConverterFactory) {
    this.fieldConverterFactory = fieldConverterFactory;
  }

  @Override
  public boolean canConvert(FieldContext fieldContext) {
    return fieldContext.getFieldType().isArray();
  }

  @Override
  public Field convert(FieldContext fieldContext) {
    fieldContext.setFieldType(fieldContext.getFieldType().getComponentType());
    Field result = fieldConverterFactory.convertToFieldDataType(fieldContext);
    result.isMultiValue(true);
    return result;
  }

}
