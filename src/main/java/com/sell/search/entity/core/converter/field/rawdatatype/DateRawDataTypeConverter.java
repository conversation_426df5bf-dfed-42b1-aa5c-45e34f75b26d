package com.sell.search.entity.core.converter.field.rawdatatype;

import com.sell.search.entity.core.converter.field.rawdatatype.RawDataTypeConverter;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import java.sql.Timestamp;
import java.time.OffsetDateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

/**
 * Created by hemants on 24/04/19.
 * The Date raw data type converter
 */
@Slf4j
public class DateRawDataTypeConverter implements RawDataTypeConverter {

  /**
   * Only for DATE_PICKER
   *
   * @param definedDataType
   * @return
   */
  @Override
  public boolean canConvert(Field definedDataType) {
    return !ObjectUtils.isEmpty(definedDataType.getMultiValue()) && !definedDataType.getMultiValue() && FieldType.DATE_PICKER.equals(definedDataType.getType());
  }

  /**
   * try to convert into localDate else raise exception
   *
   * @param rawObject
   * @param field
   * @return
   */
  @Override
  public Object convert(Object rawObject, Field field) {
    try {
      return OffsetDateTime.parse(rawObject.toString()).toLocalDate();
    } catch (Exception e) {
      log.error("Error converting to LocalDate", e);
      throwException(field.getName(), rawObject, field.getType().toString());
    }
    try{
      log.error("trying using epoch");
      return new Timestamp(Long.valueOf(rawObject.toString())).toLocalDateTime();
    }catch(Exception e){
      throwException(field.getName(), rawObject, field.getType().toString());
    }
    return rawObject;
  }

  private void throwException(String k, Object object, String converterName) {
    RawDatatypeExceptionHelper.throwException(k, object, converterName, "field.conversion.error.message" );
  }

}
