package com.sell.search.entity.core.service;

import com.sell.search.core.domain.EntityType;
import com.sell.search.core.utils.EnvProfileUtil;
import com.sell.search.entity.core.service.LookupServiceDiscovery;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;
import org.springframework.util.ObjectUtils;

/**
 * Created by hemants on 03/05/19.
 */
public class MapBasedLookupServiceDiscovery implements LookupServiceDiscovery {

  private static final Map<EntityType, EntitySearchLocator> entityTypeUrlMap = new HashMap<>();
  private final EnvProfileUtil envProfileUtil;

  @Data
  class EntitySearchLocator {

    private final String url;
    private final String fieldToSearchAgainst;
  }

  public MapBasedLookupServiceDiscovery(EnvProfileUtil envProfileUtil) {
    this.envProfileUtil = envProfileUtil;
    entityTypeUrlMap.put(EntityType.LEAD, new EntitySearchLocator("/search/lead/lookup", "firstName"));
    entityTypeUrlMap.put(EntityType.DEAL, new EntitySearchLocator("/search/deal/lookup", "name"));
    entityTypeUrlMap.put(EntityType.COMPANY, new EntitySearchLocator("/companies/lookup", "name"));
    entityTypeUrlMap.put(EntityType.CONTACT, new EntitySearchLocator("/search/contact/lookup", "firstName"));

    entityTypeUrlMap.put(EntityType.USER, new EntitySearchLocator("/users/lookup", "firstName"));
    entityTypeUrlMap.put(EntityType.TEAM, new EntitySearchLocator("/teams/lookup", "name"));
    entityTypeUrlMap.put(EntityType.PROFILE, new EntitySearchLocator("/profiles/lookup", "name"));

    entityTypeUrlMap.put(EntityType.PIPELINE, new EntitySearchLocator("/pipelines/lookup", "name"));

    entityTypeUrlMap.put(EntityType.TASK, new EntitySearchLocator("/tasks/lookup", "name"));
    entityTypeUrlMap.put(EntityType.NOTE, new EntitySearchLocator("/notes/lookup", "title"));
    entityTypeUrlMap.put(EntityType.PRODUCT, new EntitySearchLocator("/products/lookup", "name"));
  }

  @Override
  public String getUrl(EntityType entityType) {
    return decorateWithQueryParams(entityTypeUrlMap.get(entityType), "");
  }

  @Override
  public String getUrlWithParentEntity(EntityType entityType, EntityType parentEntity) {
    return decorateWithQueryParams(entityTypeUrlMap.get(entityType), "entityType=" + parentEntity.getLabel().toUpperCase());
  }


  private String decorateWithQueryParams(EntitySearchLocator entitySearchLocator, String queryParam) {
    return ObjectUtils.isEmpty(entitySearchLocator) ? ""
        : queryParam.isEmpty() ? String.format("%s?q=%s:", entitySearchLocator.getUrl(), entitySearchLocator.getFieldToSearchAgainst()) :
            String.format("%s?%s&q=%s:", entitySearchLocator.getUrl(), queryParam, entitySearchLocator.getFieldToSearchAgainst());
  }
}
