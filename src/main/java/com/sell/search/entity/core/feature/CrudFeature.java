package com.sell.search.entity.core.feature;

import com.sell.search.core.domain.BaseEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.Serializable;

/**
 * Created by hemants on 07/04/19.
 *  The interface needs to extends in Service Interface if crud functionality is required.
 */
public interface CrudFeature<T extends BaseEntity, ID extends Serializable> {

  T create(T entity);

  T get(ID id);

  Page<T> getList(Pageable pageable);

  T update(T entity);

  T delete(T entity);
}
