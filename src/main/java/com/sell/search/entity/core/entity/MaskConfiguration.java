package com.sell.search.entity.core.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sell.search.core.annotation.AccessPermission;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.LongListType;
import java.io.Serializable;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MapsId;
import javax.persistence.OneToOne;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

@Getter
@Setter
@AccessPermission("config")
@NoArgsConstructor
@AllArgsConstructor
public class MaskConfiguration implements Serializable {

  private Long id;

  private Field field;

  private boolean enabled;

  private List<Long> profileIds;

  private MaskConfiguration(Field field, boolean enabled, List<Long> profileIds){
    this.field = field;
    this.enabled = enabled;
    this.profileIds = profileIds;
  }

  public static MaskConfiguration createNew(Field field, List<Long> profileIds){
    return new MaskConfiguration(
        field,
        false,
        profileIds
    );
  }
}