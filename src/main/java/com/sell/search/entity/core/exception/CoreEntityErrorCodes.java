package com.sell.search.entity.core.exception;

import com.sell.search.core.domain.ErrorCodes;
import com.sell.search.core.domain.ErrorResource;

/**
 * Created by hemants on 04/04/19.
 */
public class CoreEntityErrorCodes extends ErrorCodes {

  private static final String SERVICE_CODE = "006";
  public static final ErrorResource INVALID_ENTITY = new ErrorResource(SERVICE_CODE + "001", "entity.invalid");
}
