package com.sell.search.entity.core.service;

import com.sell.search.core.utils.SecurityUtil;
import lombok.RequiredArgsConstructor;
import org.elasticsearch.client.RestHighLevelClient;

/**
 * Created by hemants on 27/05/19.
 */
@RequiredArgsConstructor
public abstract class AbstractIdNameService {

  protected final RestHighLevelClient newClient;

  protected String getIndexName() {
    return SecurityUtil.getTenantId() + "-id-name";
  }
}
