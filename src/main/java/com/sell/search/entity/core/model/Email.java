package com.sell.search.entity.core.model;

import com.sell.search.entity.core.model.EmailType;
import java.io.Serializable;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Created by shashanks3 on 30/3/19.
 */
@NoArgsConstructor
@EqualsAndHashCode
public class Email implements Serializable {
  com.sell.search.entity.core.model.EmailType type;
  @org.hibernate.validator.constraints.Email
  String value;
  boolean isPrimary;

  public com.sell.search.entity.core.model.EmailType getType() {
    return type;
  }

  public void setType(EmailType type) {
    this.type = type;
  }

  public String getValue() {
    return value;
  }

  public void setValue(@org.hibernate.validator.constraints.Email String value) {
    this.value = value != null ? value.toLowerCase() : value;
  }

  public boolean isPrimary() {
    return isPrimary;
  }

  public void setPrimary(boolean primary) {
    isPrimary = primary;
  }
}
