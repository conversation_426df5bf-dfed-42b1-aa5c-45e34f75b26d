package com.sell.search.entity.core.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sell.search.core.annotation.AccessPermission;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.ColorCode;
import com.sell.search.entity.core.model.ConfigurationJsonType;
import java.io.Serializable;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MapsId;
import javax.persistence.OneToOne;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ColorConfiguration implements Serializable{

  private Long id;

  private Field field;

  private List<ColorCode> colorCodes;

  public ColorConfiguration update(List<ColorCode> colorCodes){
    this.colorCodes = colorCodes;
    return this;
  }
}