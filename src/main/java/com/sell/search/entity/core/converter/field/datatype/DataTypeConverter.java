package com.sell.search.entity.core.converter.field.datatype;


import com.sell.search.entity.core.converter.field.datatype.FieldContext;
import com.sell.search.entity.core.entity.Field;

/**
 * Created by hemants on 16/04/19.
 * The interface for converting java class into custom field type based on its type
 */
public interface DataTypeConverter<T> {

  /**
   * Check if implementation can convert the class or not.
   *
   * @param
   * @return
   */
  boolean canConvert(FieldContext fieldContext);

  /**
   * An implementation to do actual conversion.
   *
   * @param
   * @return
   */
  Field convert(FieldContext fieldContext);
}
