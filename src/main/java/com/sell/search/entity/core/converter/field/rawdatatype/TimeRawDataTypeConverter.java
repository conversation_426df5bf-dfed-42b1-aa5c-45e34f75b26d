package com.sell.search.entity.core.converter.field.rawdatatype;

import com.sell.search.entity.core.converter.field.rawdatatype.RawDataTypeConverter;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import java.time.OffsetDateTime;
import org.springframework.util.ObjectUtils;

/**
 * Created by hemants on 24/04/19.
 * The time raw data type converter
 */
public class TimeRawDataTypeConverter implements RawDataTypeConverter {


  /**
   * Supports TIME_PICKER
   *
   * @param definedDataType
   * @return
   */
  @Override
  public boolean canConvert(Field definedDataType) {
    return !ObjectUtils.isEmpty(definedDataType.getMultiValue()) && !definedDataType.getMultiValue() && FieldType.TIME_PICKER.equals(definedDataType.getType());
  }

  /**
   * Convert to offsetTime
   *
   * @param rawObject
   * @param field
   * @return
   */
  @Override
  public Object convert(Object rawObject, Field field) {
    try {
      return OffsetDateTime.parse(rawObject.toString()).toOffsetTime();
    } catch (Exception e) {
      throwException(field.getName(), rawObject, field.getType().toString());
    }
    return rawObject;
  }

  private void throwException(String k, Object object, String converterName) {
    RawDatatypeExceptionHelper.throwException(k, object, converterName, "field.conversion.error.message" );
  }

}