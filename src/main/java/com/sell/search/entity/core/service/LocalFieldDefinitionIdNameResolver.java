package com.sell.search.entity.core.service;

import com.sell.search.entity.core.converter.field.datatype.FieldContext;
import com.sell.search.entity.core.converter.field.datatype.LookupConverter;
import com.sell.search.entity.core.dto.SearchResponseDetailsDTO;
import com.sell.search.entity.core.dto.SearchResponseWithMetaData;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.service.AbstractIdNameResolver;
import com.sell.search.entity.core.service.IdNameSearchService;
import org.apache.commons.lang3.reflect.FieldUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by hemants on 16/07/19.
 */
public class LocalFieldDefinitionIdNameResolver extends AbstractIdNameResolver {


  public LocalFieldDefinitionIdNameResolver(IdNameSearchService idNameSearchService) {
    super(idNameSearchService);
  }

  public SearchResponseWithMetaData getSearchResultWithIdNameStoreMap(SearchResponseDetailsDTO searchResponse,
      Class resourceName) {
    CompletableFuture<Map<String, Map<Field, List>>> fieldDefinitionFuture = getFieldDefinitionFromClassFuture(resourceName);
    return executeSearchResultAndAppendIdNameStoreMap(CompletableFuture.supplyAsync(() -> searchResponse), fieldDefinitionFuture);
  }

  private CompletableFuture<Map<String, Map<Field, List>>> getFieldDefinitionFromClassFuture(Class clazz) {
    java.lang.reflect.Field[] fields = FieldUtils.getAllFields(clazz);
    LookupConverter lookupConverter = new LookupConverter();

    List<Field> validFields = Stream.of(fields)
        .filter(f -> lookupConverter.canConvert(new FieldContext(f, f.getType(), clazz)))
        .map(f -> {
          Field field = lookupConverter.convert(new FieldContext(f, f.getType(), clazz));
          field.setName(f.getName());
          return field;
        })
        .map(f -> {
          f.setStandard(true);
          return f;
        })
        .collect(Collectors.toList());

    return CompletableFuture.supplyAsync(() ->
            makeRequiredDataStructure(validFields)
        , getDelegatingSecurityContextAsyncTaskExecutor());
  }
}
