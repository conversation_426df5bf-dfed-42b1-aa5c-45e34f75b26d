package com.sell.search.entity.core.service;

import com.sell.search.core.domain.ErrorCodes;
import com.sell.search.core.domain.TenantAwareBaseEntity;
import com.sell.search.core.event.CoreEvent;
import com.sell.search.core.event.EventEmitter;
import com.sell.search.core.exception.ResourceNotFoundException;
import com.sell.search.core.utils.SecurityUtil;
import com.sell.search.entity.core.annotation.Eventable;
import com.sell.search.entity.core.model.CrudEnum;
import com.sell.search.entity.core.service.CrudService;
import com.sell.search.entity.core.service.EventableCrudService;
import java.io.Serializable;
import java.lang.annotation.Annotation;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

/**
 * Created by hemants on 06/04/19.
 * The abstract implementation of event generation on crud on entity functionality. This class decorate crud method with eventable functionality
 */
@Slf4j
public abstract class AbstractEventableCrudService<T extends TenantAwareBaseEntity, ID extends Serializable> implements EventableCrudService<T, ID> {

  private final com.sell.search.entity.core.service.CrudService<T, ID> crudService;
  private final TransactionTemplate transactionTemplate;
  private final EventEmitter eventEmitter;

  /**
   * Constructor
   * @param crudService Needs to be qualified with @Qualifier annotation, This service provide crud service.
   * @param transactionManager
   * @param coreEventEmitter
   */
  public AbstractEventableCrudService(CrudService<T, ID> crudService, PlatformTransactionManager transactionManager, EventEmitter coreEventEmitter){
    Assert.notNull(transactionManager, "The 'transactionManager' argument must not be null.");
    Assert.notNull(crudService, "The 'crudService' argument must not be null.");
    Assert.notNull(coreEventEmitter, "The 'eventEmitter' argument must not be null.");
    this.transactionTemplate = new TransactionTemplate(transactionManager);
    this.crudService = crudService;
    this.eventEmitter = coreEventEmitter;
  }


  /**
   * This method should be called by client to create an entity. This first call overridden create method then generate
   * an event
   *
   * @param entity
   * @return
   */
  @Override
  public T create(T entity) {
    return processEntity(crudService::create, entity, CrudEnum.CREATED.toString());
  }

  /**
   * This method should be called by client to update an entity. This first call overridden updated method then generate
   * an event
   *
   * @param entity
   * @return
   */
  @Override
  public T update(T entity) {
    return processEntity(crudService::update, entity, CrudEnum.UPDATED.toString());
  }

  /**
   * This method should be called by client to delete an entity. This first call overridden delete method then generate
   * an event
   *
   * @param entity
   * @return
   */
  @Override
  public T delete(T entity) {
    return processEntity(crudService::delete, entity, CrudEnum.DELETED.toString());
  }


  /**
   * This method is called by create, delete and update method If first set tenantid to the entity with correct tenantid then send the event
   *
   * @param operation function which needs to be executed
   * @param entity
   * @param action
   * @return
   */
  public T processEntity(Function<T, T> operation, T entity, String action) {
    return transactionTemplate.execute(status -> {
      entity.setTenantId(SecurityUtil.getTenantId());
      T result = operation.apply(entity);
      TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
        @Override public void suspend() { }
        @Override public void resume() { }
        @Override public void flush() { }
        @Override public void beforeCommit(boolean readOnly) { }
        @Override public void beforeCompletion() { }
        @Override public void afterCompletion(int status) { }
        @Override public void afterCommit() {
          log.debug("after commit");
          sendEvent(result, action);
        }
      });
      return result;
    });
  }

  /**
   * Send event
   *
   * @param entity
   * @param operation
   */
  protected void sendEvent(T entity, String operation) {
    if (entity != null) {
      Annotation[] eventables = entity.getClass().getAnnotationsByType(Eventable.class);
      if (eventEmitter != null && eventables.length > 0) {
        String entityName = getEntityName(entity);
        String serviceName = eventEmitter.getServiceName();
        String eventName = String.format("%s.%s.%s", serviceName, entityName, operation);
        String eventDescription = String.format("%s %s", entityName, operation);
        eventEmitter.emit(new CoreEvent(eventName, eventDescription, entity.getClass()), entity);
      }
    }
  }

  /**
   * Use crud service get to return entity, not need to generate event for get.
   * @param id
   * @throws ResourceNotFoundException {@link ErrorCodes.COMMON_NOT_FOUND}
   * @return
   */
  @Override
  public T get(ID id){
    return crudService.get(id);
  }

  /**
   * Use crud service get to return entity, not need to generate event for get.
   * @param id
   * @return entity or null
   */
  @Override
  public T getOrNull(ID id) {
    return crudService.getOrNull(id);
  }

  /**
   * Use crud service get to return entity, not need to generate event for get.
   * @param pageable
   * @return
   */
  @Override
  public Page<T> getList(Pageable pageable){
    return crudService.getList(pageable);
  }

  private String getEntityName(T entity) {
    return entity.getClass().getSimpleName().toLowerCase();
  }
}
