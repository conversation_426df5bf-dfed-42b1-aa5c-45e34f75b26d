package com.sell.search.entity.core.converter.field.datatype;

import static com.sell.search.entity.core.converter.field.datatype.ConverterUtil.getAnnotationFromFieldGetter;

import com.sell.search.entity.core.annotation.LookupField;
import com.sell.search.entity.core.converter.field.datatype.DataTypeConverter;
import com.sell.search.entity.core.converter.field.datatype.FieldContext;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import java.lang.annotation.Annotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;


/**
 * Created by Shashank .
 */
@Slf4j
public class LookupConverter implements DataTypeConverter {

  @Override
  public boolean canConvert(FieldContext fieldContext) {
    return fieldContext.getField().getAnnotationsByType(LookupField.class).length > 0
        || getAnnotationFromFieldGetter(fieldContext.getField(), fieldContext.getFieldClass(), LookupField.class).length > 0;
  }

  @Override
  public Field convert(FieldContext fieldContext) {
    Annotation[] annotations = fieldContext.getField().getAnnotationsByType(LookupField.class);
    if (ObjectUtils.isEmpty(annotations)) {
      annotations = getAnnotationFromFieldGetter(fieldContext.getField(), fieldContext.getFieldClass(), LookupField.class);
    }
    Field fieldDataType = new Field().type(FieldType.LOOK_UP);
    fieldDataType.setLookupForEntity(((LookupField) annotations[0]).value().name());
    fieldDataType.setInternalType(((LookupField) annotations[0]).internalType());
    return fieldDataType;
  }

}
