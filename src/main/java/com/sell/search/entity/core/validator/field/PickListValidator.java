package com.sell.search.entity.core.validator.field;

import com.sell.search.core.domain.ErrorResource;
import com.sell.search.core.domain.FieldErrorResource;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.entity.PicklistValue;
import com.sell.search.entity.core.exception.CoreEntityErrorCodes;
import com.sell.search.entity.core.exception.EntityException;
import com.sell.search.entity.core.model.FieldType;
import com.sell.search.entity.core.validator.field.DataTypeValidator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by shashanks3 on 13/5/19.
 */
public class PickListValidator implements DataTypeValidator {

  /**
   * Validate for picklist and multi picklist
   */
  @Override
  public boolean validate(Map<String, Object> customFields, Map<String, Field> allowedCustomFields) {

    allowedCustomFields.entrySet().stream().filter(entry -> FieldType.PICK_LIST.equals(entry.getValue().getType())).forEach(entry -> {
      if (customFields.containsKey(entry.getKey())) {
        Field field = allowedCustomFields.get(entry.getKey());
        if (!validatePickList(customFields.get(entry.getKey()), field)) {
          throwException(field.getName(), customFields.get(entry.getKey()).toString());
        }
      }
    });
    return true;
  }

  private boolean validatePickList(Object pickListValue, Field field) {
    if (pickListValue == null) {
      return true;
    }
    Map<String, PicklistValue> hm = new HashMap<>();

    if (field.getPicklist() != null) {
      List<PicklistValue> picklistValues = field.getPicklist().getValues();
      if (field.getInternal() != null && field.getInternal()) {
        picklistValues.stream().forEach(picklist -> hm.put(picklist.getName(), picklist));
      } else {
        picklistValues.stream().forEach(picklist -> hm.put(picklist.getId().toString(), picklist));
      }
    }
    if (pickListValue instanceof Number) {
      return isValidPickList(((Number) pickListValue).toString(), hm);
    } else if (pickListValue instanceof String) {
      return isValidPickList(pickListValue.toString(), hm);
    } else if (pickListValue instanceof ArrayList) {
      List<Object> inputPicklists = (List<Object>) pickListValue;
      return isValidMultiPickList(inputPicklists, hm);
    }
    throw new EntityException(CoreEntityErrorCodes.COMMON_VALIDATION_ERROR);
  }

  private Boolean isValidMultiPickList(List<Object> inputPicklists, Map<String, PicklistValue> pickListHashMap) {
    for (Object pickListValue : inputPicklists) {
      if (!(pickListValue instanceof Number)) {
        throw new EntityException(CoreEntityErrorCodes.COMMON_VALIDATION_ERROR);
      }
      if (!pickListHashMap.containsKey(((Number) pickListValue).toString())) {
        return false;
      }
    }
    return true;
  }

  private boolean isValidPickList(String pickListId, Map<String, PicklistValue> pickListHashMap) {
    return pickListHashMap.containsKey(pickListId);
  }

  /**
   * A common method to throw an exception
   */
  private EntityException throwException(String k, String pickListValue) {
    ErrorResource eR =
        new ErrorResource(CoreEntityErrorCodes.COMMON_VALIDATION_ERROR.getCode(), CoreEntityErrorCodes.COMMON_VALIDATION_ERROR.getMessage());
    eR.fieldErrors(Arrays.asList(new FieldErrorResource(k, "field.not.contain.picklist.error", pickListValue)));
    throw new EntityException(eR);
  }
}
