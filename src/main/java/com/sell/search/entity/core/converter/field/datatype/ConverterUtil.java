package com.sell.search.entity.core.converter.field.datatype;

import java.lang.annotation.Annotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

@Slf4j
public class ConverterUtil {

  public static Annotation[] getAnnotationFromFieldGetter(java.lang.reflect.Field field, Class fieldClass, Class annotationClass) {
    String methodName = "get" + capitalizeFirstChar(field.getName());
    try {
      return fieldClass.getDeclaredMethod(methodName).getAnnotationsByType(annotationClass);
    } catch (NoSuchMethodException e) {
      log.debug("Method {} does not exist for {} class", methodName, field.getDeclaringClass().getSimpleName());
      return new Annotation[0];
    }
  }

  private static String capitalizeFirstChar(String inputString) {
    return ObjectUtils.isEmpty(inputString) ? inputString : inputString.substring(0, 1).toUpperCase() + inputString.substring(1);
  }

}
