package com.sell.search.entity.core.model;


import com.sell.search.entity.core.model.CustomFieldJsonUserType;
import java.util.Map;
import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import com.sell.search.core.domain.TenantOwnerAwareBaseEntity;
import lombok.Getter;
import lombok.Setter;


/**
 * Created by hemants on 11/02/19.
 * This entity needs to be extends if an entity require custom field functionality.
 */

@MappedSuperclass
@TypeDef(name = "CustomFieldJsonUserType", typeClass = CustomFieldJsonUserType.class)
@Setter
@Getter
public class CustomFieldAwareBaseEntity extends TenantOwnerAwareBaseEntity {

    // not isRequired
    //@JsonSerialize(using = CustomFieldSerializer.class)
    @Type(type = "CustomFieldJsonUserType")
    @Column(name = "custom_field", columnDefinition = "jsonb")
    private Map<String, Object> customFieldValues;

    public CustomFieldAwareBaseEntity customFieldValues(Map<String, Object> customFieldValues) {
        this.customFieldValues = customFieldValues;
        return this;
    }
}
