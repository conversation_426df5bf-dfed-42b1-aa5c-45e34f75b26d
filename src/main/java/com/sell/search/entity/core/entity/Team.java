package com.sell.search.entity.core.entity;

import com.sell.search.core.constants.InputSize;
import com.sell.search.entity.core.annotation.Internal;
import com.sell.search.entity.core.annotation.LookupField;
import com.sell.search.entity.core.annotation.ExtendedFields;
import com.sell.search.entity.core.annotation.StringField;
import com.sell.search.entity.core.model.CustomFieldAwareBaseEntity;
import com.sell.search.core.domain.EntityType;
import com.sell.search.entity.core.model.StringTypeField;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * Created by shashanks3 on 10/5/19.
 */
@Getter
@Setter
@MappedSuperclass
@ExtendedFields(value = {"ownerId", "createdAt", "updatedAt", "createdBy", "updatedBy"})
public class Team extends CustomFieldAwareBaseEntity {

  @NotEmpty
  private String name;

  @Column(length = InputSize.MAX_INPUT_SIZE_DESCRIPTION)
  @StringField(StringTypeField.PARAGRAPH_TEXT)
  private String description;
  private boolean active = true;

  @Transient
  @Internal
  private Long userCount;

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getOwnerId() {
    return super.getOwnerId();
  }

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getCreatedBy() {
    return super.getCreatedBy();
  }

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getUpdatedBy() {
    return super.getUpdatedBy();
  }

  @Override
  @Internal
  public Date getCreatedAt() {
    return super.getCreatedAt();
  }

  @Override
  @Internal
  public Date getUpdatedAt() {
    return super.getUpdatedAt();
  }
}
