package com.sell.search.entity.core.service;

import com.sell.search.core.domain.EntityType;
import com.sell.search.core.domain.TenantOwnerAwareBaseEntity;
import com.sell.search.core.event.CoreEvent;
import com.sell.search.core.event.CoreEventEmitter;
import com.sell.search.core.repository.BaseJpaRepository;
import com.sell.search.core.utils.SecurityUtil;
import com.sell.search.entity.core.dto.ChangeOwnerMessage;
import com.sell.search.entity.core.dto.ChangeOwnerResponse;
import com.sell.search.entity.core.exception.CoreEntityErrorCodes;
import com.sell.search.entity.core.exception.EntityException;
import com.sell.search.entity.core.exception.OwnerDoesNotExist;
import com.sell.search.entity.core.model.CrudEnum;
import com.sell.search.entity.core.service.ChangeOwnerService;
import com.sell.search.entity.core.service.OwnerService;
import com.sell.search.entity.core.util.AuthenticationSerializer;
import io.jsonwebtoken.lang.Assert;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * Created by hemants on 05/04/19. The abstract implementation of change of ownership functionality
 */
public abstract class AbstractChangeOwnerService<T extends TenantOwnerAwareBaseEntity> implements ChangeOwnerService<T, Long>, OwnerService<Long> {

  private final BaseJpaRepository<T, Long> jpaRepository;
  private final CoreEventEmitter coreEventEmitter;

  /**
   * Specification containing tenantId and list of entity ids
   */
  private Specification<T> findByIds(Collection<Long> ids) {
    return (root, query, cb) -> (
        cb.and(
            root.get("id").in(ids),
            cb.equal(root.get("tenantId"), SecurityUtil.getTenantId())
        )
    );
  }

  /**
   * Constructor
   *
   * @param jpaRepository Needs to be qualified with @Qualifier annotation
   */
  public AbstractChangeOwnerService(BaseJpaRepository<T, Long> jpaRepository, CoreEventEmitter coreEventEmitter) {
    Assert.notNull(jpaRepository, "The 'JpaRepository' argument must not be null.");
    org.springframework.util.Assert.notNull(coreEventEmitter, "The 'eventEmitter' argument must not be null.");
    this.jpaRepository = jpaRepository;
    this.coreEventEmitter = coreEventEmitter;
  }

  /**
   * It first check if owner exists or not. If it exists, it change the owner and raise "OWNER_UPDATED" event
   */
  @Override
  @Transactional
  public ChangeOwnerResponse<Long> updateEntityOwner(Long ownerId, Long entityId, List<EntityType> childEntities, boolean skipOwnerCheck)
      throws OwnerDoesNotExist {
    if (!skipOwnerCheck) {
      getOwner(ownerId);
    }
    T entity = jpaRepository.findOne(entityId);
    if (ObjectUtils.isEmpty(entity)) {
      throw new EntityException(CoreEntityErrorCodes.COMMON_RESOURCE_NOT_FOUND, getEntityName(entity));
    }
    Long oldOwnerId = entity.getOwnerId();
    entity = jpaRepository.updateEntityOwner(entity, ownerId);
    return sendEvents(childEntities, entity, oldOwnerId);
  }

  protected ChangeOwnerResponse<Long> sendEvents(List<EntityType> childEntities, T entity, Long oldOwnerId) {
    ChangeOwnerResponse<Long> changeOwnerResponse = mapEntityToChangeOwnerResponse(entity, oldOwnerId);
    sendEvent(changeOwnerResponse, childEntities, entity, CrudEnum.OWNER_UPDATED.toString());
    sendUpdateEntityEvent(entity);
    return changeOwnerResponse;
  }

  protected void sendUpdateEntityEvent(T entity) {
    String serviceName = coreEventEmitter.getServiceName();
    String entityName = getEntityName(entity);
    String eventName = String.format("%s.%s.%s", serviceName, entityName, CrudEnum.UPDATED);
    String eventDescription = String.format("%s %s", entityName, CrudEnum.UPDATED);
    coreEventEmitter.emit(new CoreEvent(eventName, eventDescription, entity.getClass()), entity);
  }

  /**
   * Update owner based on collection of ids, internally it call updateEntityOwner by single id.
   */
  @Override
  @Transactional
  public Collection<ChangeOwnerResponse<Long>> updateEntityOwner(Long ownerId, Collection<Long> entityIds, List<EntityType> childEntities)
      throws OwnerDoesNotExist {
    getOwner(ownerId);
    Collection<ChangeOwnerResponse<Long>> changeOwnerResponses = new ArrayList<>();
    Collection<T> entities = jpaRepository.findAll(findByIds(entityIds));
    for (T entity : entities) {
      changeOwnerResponses.add(updateEntityOwner(ownerId, entity.getId(), childEntities, true));
    }
    return changeOwnerResponses;
  }

  /**
   * Private function to send event
   */
  private void sendEvent(ChangeOwnerResponse<Long> changeOwnerResponse, List<EntityType> childEntities, T entity, String operation) {
    if (entity != null && coreEventEmitter != null) {
      String entityName = getEntityName(entity);
      String serviceName = coreEventEmitter.getServiceName();
      String eventName = String.format("%s.%s.%s", serviceName, entityName, operation);
      String eventDescription = String.format("%s %s", entityName, operation);
      coreEventEmitter
          .emit(new CoreEvent(eventName, eventDescription, entity.getClass()), mapChangeOwnerResponseToMessage(changeOwnerResponse, childEntities));

    }
  }

  /**
   * Helper function to get entity name from class.
   */
  // TODO: move this to common core jar
  private String getEntityName(T entity) {
    return ObjectUtils.isEmpty(entity) ? "Resource" : entity.getClass().getSimpleName().toLowerCase();
  }

  /**
   * @param entity Target entity
   * @param oldOwnerId Old owner id
   * @return ChangeOwner responses
   */
  private ChangeOwnerResponse<Long> mapEntityToChangeOwnerResponse(T entity, Long oldOwnerId) {
    ChangeOwnerResponse<Long> changeOwnerResponse = new ChangeOwnerResponse<>();
    changeOwnerResponse.setEntityId(entity.getId());
    changeOwnerResponse.setNewOwnerId(entity.getOwnerId());
    changeOwnerResponse.setOldOwnerId(oldOwnerId);
    return changeOwnerResponse;
  }

  /**
   * @param response Target entity
   * @return ChangeOwner responses
   */
  private ChangeOwnerMessage<Long> mapChangeOwnerResponseToMessage(ChangeOwnerResponse<Long> response, List<EntityType> childEntities) {
    ChangeOwnerMessage<Long> message = new ChangeOwnerMessage<>();
    String authContext = AuthenticationSerializer.serialize(SecurityUtil.getAuthentication());
    message.setTenantId(SecurityUtil.getTenantId());
    message.setEntityId(response.getEntityId());
    message.setNewOwnerId(response.getNewOwnerId());
    message.setOldOwnerId(response.getOldOwnerId());
    message.setChildEntities(childEntities);
    message.setAuthContext(authContext);
    return message;
  }


}

