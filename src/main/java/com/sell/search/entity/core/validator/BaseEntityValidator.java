package com.sell.search.entity.core.validator;

import com.sell.search.entity.core.exception.FieldValidationException;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

/**
 * Inteface for base entity validator
 * @param <T>
 */
public interface BaseEntityValidator<T> extends Validator {

  void validateEntity(T entity, Errors errors) throws FieldValidationException;
}
