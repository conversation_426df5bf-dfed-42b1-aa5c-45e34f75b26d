package com.sell.search.entity.core.feature;

import com.sell.search.core.domain.TenantOwnerAwareBaseEntity;
import com.sell.search.entity.core.dto.ChangeOwnerResponse;
import com.sell.search.entity.core.exception.OwnerDoesNotExist;
import com.sell.search.core.domain.EntityType;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * Created by hemants on 07/04/19. The interface needs to extends in ServiceInterface if change of own functionality is required.
 */
public interface ChangeOwnerFeature<T extends TenantOwnerAwareBaseEntity, ID extends Serializable> {

  ChangeOwnerResponse<ID> updateEntityOwner(Long ownerId, ID entityId, List<EntityType> childEntities) throws OwnerDoesNotExist;

  Collection<ChangeOwnerResponse<ID>> updateEntityOwner(ID ownerId, Collection<ID> entityIds, List<EntityType> childEntities)
      throws OwnerDoesNotExist;

}
