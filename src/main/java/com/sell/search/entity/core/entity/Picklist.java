package com.sell.search.entity.core.entity;

import com.sell.search.core.annotation.AccessPermission;
import com.sell.search.core.domain.TenantAwareBaseEntity;
import com.sell.search.entity.core.entity.PicklistValue;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;

/**
 * Created by shashanks3 on 26/4/19.
 */
@Setter
@Getter
@Where(clause = "deleted=false")
@AccessPermission("config")
public class Picklist extends TenantAwareBaseEntity {
  private String name;
  private String displayName;

  private boolean global;
  private boolean systemDefault;

  public Picklist() {
    //
  }

  public Picklist(String name, String displayName) {
    super();
    this.name = name;
    this.displayName = displayName;
  }

  private List<PicklistValue> values;
}
