package com.sell.search.entity.core.config;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.search.entity.core.converter.CustomFieldTypeConverter;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.CustomFieldAwareBaseEntity;
import com.sell.search.core.domain.EntityType;
import com.sell.search.entity.core.service.client.IEntityClient;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter;

/**
 * Created by hemants on 11/02/19.
 * This is the main transformer class which converts user entered custom values into correct type. Basically it checks
 * following things:
 * - If user supplied attributes are defined in tenant's custom defined attribute list or not.
 * - If type of user supplied attributes are in accordance with tenant's custom defined attribute's type
 */

@Slf4j
public class CustomMappingJackson2HttpMessageConverter extends AbstractJackson2HttpMessageConverter {

  @Autowired
  @Qualifier("entityClient")
  private IEntityClient entityClient;

  protected CustomMappingJackson2HttpMessageConverter(ObjectMapper objectMapper) {
    super(objectMapper, MediaType.APPLICATION_JSON);
  }


  /**
   * Convert custom attributes according to tenant's defined attribute list.
   *
   * @param type
   * @param contextClass
   * @param inputMessage
   * @return
   * @throws IOException
   */
  @Override
  public Object read(Type type, Class<?> contextClass, HttpInputMessage inputMessage)
      throws IOException {
    CustomFieldAwareBaseEntity obj = (CustomFieldAwareBaseEntity) super.read(type, contextClass, inputMessage);
    Map<String, Object> customFields = obj.getCustomFieldValues();
    // handle null condition
    if (customFields == null) {
      customFields = new HashMap<>();
    }
    List<Field> fieldList = entityClient.getFields(EntityType.LEAD);
    log.debug("list of custom fields are {}", fieldList);
    obj.setCustomFieldValues(CustomFieldTypeConverter.getTypeConvertedAttributeMap(fieldList, customFields));
    return obj;
  }


  @Override
  public boolean canRead(Type type, Class<?> contextClass, MediaType mediaType) {
    log.trace("custom can Read");
    return CustomFieldAwareBaseEntity.class.isAssignableFrom(getJavaType(type, contextClass).getRawClass());
  }


  @Override
  public boolean canRead(Class<?> clazz, MediaType mediaType) {
    log.trace("custom can Read");
    return super.canRead(clazz, mediaType);
  }

  @Override
  public boolean canWrite(Class<?> clazz, MediaType mediaType) {
    log.trace("custom can write");
    return super.canWrite(clazz, mediaType);
  }
}
