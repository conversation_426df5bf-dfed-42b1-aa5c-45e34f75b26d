package com.sell.search.entity.core.converter.field.rawdatatype;

import com.sell.search.entity.core.converter.field.rawdatatype.RawDataTypeConverter;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import java.util.Arrays;
import java.util.List;
import org.springframework.util.ObjectUtils;

/**
 * Created by hemants on 19/04/19. The string raw data type converter
 */
public class StringRawDataTypeConverter implements RawDataTypeConverter {

  private final List<FieldType> validStringConvertibleList =
      Arrays.asList(FieldType.TEXT_FIELD, FieldType.SINGLE_LINE_TEXT, FieldType.PARAGRAPH_TEXT, FieldType.RICH_TEXT);


  /**
   * Supports validStringConvertibleList data type
   *
   * @param definedDataType
   * @return
   */
  @Override
  public boolean canConvert(Field definedDataType) {
    return !ObjectUtils.isEmpty(definedDataType.getMultiValue()) && !definedDataType.getMultiValue()
        && (validStringConvertibleList.contains(definedDataType.getType()) || (definedDataType.getInternal() != null && definedDataType.getInternal()
            && (FieldType.PICK_LIST.equals(definedDataType.getType()) || FieldType.MULTI_PICKLIST.equals(definedDataType.getType()))));
  }

  /**
   * Convert to string
   *
   * @param rawObject
   * @param field
   * @return
   */
  @Override
  public Object convert(Object rawObject, Field field) {
    if (!(rawObject instanceof String)) {
      throwException(field.getName(), rawObject, field.getType().toString());
    }
    return rawObject;
  }

  private void throwException(String k, Object object, String converterName) {
    RawDatatypeExceptionHelper.throwException(k, object, converterName, "field.conversion.error.message");
  }
}
