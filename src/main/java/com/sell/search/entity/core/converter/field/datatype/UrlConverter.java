package com.sell.search.entity.core.converter.field.datatype;

import com.sell.search.entity.core.annotation.UrlField;
import com.sell.search.entity.core.converter.field.datatype.DataTypeConverter;
import com.sell.search.entity.core.converter.field.datatype.FieldContext;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;

public class UrlConverter implements DataTypeConverter {

  @Override
  public boolean canConvert(com.sell.search.entity.core.converter.field.datatype.FieldContext fieldContext) {
    return fieldContext.getField().getAnnotationsByType(UrlField.class).length > 0;
  }

  @Override
  public Field convert(FieldContext fieldContext) {
    return new Field().type(FieldType.URL);
  }
}
