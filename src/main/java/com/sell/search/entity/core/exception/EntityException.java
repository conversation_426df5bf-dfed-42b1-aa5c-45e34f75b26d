package com.sell.search.entity.core.exception;

import com.sell.search.core.domain.ErrorResource;
import com.sell.search.core.exception.BaseException;

/**
 * Created by hemants on 04/04/19.
 */
public class EntityException extends BaseException {
  private static final long serialVersionUID = 1L;

  public EntityException(ErrorResource errorResource) {
    super(errorResource);
  }
  public EntityException(ErrorResource errorResource, Object... args){
    super(errorResource, args);

  }
}
