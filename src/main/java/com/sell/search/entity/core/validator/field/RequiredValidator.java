package com.sell.search.entity.core.validator.field;

import com.sell.search.core.domain.ErrorResource;
import com.sell.search.core.domain.FieldErrorResource;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.exception.CoreEntityErrorCodes;
import com.sell.search.entity.core.exception.EntityException;
import com.sell.search.entity.core.validator.field.DataTypeValidator;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.Map;

/**
 * Created by hemants on 19/04/19.
 * The validator class for required validation, applicable for all field type
 */
public class RequiredValidator implements DataTypeValidator {


  /**
   * Validate the custom fields
   * @param customFields
   * @param allowedCustomFields
   */
  @Override
  public boolean validate(Map<String, Object> customFields, Map<String, Field> allowedCustomFields) {
    for (Map.Entry<String, Field> entry : allowedCustomFields.entrySet()) {
      String k = entry.getKey();
      Field v = entry.getValue();
      if (!ObjectUtils.isEmpty(v.getRequired()) && v.getRequired()
          && (!customFields.containsKey(k) || ObjectUtils.isEmpty(customFields.get(k)))) {
        throwException(k);
      }
    }
    return true;
  }

  /**
   * A common method to throw an exception
   * @param k
   * @return
   */
  private EntityException throwException(String k) {
    ErrorResource eR = new ErrorResource(CoreEntityErrorCodes.COMMON_VALIDATION_ERROR.getCode(),
        CoreEntityErrorCodes.COMMON_VALIDATION_ERROR.getMessage());
    eR.fieldErrors(Arrays.asList(new FieldErrorResource(k, "field.validation.constraints.NotNull.message")));
    throw new EntityException(eR);
  }
}
