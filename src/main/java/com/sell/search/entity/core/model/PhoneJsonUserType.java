package com.sell.search.entity.core.model;

import com.sell.search.entity.core.model.JsonType;
import com.sell.search.entity.core.model.PhoneNumber;
import java.util.Arrays;

/**
 * Created by hemants on 08/03/19.
 */
public class PhoneJsonUserType extends JsonType {

  @Override
  public Class returnedClass() {
    return PhoneNumber[].class;
  }

  @Override
  public boolean equals(Object obj1, Object obj2) {
    PhoneNumber[] phoneNumbersOfObject1 = (PhoneNumber[]) obj1;
    PhoneNumber[] phoneNumbersOfObject2 = (PhoneNumber[]) obj2;
    return Arrays.equals(phoneNumbersOfObject1, phoneNumbersOfObject2);
  }
}
