package com.sell.search.entity.core.converter.field.rawdatatype;

import com.sell.search.entity.core.converter.field.rawdatatype.RawDataTypeConverter;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import java.sql.Timestamp;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

/**
 * Created by hemants on 24/04/19.
 * The DateTime raw data type converter
 */
@Slf4j
public class DateTimeRawDataTypeConverter implements RawDataTypeConverter {

  /**
   * Only for DATETIME_PICKER
   * @param definedDataType
   * @return
   */
  @Override
  public boolean canConvert(Field definedDataType) {
    return !ObjectUtils.isEmpty(definedDataType.getMultiValue()) && !definedDataType.getMultiValue() && FieldType.DATETIME_PICKER.equals(definedDataType.getType());
  }

  /**
   * try to convert into OffsetDateTime else raise exception
   *
   * @param rawObject
   * @param field
   * @return
   */
  @Override
  public Object convert(Object rawObject, Field field) {
    try {
      return OffsetDateTime.parse(rawObject.toString());
    } catch (Exception e) {
      log.debug("Input date is not convertible to OffsetDateTime");
    }
    try{
      log.debug("Trying to convert using epoch");
      //TODO: update this once we start storing tenant zone information
      return new Timestamp(Long.valueOf(rawObject.toString())).toLocalDateTime().atOffset(ZoneOffset.UTC);
    }catch(Exception e){
      throwException(field.getName(), rawObject, field.getType().toString());
    }
    return rawObject;
  }

  private void throwException(String k, Object object, String converterName) {
    RawDatatypeExceptionHelper.throwException(k, object, converterName, "field.conversion.error.message" );
  }

}
