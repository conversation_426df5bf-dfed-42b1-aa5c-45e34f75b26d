package com.sell.search.entity.core.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by hemants on 08/03/19.
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class PhoneNumber implements Serializable {
  com.sell.search.entity.core.model.PhoneType type;
  String code;
  String value;
  String dialCode;
  boolean isPrimary;

  public PhoneNumber dialCode(String dialCode) {
    this.dialCode = dialCode;
    return this;
  }

  public PhoneNumber code(String code) {
    this.code = code;
    return this;
  }

  public PhoneNumber type(PhoneType type) {
    this.type = type;
    return this;
  }

  public PhoneNumber value(String value) {
    this.value = value;
    return this;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((code == null) ? 0 : code.hashCode());
    result = prime * result + ((value == null) ? 0 : value.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj)
      return true;
    if (obj == null)
      return false;
    if (getClass() != obj.getClass())
      return false;
    PhoneNumber other = (PhoneNumber) obj;
    if (code == null) {
      if (other.code != null)
        return false;
    } else if (!code.equals(other.code))
      return false;
    if (value == null) {
      if (other.value != null)
        return false;
    } else if (!value.equals(other.value))
      return false;

    if (this.getType() == null) {
      if (other.getType() != null)
        return false;
    } else if (!this.getType().equals(other.getType()))
      return false;

    return (this.isPrimary != Boolean.TRUE || other.isPrimary != Boolean.FALSE)
        && (this.isPrimary != Boolean.FALSE || other.isPrimary != Boolean.TRUE);
  }

  public PhoneNumber maskedValue(String value) {
    this.value = "****".concat(value.substring(value.length() - 3));
    return this;
  }
}
