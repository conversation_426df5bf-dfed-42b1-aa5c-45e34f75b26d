package com.sell.search.entity.core.converter.field.factory;

import static com.sell.search.entity.core.converter.field.datatype.ConverterUtil.getAnnotationFromFieldGetter;

import com.sell.search.core.annotation.FieldAttribute;
import com.sell.search.core.repository.BaseJpaRepository;
import com.sell.search.entity.core.annotation.ExtendedFields;
import com.sell.search.entity.core.annotation.Internal;
import com.sell.search.entity.core.annotation.SkipIdNameResolution;
import com.sell.search.entity.core.converter.field.datatype.ArrayConverter;
import com.sell.search.entity.core.converter.field.datatype.DataTypeConverter;
import com.sell.search.entity.core.converter.field.datatype.DateConverter;
import com.sell.search.entity.core.converter.field.datatype.EmailConverter;
import com.sell.search.entity.core.converter.field.datatype.EntityLookupConverter;
import com.sell.search.entity.core.converter.field.datatype.EnumConverter;
import com.sell.search.entity.core.converter.field.datatype.FieldContext;
import com.sell.search.entity.core.converter.field.datatype.LookupConverter;
import com.sell.search.entity.core.converter.field.datatype.NumberConverter;
import com.sell.search.entity.core.converter.field.datatype.PhoneConverter;
import com.sell.search.entity.core.converter.field.datatype.PicklistConverter;
import com.sell.search.entity.core.converter.field.datatype.PipelineConverter;
import com.sell.search.entity.core.converter.field.datatype.TextConverter;
import com.sell.search.entity.core.converter.field.datatype.ToggleConverter;
import com.sell.search.entity.core.converter.field.datatype.UrlConverter;
import com.sell.search.entity.core.converter.field.exception.FieldConvertException;
import com.sell.search.entity.core.converter.field.factory.FieldConverterFactory;
import com.sell.search.entity.core.converter.field.strategy.DisplayNameNamingStrategy;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.entity.Picklist;
import java.lang.annotation.Annotation;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.util.ObjectUtils;
/**
 * Created by hemants on 16/04/19. The java class to our custom field factory class which contains
 * all converter
 */
public class ClassDataTypeConverterFactory implements FieldConverterFactory {
  private final List<DataTypeConverter> dataTypeConverterList = new ArrayList<>();
  private final DisplayNameNamingStrategy displayNameNamingStrategy;

  /**
   * Initialized all converter which is currently supported in our system
   *
   * @param namingStrategy
   */
  public ClassDataTypeConverterFactory(
      DisplayNameNamingStrategy displayNameNamingStrategy,
      BaseJpaRepository<Picklist, Long> picklistRepository) {
    dataTypeConverterList.add(new ArrayConverter(this));
    dataTypeConverterList.add(new NumberConverter());
    dataTypeConverterList.add(new DateConverter());
    dataTypeConverterList.add(new EmailConverter());
    dataTypeConverterList.add(new PhoneConverter());
    dataTypeConverterList.add(new ToggleConverter());
    dataTypeConverterList.add(new PicklistConverter(picklistRepository));
    dataTypeConverterList.add(new EnumConverter());
    dataTypeConverterList.add(new LookupConverter());
    dataTypeConverterList.add(new EntityLookupConverter());
    dataTypeConverterList.add(new UrlConverter());
    dataTypeConverterList.add(new TextConverter());
    dataTypeConverterList.add(new PipelineConverter());
    this.displayNameNamingStrategy = displayNameNamingStrategy;
  }

  /**
   * First find out all fields declared in java class, then iteratively convert it into custom
   * fields.
   *
   * @param clazz
   * @return
   */
  public List<Field> convert(Class clazz, Consumer<Field> callbackAfterFieldConverted) {
    java.lang.reflect.Field[] fields = FieldUtils.getAllFields(clazz);
    String[] superFields = getConvertibleSuperFields(clazz);
    return Stream.of(fields)
        .filter(
            field ->
                field.getDeclaringClass().equals(clazz)
                    || Stream.of(superFields).anyMatch(sf -> sf.equals(field.getName())))
        .map(f -> getFieldDataType(f, callbackAfterFieldConverted, clazz))
        .collect(Collectors.toList());
  }

  private String[] getConvertibleSuperFields(Class clazz) {
    Annotation annotation = clazz.getAnnotation(ExtendedFields.class);
    try {
      return null != annotation
          ? (String[])
              annotation
                  .annotationType()
                  .getDeclaredMethod("value", null)
                  .invoke(annotation, (Object[]) null)
          : new String[0];
    } catch (Exception e) {
      return new String[0];
    }
  }

  /**
   * Convert java's field into our own custom field structure
   *
   * @param field
   * @return
   */
  Field getFieldDataType(
      java.lang.reflect.Field field, Consumer<Field> callbackAfterFieldConverted, Class fieldType) {
    Field fieldDataType =
        convertToFieldDataType(new FieldContext(field, field.getType(), fieldType));
    setName(field, fieldDataType);
    if (!ObjectUtils.isEmpty(callbackAfterFieldConverted)) {
      callbackAfterFieldConverted.accept(fieldDataType);
    }
    return fieldDataType;
  }

  /**
   * Set field display name based on naming strategy
   *
   * @param field
   * @param fieldDataType
   */
  private void setName(java.lang.reflect.Field field, Field fieldDataType) {
    fieldDataType.setName(field.getName());
    fieldDataType.setDisplayName(getFieldDisplayName(field));
  }

  private String getFieldDisplayName(java.lang.reflect.Field field) {
    if (null != field.getAnnotation(FieldAttribute.class)
        && !ObjectUtils.isEmpty(field.getAnnotation(FieldAttribute.class).displayName())) {
      return field.getAnnotation(FieldAttribute.class).displayName();
    } else {
      return displayNameNamingStrategy.getDisplayName(field.getName());
    }
  }

  /**
   * Call each converter, check if it is able to convert or not, if able to convert then call it's
   * convert method
   *
   * @param
   * @return
   */
  @Override
  public Field convertToFieldDataType(FieldContext fieldContext) {
    for (DataTypeConverter converter : dataTypeConverterList) {
      if (converter.canConvert(fieldContext)) {
        Field resultField = converter.convert(fieldContext);
        SkipIdNameResolution[] skipIdNameResolutionAnnotations = fieldContext.getField().getAnnotationsByType(SkipIdNameResolution.class);
        if(skipIdNameResolutionAnnotations.length > 0){
          resultField.setSkipIdNameResolution(true);
        }
        makeInternalIfRequired(resultField, fieldContext);
        return resultField;
      }
    }

    // TODO: put it into message.property
    throw new FieldConvertException(
        "No converter found to convert data type : "
            + fieldContext.getField().getType().getSimpleName());
  }

  private void makeInternalIfRequired(Field resultField, FieldContext fieldContext) {
    resultField.setInternal(
        fieldContext.getField().isAnnotationPresent(Internal.class)
            || getAnnotationFromFieldGetter(
                        fieldContext.getField(), fieldContext.getFieldClass(), Internal.class)
                    .length
                > 0);
  }
}
