package com.sell.search.entity.core.converter.field.rawdatatype;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.search.entity.core.converter.field.rawdatatype.RawDataTypeConverter;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.model.FieldType;
import java.math.BigDecimal;
import java.math.BigInteger;
import org.springframework.util.ObjectUtils;

public class NumberRawDataTypeConverter implements RawDataTypeConverter {

  /**
   * Only for Number
   *
   * @param definedDataType
   * @return
   */
  @Override
  public boolean canConvert(Field definedDataType) {
    return !ObjectUtils.isEmpty(definedDataType.getMultiValue())
        && !definedDataType.getMultiValue()
        && (FieldType.NUMBER.equals(definedDataType.getType())
            || FieldType.LOOK_UP.equals(definedDataType.getType())
            || FieldType.MULTI_PICKLIST.equals(definedDataType.getType())
            || ((definedDataType.getInternal() == null || !definedDataType.getInternal())
                && FieldType.PICK_LIST.equals(definedDataType.getType())));
  }

  /**
   * try to convert into {@link BigDecimal} else raise exception
   *
   * @param rawObject
   * @param definedDataType
   * @return
   */
  @Override
  public Object convert(Object rawObject, Field definedDataType) {
    try {
      ObjectMapper objectMapper = new ObjectMapper();
      Class internalType = definedDataType.getInternalType();
      if (internalType == null || internalType == BigDecimal.class) {
        return getBigDecimal(rawObject);
      }
      Object object = objectMapper.convertValue(rawObject, internalType);
      return object;
    } catch (Exception e) {
      throwException(definedDataType.getName(), rawObject, "Number");
    }
    return null; // does not matter as it will be either exception or bigdecimal
  }

  /**
   * Try various possibilities
   *
   * @param value
   * @return
   */
  private BigDecimal getBigDecimal(Object value) {
    BigDecimal ret = null;
    if (value != null) {
      if (value instanceof BigDecimal) {
        ret = (BigDecimal) value;
      } else if (value instanceof BigInteger) {
        ret = new BigDecimal((BigInteger) value);
      } else if (value instanceof Number) {
        ret = new BigDecimal(((Number) value).toString());
      } else {
        throw new ClassCastException("Not able to convert to Number");
      }
    }
    return ret;
  }

  private void throwException(String k, Object object, String converterName) {
    RawDatatypeExceptionHelper.throwException(
        k, object, converterName, "field.conversion.error.message");
  }
}
