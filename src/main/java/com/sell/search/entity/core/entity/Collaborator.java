package com.sell.search.entity.core.entity;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.MappedSuperclass;
import com.sell.search.core.domain.TenantAwareBaseEntity;
import com.sell.search.core.domain.EntityType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@MappedSuperclass
public class Collaborator extends TenantAwareBaseEntity {

  @Enumerated(EnumType.STRING)
  private EntityType entityType;
  private Long entityId;

}
