package com.sell.search.entity.core.converter.field.datatype;

import com.sell.search.core.repository.BaseJpaRepository;
import com.sell.search.core.utils.SecurityUtil;
import com.sell.search.entity.core.annotation.PicklistField;
import com.sell.search.entity.core.converter.field.datatype.DataTypeConverter;
import com.sell.search.entity.core.converter.field.datatype.FieldContext;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.entity.Picklist;
import com.sell.search.entity.core.entity.PicklistValue;
import com.sell.search.entity.core.model.FieldType;
import java.lang.annotation.Annotation;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;
import org.springframework.util.ObjectUtils;

/**
 * Created by Shashank .
 */
public class PicklistConverter implements DataTypeConverter {

  private final BaseJpaRepository<Picklist, Long> picklistRepository;

  public PicklistConverter(BaseJpaRepository<Picklist, Long> picklistRepository) {
    super();
    this.picklistRepository = picklistRepository;
  }

  @Override
  public boolean canConvert(com.sell.search.entity.core.converter.field.datatype.FieldContext fieldContext) {
    return fieldContext.getField().getAnnotationsByType(PicklistField.class).length > 0;
  }

  @Override
  public Field convert(FieldContext fieldContext) {
    Annotation[] annotations = fieldContext.getField().getAnnotationsByType(PicklistField.class);
    Field fieldDataType = new Field().type(FieldType.PICK_LIST);
    fieldDataType.setPicklist(getPicklist(annotations[0], fieldDataType));
    return fieldDataType;
  }

  private Picklist getPicklist(Annotation annotation, Field fieldDef) {
    PicklistField annotationObj = (PicklistField) annotation;
    if (!ObjectUtils.isEmpty(annotationObj.name()) && (annotationObj.name().equals("INDUSTRY") || annotationObj.name().equals("BUSINESS_TYPE"))) {
      Picklist standardPicklist = picklistRepository.findOne((Root<Picklist> root, CriteriaQuery<?> query, CriteriaBuilder cb)
          -> cb.and(cb.equal(root.get("tenantId"), SecurityUtil.getTenantId()), cb.equal(root.get("systemDefault"), true),
          cb.equal(root.get("name"), annotationObj.name())));

      if (standardPicklist != null) {
        return standardPicklist;
      }
    }
    if (!ObjectUtils.isEmpty(annotationObj.name()) && ObjectUtils.isEmpty(annotationObj.value())) {
      Picklist standardPicklist = picklistRepository.findOne((Root<Picklist> root, CriteriaQuery<?> query, CriteriaBuilder cb)
          -> cb.and(cb.equal(root.get("tenantId"), 0), cb.equal(root.get("systemDefault"), true), cb.equal(root.get("name"), annotationObj.name())));

      if (standardPicklist != null) {
        return standardPicklist;
      }
    }

    Picklist picklist = new Picklist();
    picklist.setName(ObjectUtils.isEmpty(annotationObj.name()) ? annotationObj.name() : fieldDef.getName());

    List<PicklistValue> picklistValues = new ArrayList<>();
    String[] values = annotationObj.value();
    for (String value : values) {
      PicklistValue pickListValue = new PicklistValue();
      pickListValue.setDisplayName(value);
      pickListValue.setName(value.trim().replaceAll("\\s", "_").toUpperCase());
      pickListValue.setPicklist(picklist);
      picklistValues.add(pickListValue);
    }

    picklist.setValues(picklistValues);
    return picklist;
  }
}
