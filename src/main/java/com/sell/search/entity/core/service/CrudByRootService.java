package com.sell.search.entity.core.service;

import com.sell.search.core.domain.BaseEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.Serializable;

/**
 * Created by hemants on 07/04/19.
 */
@Deprecated
public interface CrudByRootService <R, T extends BaseEntity, ID extends Serializable>  {

  T create(R rootEntity, T entity);

  T get(R rootEntity, ID id);

  Page<T> getList(R rootEntity, Pageable pageable);

  T update(R rootEntity, T entity);

  T delete(R rootEntity, T entity);



}
