package com.sell.search.global.search;

import lombok.Getter;

@Getter
public enum EntityType {
  LEAD(new Lead(), "lead", new LeadResponse()),
  CONTACT(new Contact(), "contact", new ContactResponse()),
  DEAL(new Deal(), "deal",new DealResponse()),
  COMPANY(new Company(), "company", new CompanyResponse()),
  TASK(new Task(), "task", new TaskResponse());

  private final GlobalSearchBaseEntity baseEntity;
  private final String permissionName;
  private final GlobalBaseEntityResponse globalBaseEntityResponse;

  EntityType(GlobalSearchBaseEntity baseEntity, String permissionName, GlobalBaseEntityResponse globalBaseEntityResponse) {
    this.baseEntity = baseEntity;
    this.permissionName = permissionName;
    this.globalBaseEntityResponse = globalBaseEntityResponse;
  }

  public static EntityType toEntity(String s) {
    return EntityType.valueOf(s);
  }
}
