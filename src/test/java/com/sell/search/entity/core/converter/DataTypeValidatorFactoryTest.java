package com.sell.search.entity.core.converter;

import com.sell.search.entity.core.converter.field.factory.RawDataTypeConverterFactory;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.entity.core.entity.Lead;
import com.sell.search.entity.core.exception.EntityException;
import com.sell.search.entity.core.model.FieldType;
import com.sell.search.entity.core.validator.field.DataTypeValidatorFactory;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.EntityManager;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

/**
 * Created by hemants on 19/04/19.
 */
@RunWith(MockitoJUnitRunner.class)
public class DataTypeValidatorFactoryTest {


  @Mock
  private EntityManager em;

  private DataTypeValidatorFactory dataTypeValidatorFactory = new DataTypeValidatorFactory<Lead, Long>(Lead.class, em);
  private RawDataTypeConverterFactory rawDataTypeConverterFactory = new RawDataTypeConverterFactory();

  @Test(expected = EntityException.class)
  public void shouldNotAbleToAllowMoreThanAllowedLength() {
    final String fieldName = "thought";
    Map<String, Object> customFields = new HashMap<>();
    customFields.put(fieldName, "This new trend");
    Map<String, Field> allowedCustomFields = new HashMap<>();
    Field thoughtField = new FieldBuilder(FieldType.TEXT_FIELD)
        .multiValue(false)
        .name(fieldName)
        .length(3)
        .build();
    allowedCustomFields.put(fieldName, thoughtField);
    rawDataTypeConverterFactory.convert(customFields, allowedCustomFields);
    dataTypeValidatorFactory.validate(customFields, allowedCustomFields);
  }

  @Test
  public void shouldAbleToAllowLengthWithInLimit() {
    final String fieldName = "thought";
    Map<String, Object> customFields = new HashMap<>();
    customFields.put(fieldName, "This new trend");
    Map<String, Field> allowedCustomFields = new HashMap<>();
    Field thoughtField = new FieldBuilder(FieldType.TEXT_FIELD)
        .multiValue(false)
        .name(fieldName)
        .length(30)
        .build();
    allowedCustomFields.put(fieldName, thoughtField);
    rawDataTypeConverterFactory.convert(customFields, allowedCustomFields);
    dataTypeValidatorFactory.validate(customFields, allowedCustomFields);
    Assert.assertTrue(14 == ((String) customFields.get(fieldName)).length());
  }

  @Test
  public void shouldAbleToAllowLengthWithInLimitForMultiValue() {
    final String fieldName = "thought";
    Map<String, Object> customFields = new HashMap<>();
    customFields.put(fieldName, Arrays.asList("This new trend" , "This is another trend"));
    Map<String, Field> allowedCustomFields = new HashMap<>();
    Field thoughtField = new FieldBuilder(FieldType.TEXT_FIELD)
        .multiValue(true)
        .name(fieldName)
        .length(30)
        .build();
    allowedCustomFields.put(fieldName, thoughtField);
    rawDataTypeConverterFactory.convert(customFields, allowedCustomFields);
    dataTypeValidatorFactory.validate(customFields, allowedCustomFields);
    Assert.assertTrue(2 == ((List<String>) customFields.get(fieldName)).size());
  }


  @Test(expected = EntityException.class)
  public void shouldNotAllowWithoutRequiredField() {
    final String fieldName = "thought";
    Map<String, Object> customFields = new HashMap<>();
    customFields.put(fieldName, "This new trend,This is another trend");
    Map<String, Field> allowedCustomFields = new HashMap<>();

    Field thoughtField = new FieldBuilder(FieldType.TEXT_FIELD)
        .multiValue(false)
        .name(fieldName)
        .length(30)
        .build();

    Field requiredField = new FieldBuilder(FieldType.NUMBER)
        .multiValue(false)
        .name("test")
        .required(true)
        .length(30)
        .build();

    allowedCustomFields.put(fieldName, thoughtField);
    allowedCustomFields.put("test", requiredField);
    rawDataTypeConverterFactory.convert(customFields, allowedCustomFields);
    dataTypeValidatorFactory.validate(customFields, allowedCustomFields);
    Assert.assertTrue(2 == ((List<String>) customFields.get(fieldName)).size());
  }

  @Test(expected = Exception.class)
  public void shouldNotAbleRunIfCustomFieldIsNull() {
    final String fieldName = "thought";
    Map<String, Object> customFields = null;
    Map<String, Field> allowedCustomFields = new HashMap<>();
    Field thoughtField = new FieldBuilder(FieldType.TEXT_FIELD)
        .multiValue(false)
        .name(fieldName)
        .length(30)
        .build();
    allowedCustomFields.put(fieldName, thoughtField);
    rawDataTypeConverterFactory.convert(customFields, allowedCustomFields);
    dataTypeValidatorFactory.validate(customFields, allowedCustomFields);
  }


  @Test
  public void shouldAbleRunIfCustomFieldIsEmpty() {
    final String fieldName = "thought";
    Map<String, Object> customFields = new HashMap<>();
    Map<String, Field> allowedCustomFields = new HashMap<>();
    Field thoughtField = new FieldBuilder(FieldType.TEXT_FIELD)
        .multiValue(false)
        .name(fieldName)
        .length(30)
        .build();
    allowedCustomFields.put(fieldName, thoughtField);
    rawDataTypeConverterFactory.convert(customFields, allowedCustomFields);
    dataTypeValidatorFactory.validate(customFields, allowedCustomFields);
  }

  @Test(expected = EntityException.class)
  public void shouldNotAbleRunIfGreaterThanNotInRange() {
    final String fieldName = "driving_age";
    Map<String, Object> customFields = new HashMap<>();

    //Configured fields
    Map<String, Field> allowedCustomFields = new HashMap<>();
    Field thoughtField = new FieldBuilder(FieldType.NUMBER)
        .multiValue(false)
        .name(fieldName)
        .greaterThan(BigDecimal.valueOf(18).toString())
        .lessThan(BigDecimal.valueOf(80).toString())
        .build();
    allowedCustomFields.put(fieldName, thoughtField);

    customFields.put(fieldName, "90");
    rawDataTypeConverterFactory.convert(customFields, allowedCustomFields);
    dataTypeValidatorFactory.validate(customFields, allowedCustomFields);
  }

  @Test
  public void shouldAbleRunIfGreaterThanIsNotProvided() {
    final String fieldName = "driving_age";
    Map<String, Object> customFields = new HashMap<>();

    //Configured fields
    Map<String, Field> allowedCustomFields = new HashMap<>();
    Field thoughtField = new FieldBuilder(FieldType.NUMBER)
        .multiValue(false)
        .name(fieldName)
        .lessThan(BigDecimal.valueOf(80).toString())
        .internalType(BigDecimal.class)
        .build();
    allowedCustomFields.put(fieldName, thoughtField);

    customFields.put(fieldName, 70);
    rawDataTypeConverterFactory.convert(customFields, allowedCustomFields);
    dataTypeValidatorFactory.validate(customFields, allowedCustomFields);
  }


  @Test
  public void shouldAbleRunIfLessThanIsNotProvided() {
    final String fieldName = "driving_age";
    Map<String, Object> customFields = new HashMap<>();

    //Configured fields
    Map<String, Field> allowedCustomFields = new HashMap<>();
    Field thoughtField = new FieldBuilder(FieldType.NUMBER)
        .multiValue(false)
        .name(fieldName)
        .internalType(BigDecimal.class)
        .greaterThan(BigDecimal.valueOf(18).toString())
        .build();
    allowedCustomFields.put(fieldName, thoughtField);

    customFields.put(fieldName, 70);
    rawDataTypeConverterFactory.convert(customFields, allowedCustomFields);
    dataTypeValidatorFactory.validate(customFields, allowedCustomFields);
  }

  @Test(expected = EntityException.class)
  public void shouldNotAbleRunIfGreaterThanNotInRangeMultiValue() {
    final String fieldName = "driving_age";
    Map<String, Object> customFields = new HashMap<>();

    //Configured fields
    Map<String, Field> allowedCustomFields = new HashMap<>();
    Field thoughtField = new FieldBuilder(FieldType.NUMBER)
        .multiValue(true)
        .name(fieldName)
        .greaterThan(BigDecimal.valueOf(18).toString())
        .lessThan(BigDecimal.valueOf(80).toString())
        .build();
    allowedCustomFields.put(fieldName, thoughtField);

    customFields.put(fieldName, "69,50,90,20");
    rawDataTypeConverterFactory.convert(customFields, allowedCustomFields);
    dataTypeValidatorFactory.validate(customFields, allowedCustomFields);
  }


  @Test(expected = EntityException.class)
  public void shouldNotAbleRunIfLessThanNotInRange() {
    final String fieldName = "driving_age";
    Map<String, Object> customFields = new HashMap<>();

    //Configured fields
    Map<String, Field> allowedCustomFields = new HashMap<>();
    Field thoughtField = new FieldBuilder(FieldType.NUMBER)
        .multiValue(false)
        .name(fieldName)
        .greaterThan(BigDecimal.valueOf(18).toString())
        .lessThan(BigDecimal.valueOf(80).toString())
        .build();
    allowedCustomFields.put(fieldName, thoughtField);

    customFields.put(fieldName, 17);
    rawDataTypeConverterFactory.convert(customFields, allowedCustomFields);
    dataTypeValidatorFactory.validate(customFields, allowedCustomFields);
  }

  @Test(expected = EntityException.class)
  public void shouldNotAbleRunIfLessThanNotInRangeMultiValue() {
    final String fieldName = "driving_age";
    Map<String, Object> customFields = new HashMap<>();

    //Configured fields
    Map<String, Field> allowedCustomFields = new HashMap<>();
    Field thoughtField = new FieldBuilder(FieldType.NUMBER)
        .multiValue(true)
        .name(fieldName)
        .greaterThan(BigDecimal.valueOf(18).toString())
        .lessThan(BigDecimal.valueOf(80).toString())
        .build();
    allowedCustomFields.put(fieldName, thoughtField);

    customFields.put(fieldName, "59,30,20,17,10");
    rawDataTypeConverterFactory.convert(customFields, allowedCustomFields);
    dataTypeValidatorFactory.validate(customFields, allowedCustomFields);
  }


  @Test
  public void shouldAbleRunIfLessThanInRangeMultiValue() {
    final String fieldName = "driving_age";
    Map<String, Object> customFields = new HashMap<>();

    //Configured fields
    Map<String, Field> allowedCustomFields = new HashMap<>();
    Field thoughtField = new FieldBuilder(FieldType.NUMBER)
        .multiValue(true)
        .name(fieldName)
        .internalType(BigDecimal.class)
        .greaterThan(BigDecimal.valueOf(18).toString())
        .lessThan(BigDecimal.valueOf(80).toString())
        .build();
    allowedCustomFields.put(fieldName, thoughtField);

    customFields.put(fieldName, Arrays.asList(59));
    rawDataTypeConverterFactory.convert(customFields, allowedCustomFields);
    dataTypeValidatorFactory.validate(customFields, allowedCustomFields);
  }


  @Test
  public void shouldAbleRunIfLessThanInRangeDate() {
    final String fieldName = "joining_date";

    Map<String, Object> customFields = new HashMap<>();
    customFields.put(fieldName, "2019-04-17T09:28:19.000Z");

    //Configured fields
    Map<String, Field> allowedCustomFields = new HashMap<>();
    Field thoughtField = new FieldBuilder(FieldType.DATE_PICKER)
        .multiValue(false)
        .name(fieldName)
        .greaterThan("2019-04-16")
        .lessThan("2019-04-18")
        .build();
    allowedCustomFields.put(fieldName, thoughtField);
    rawDataTypeConverterFactory.convert(customFields, allowedCustomFields);
    dataTypeValidatorFactory.validate(customFields, allowedCustomFields);
  }

  @Test
  public void shouldAbleRunIfLessThanIsNotGiven() {
    final String fieldName = "joining_date";

    Map<String, Object> customFields = new HashMap<>();
    customFields.put(fieldName, "2019-04-17T09:28:19.000Z");

    //Configured fields
    Map<String, Field> allowedCustomFields = new HashMap<>();
    Field thoughtField = new FieldBuilder(FieldType.DATE_PICKER)
        .multiValue(false)
        .name(fieldName)
        .greaterThan("2019-04-16")
        .build();
    allowedCustomFields.put(fieldName, thoughtField);
    rawDataTypeConverterFactory.convert(customFields, allowedCustomFields);
    dataTypeValidatorFactory.validate(customFields, allowedCustomFields);
  }


  @Test(expected = EntityException.class)
  public void shouldNotAbleRunIfLessThanNotInRangeDate() {
    final String fieldName = "joining_date";


    Map<String, Object> customFields =  new HashMap<>();
    customFields.put(fieldName, "2019-04-19");

    //Configured fields
    Map<String, Field> allowedCustomFields = new HashMap<>();
    Field thoughtField = new FieldBuilder(FieldType.DATE_PICKER)
        .multiValue(false)
        .name(fieldName)
        .greaterThan("2019-04-16")
        .lessThan("2019-04-18")
        .build();
    allowedCustomFields.put(fieldName, thoughtField);
    rawDataTypeConverterFactory.convert(customFields, allowedCustomFields);
    dataTypeValidatorFactory.validate(customFields, allowedCustomFields);
  }


  @Test
  public void shouldAbleRunIfLessThanInRangeTime() {
    final String fieldName = "meeting_time";

    Map<String, Object> customFields = new HashMap<>();
    customFields.put(fieldName, "2019-09-20T06:32:49+05:30");

    //Configured fields
    Map<String, Field> allowedCustomFields = new HashMap<>();
    Field thoughtField = new FieldBuilder(FieldType.TIME_PICKER)
        .multiValue(false)
        .name(fieldName)
        .greaterThan("05:32:49+05:30")
        .lessThan("07:32:49+05:30")
        .build();
    allowedCustomFields.put(fieldName, thoughtField);
    rawDataTypeConverterFactory.convert(customFields, allowedCustomFields);
    dataTypeValidatorFactory.validate(customFields, allowedCustomFields);
  }

  @Test
  public void shouldAbleRunIfGreaterThanIsNotGiven() {
    final String fieldName = "meeting_time";

    Map<String, Object> customFields = new HashMap<>();
    customFields.put(fieldName, "2019-09-20T06:32:49+05:30");

    //Configured fields
    Map<String, Field> allowedCustomFields = new HashMap<>();
    Field thoughtField = new FieldBuilder(FieldType.TIME_PICKER)
        .multiValue(false)
        .name(fieldName)
        .lessThan("07:32:49+05:30")
        .build();
    allowedCustomFields.put(fieldName, thoughtField);
    rawDataTypeConverterFactory.convert(customFields, allowedCustomFields);
    dataTypeValidatorFactory.validate(customFields, allowedCustomFields);
  }

  @Test(expected = EntityException.class)
  public void shouldNotAbleRunIfLessThanNotInRangeTime() {
    final String fieldName = "meeting_time";

    Map<String, Object> customFields = new HashMap<>();
    customFields.put(fieldName, "08:32:49+05:30");

    //Configured fields
    Map<String, Field> allowedCustomFields = new HashMap<>();
    Field thoughtField = new FieldBuilder(FieldType.TIME_PICKER)
        .multiValue(false)
        .name(fieldName)
        .greaterThan("05:32:49+05:30")
        .lessThan("07:32:49+05:30")
        .build();
    allowedCustomFields.put(fieldName, thoughtField);
    rawDataTypeConverterFactory.convert(customFields, allowedCustomFields);
    dataTypeValidatorFactory.validate(customFields, allowedCustomFields);
  }
}
