package com.sell.search.entity.core.service;

import static java.util.Collections.emptySet;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;

import com.sell.search.core.domain.CoreAuthentication;
import com.sell.search.entity.core.config.RestHighLevelClientMock;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.http.entity.ContentType;
import org.apache.http.nio.entity.NStringEntity;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClient;
import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mockito;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
public class DefaultIdNameSearchServiceTest {

  private DefaultIdNameSearchService defaultIdNameSearchService;
  private RestClient newRestClient;
  private RestHighLevelClientMock newRestHighLevelClient;

  @Captor
  ArgumentCaptor<Request> newRequestArgumentCaptor = ArgumentCaptor.forClass(Request.class);

  @Before
  public void setup() {
    newRestClient = mock(RestClient.class);
    newRestHighLevelClient = new RestHighLevelClientMock(newRestClient);
    defaultIdNameSearchService = new DefaultIdNameSearchService(newRestHighLevelClient);
  }

  @Test
  public void givenForTypeAndIdList_tryToGetIdName_shouldCreateValidQuery() throws IOException, JSONException {
    //given
    Map<String, Set> forTypeIds = new HashMap<>();
    Set<Long> ids = new HashSet<>();
    ids.add(1L);
    ids.add(10L);
    String user = "USER";
    forTypeIds.put(user, ids);

    ids = new HashSet<>();
    ids.add(2L);
    ids.add(20L);
    String pickList = "PICK_LIST";
    forTypeIds.put(pickList, ids);
    Response response = mock(Response.class);
    given(newRestClient.performRequest(any(Request.class))).willReturn(response);

    given(response.getEntity())
        .willReturn(new NStringEntity(getFileContent("queries/es/response/id-name-query-for-userAndPicklist-response.json"), ContentType.APPLICATION_JSON));

    SecurityContextHolder.getContext()
        .setAuthentication(new CoreAuthentication("alfred", "11", emptySet(), ""));
    //when
    Map<String, Map<String, Object>> search = defaultIdNameSearchService.search(forTypeIds);
    //then
    assertThat(search.size()).isEqualTo(2);
    assertThat(search.get(user).get("1")).isEqualTo("Tony");
    assertThat(search.get(user).get("10")).isEqualTo("Stark");
    assertThat(search.get(pickList).get("2")).isEqualTo("INDIA");
    assertThat(search.get(pickList).get("20")).isEqualTo("USA");

    Mockito.verify(newRestClient, times(1)).performRequest(newRequestArgumentCaptor.capture());
    Request request = newRequestArgumentCaptor.getValue();
    assertThat(request.getEndpoint()).isEqualTo("/11-id-name/_search");
    String query = getQuery(request);
    JSONAssert.assertEquals(getFileContent("queries/es/request/id-name-query-for-userAndPicklist-request.json"), query, sizeMatcher(4));
  }

  @Test
  public void givenForTypeAndIdList_tryToGetIdNameForCompany_shouldCreateValidQuery() throws IOException, JSONException {
    //given
    Map<String, Set> forTypeIds = new HashMap<>();
    Set<Long> ids = new HashSet<>();
    ids.add(1L);
    ids.add(10L);
    String user = "USER";
    forTypeIds.put(user, ids);

    ids = new HashSet<>();
    ids.add(2L);
    ids.add(20L);
    String company = "COMPANY";
    forTypeIds.put(company, ids);
    Response userResponse = mock(Response.class);
    Response companyResponse = mock(Response.class);

    given(newRestClient.performRequest(any(Request.class))).willAnswer(invocation -> {
      Object[] arguments = invocation.getArguments();
      Request req = (Request) arguments[0];
      if (req.getEndpoint().equalsIgnoreCase("/11-id-name/_search")) {
        return userResponse;
      }
      if (req.getEndpoint().equalsIgnoreCase("/11-company/_search")) {
        return companyResponse;
      }
      return null;
    });

    given(userResponse.getEntity())
        .willReturn(new NStringEntity(getFileContent("queries/es/request/id-name-query-response-for-user.json"), ContentType.APPLICATION_JSON));

    given(companyResponse.getEntity())
        .willReturn(new NStringEntity(getFileContent("queries/es/response/id-name-query-response-for-company.json"), ContentType.APPLICATION_JSON));

    SecurityContextHolder.getContext()
        .setAuthentication(new CoreAuthentication("alfred", "11", emptySet(), ""));
    //when
    Map<String, Map<String, Object>> search = defaultIdNameSearchService.search(forTypeIds);
    //then
    assertThat(search.size()).isEqualTo(2);
    assertThat(search.get(user).get("1")).isEqualTo("Tony");
    assertThat(search.get(user).get("10")).isEqualTo("Stark");

    assertThat(search.get(company).get("2")).isEqualTo("Company-2");
    assertThat(search.get(company).get("20")).isEqualTo("Company-20");

    Mockito.verify(newRestClient, times(2)).performRequest(newRequestArgumentCaptor.capture());
    List<Request> requests = newRequestArgumentCaptor.getAllValues();
    assertThat(requests.get(0).getEndpoint()).isEqualTo("/11-id-name/_search");
    JSONAssert.assertEquals(getFileContent("queries/es/request/id-name-query-request-for-user.json"), getQuery(requests.get(0)),
        sizeMatcher(4));
    assertThat(requests.get(1).getEndpoint()).isEqualTo("/11-company/_search");
    JSONAssert.assertEquals(getFileContent("queries/es/request/id-name-query-request-for-company.json"), getQuery(requests.get(1)),
        sizeMatcher(4));

  }

  @NotNull
  private CustomComparator sizeMatcher(int i) {
    return new CustomComparator(JSONCompareMode.STRICT,
        new Customization("size", (o1, o2) -> Integer.parseInt(o1.toString()) == i));
  }
  @Test
  public void givenForTypeAndIdList_tryToGetIdNameForDeal_shouldCreateValidQuery() throws IOException, JSONException {
    //given
    Map<String, Set> forTypeIds = new HashMap<>();
    Set<Long> ids = new HashSet<>();
    ids.add(1L);
    ids.add(10L);
    String user = "USER";
    forTypeIds.put(user, ids);

    ids = new HashSet<>();
    ids.add(21L);
    ids.add(210L);
    String company = "DEAL";
    forTypeIds.put(company, ids);
    Response userResponse = mock(Response.class);
    Response dealResponse = mock(Response.class);

    given(newRestClient.performRequest(any(Request.class))).willAnswer(invocation -> {
      Object[] arguments = invocation.getArguments();
      Request req = (Request) arguments[0];
      if (req.getEndpoint().equalsIgnoreCase("/11-id-name/_search")) {
        return userResponse;
      }
      if (req.getEndpoint().equalsIgnoreCase("/11-deal/_search")) {
        return dealResponse;
      }
      return null;
    });


    given(userResponse.getEntity())
        .willReturn(new NStringEntity(getFileContent("queries/es/request/id-name-query-response-for-user.json"), ContentType.APPLICATION_JSON));

    given(dealResponse.getEntity())
        .willReturn(new NStringEntity(getFileContent("queries/es/response/id-name-query-response-for-deal.json"), ContentType.APPLICATION_JSON));

    SecurityContextHolder.getContext()
        .setAuthentication(new CoreAuthentication("alfred", "11", emptySet(), ""));
    //when
    Map<String, Map<String, Object>> search = defaultIdNameSearchService.search(forTypeIds);
    //then
    assertThat(search.size()).isEqualTo(2);
    assertThat(search.get(user).get("1")).isEqualTo("Tony");
    assertThat(search.get(user).get("10")).isEqualTo("Stark");

    assertThat(search.get(company).get("21")).isEqualTo("Deal-21");
    assertThat(search.get(company).get("210")).isEqualTo("Deal-210");

    Mockito.verify(newRestClient, times(2)).performRequest(newRequestArgumentCaptor.capture());
    List<Request> requests = newRequestArgumentCaptor.getAllValues();
    assertThat(requests.get(0).getEndpoint()).isEqualTo("/11-id-name/_search");
    JSONAssert.assertEquals(getFileContent("queries/es/request/id-name-query-request-for-user.json"), getQuery(requests.get(0)), sizeMatcher(4));

    assertThat(requests.get(1).getEndpoint()).isEqualTo("/11-deal/_search");
    JSONAssert.assertEquals(getFileContent("queries/es/request/id-name-query-request-for-deal.json"), getQuery(requests.get(1)), sizeMatcher(4));

  }

  @Test
  public void givenForTypeAndIdList_tryToGetIdNameForDealCompanyAndContact_shouldCreateValidQuery() throws IOException, JSONException {
    //given
    Map<String, Set> forTypeIds = new HashMap<>();
    Set<Long> ids = new HashSet<>();
    ids.add(1L);
    ids.add(10L);
    String user = "USER";
    forTypeIds.put(user, ids);

    ids = new HashSet<>();
    ids.add(21L);
    ids.add(210L);
    String deal = "DEAL";
    forTypeIds.put(deal, ids);

    ids = new HashSet<>();
    ids.add(2L);
    ids.add(20L);
    String company = "COMPANY";
    forTypeIds.put(company, ids);

    Response userResponse = mock(Response.class);
    Response dealResponse = mock(Response.class);
    Response companyResponse = mock(Response.class);

    given(newRestClient.performRequest(any(Request.class))).willAnswer(invocation -> {
      Object[] arguments = invocation.getArguments();
      Request req = (Request) arguments[0];
      if (req.getEndpoint().equalsIgnoreCase("/11-id-name/_search")) {
        return userResponse;
      }
      if (req.getEndpoint().equalsIgnoreCase("/11-deal/_search")) {
        return dealResponse;
      }
      if (req.getEndpoint().equalsIgnoreCase("/11-company/_search")) {
        return companyResponse;
      }
      return null;
    });

    given(userResponse.getEntity())
        .willReturn(new NStringEntity(getFileContent("queries/es/request/id-name-query-response-for-user.json"), ContentType.APPLICATION_JSON));

    given(dealResponse.getEntity())
        .willReturn(new NStringEntity(getFileContent("queries/es/response/id-name-query-response-for-deal.json"), ContentType.APPLICATION_JSON));

    given(companyResponse.getEntity())
        .willReturn(new NStringEntity(getFileContent("queries/es/response/id-name-query-response-for-company.json"), ContentType.APPLICATION_JSON));

    SecurityContextHolder.getContext()
        .setAuthentication(new CoreAuthentication("alfred", "11", emptySet(), ""));
    //when
    Map<String, Map<String, Object>> search = defaultIdNameSearchService.search(forTypeIds);
    //then
    assertThat(search.size()).isEqualTo(3);
    assertThat(search.get(user).get("1")).isEqualTo("Tony");
    assertThat(search.get(user).get("10")).isEqualTo("Stark");

    assertThat(search.get(deal).get("21")).isEqualTo("Deal-21");
    assertThat(search.get(deal).get("210")).isEqualTo("Deal-210");

    assertThat(search.get(company).get("2")).isEqualTo("Company-2");
    assertThat(search.get(company).get("20")).isEqualTo("Company-20");

    List<Request> requests;

    Mockito.verify(newRestClient, times(3)).performRequest(newRequestArgumentCaptor.capture());
    requests = newRequestArgumentCaptor.getAllValues();
    assertThat(requests.get(0).getEndpoint()).isEqualTo("/11-id-name/_search");
    JSONAssert.assertEquals(getFileContent("queries/es/request/id-name-query-request-for-user.json"), getQuery(requests.get(0)), sizeMatcher(6));
    assertThat(requests.get(1).getEndpoint()).isEqualTo("/11-deal/_search");
    JSONAssert.assertEquals(getFileContent("queries/es/request/id-name-query-request-for-deal.json"), getQuery(requests.get(1)), sizeMatcher(6));
    assertThat(requests.get(2).getEndpoint()).isEqualTo("/11-company/_search");
    JSONAssert.assertEquals(getFileContent("queries/es/request/id-name-query-request-for-company.json"), getQuery(requests.get(2)), sizeMatcher(6));
  }

  @Test
  public void givenNonDealForTypeAndIdList_tryToGetIdNameForDeal_shoulReturnEmpty() throws IOException, JSONException {
    //given
    Map<String, Set> forTypeIds = new HashMap<>();
    Set<Long> ids = new HashSet<>();
    ids.add(1L);
    ids.add(10L);
    String user = "USER";
    forTypeIds.put(user, ids);

    Response userResponse = mock(Response.class);

    given(newRestClient.performRequest(any(Request.class))).willAnswer(invocation -> {
      Object[] arguments = invocation.getArguments();
      Request req = (Request) arguments[0];
      if (req.getEndpoint().equalsIgnoreCase("/11-id-name/_search")) {
        return userResponse;
      }
      if (req.getEndpoint().equalsIgnoreCase("/11-deal/_search")) {
        throw new RuntimeException();
      }
      return null;
    });

    given(userResponse.getEntity())
        .willReturn(new NStringEntity(getFileContent("queries/es/request/id-name-query-response-for-user.json"), ContentType.APPLICATION_JSON));


    SecurityContextHolder.getContext()
        .setAuthentication(new CoreAuthentication("alfred", "11", emptySet(), ""));
    //when
    Map<String, Map<String, Object>> search = defaultIdNameSearchService.search(forTypeIds);
    //then
    assertThat(search.size()).isEqualTo(1);
    assertThat(search.get(user).get("1")).isEqualTo("Tony");
    assertThat(search.get(user).get("10")).isEqualTo("Stark");


    List<Request> requests;

    Mockito.verify(newRestClient, times(1)).performRequest(newRequestArgumentCaptor.capture());
    requests = newRequestArgumentCaptor.getAllValues();
    assertThat(requests.get(0).getEndpoint()).isEqualTo("/11-id-name/_search");
    JSONAssert.assertEquals(getFileContent("queries/es/request/id-name-query-request-for-user.json"), getQuery(requests.get(0)), sizeMatcher(2));
  }

  @Test
  public void givenNonCompanyForTypeAndIdList_tryToGetIdNameForDeal_shoulReturnEmpty() throws IOException, JSONException {
    //given
    Map<String, Set> forTypeIds = new HashMap<>();
    Set<Long> ids = new HashSet<>();
    ids.add(1L);
    ids.add(10L);
    String user = "USER";
    forTypeIds.put(user, ids);

    Response userResponse = mock(Response.class);

    given(newRestClient.performRequest(any(Request.class))).willAnswer(invocation -> {
      Object[] arguments = invocation.getArguments();
      Request req = (Request) arguments[0];
      if (req.getEndpoint().equalsIgnoreCase("/11-id-name/_search")) {
        return userResponse;
      }
      if (req.getEndpoint().equalsIgnoreCase("/11-company/_search")) {
        throw new RuntimeException();
      }
      return null;
    });

    given(userResponse.getEntity())
        .willReturn(new NStringEntity(getFileContent("queries/es/request/id-name-query-response-for-user.json"), ContentType.APPLICATION_JSON));


    SecurityContextHolder.getContext()
        .setAuthentication(new CoreAuthentication("alfred", "11", emptySet(), ""));
    //when
    Map<String, Map<String, Object>> search = defaultIdNameSearchService.search(forTypeIds);
    //then
    assertThat(search.size()).isEqualTo(1);
    assertThat(search.get(user).get("1")).isEqualTo("Tony");
    assertThat(search.get(user).get("10")).isEqualTo("Stark");


    List<Request> requests;

    Mockito.verify(newRestClient, times(1)).performRequest(newRequestArgumentCaptor.capture());
    requests = newRequestArgumentCaptor.getAllValues();
    assertThat(requests.get(0).getEndpoint()).isEqualTo("/11-id-name/_search");
    JSONAssert.assertEquals(getFileContent("queries/es/request/id-name-query-request-for-user.json"), getQuery(requests.get(0)), sizeMatcher(2));
  }

  @Test
  public void givenEmptyForTypeAndItsIdList_shouldReturnEmptyMap(){
    //when
    Map<String, Map<String, Object>> search = defaultIdNameSearchService.search(new HashMap<>());
    //then
    assertThat(search).isEmpty();
  }

  private String getFileContent(String filePath) throws IOException {
    return getStringFromReader(this.getClass().getClassLoader().getResourceAsStream(filePath));
  }

  private String getQuery(Request request) throws IOException {
    return getStringFromReader(request.getEntity().getContent());
  }

  public String getStringFromReader(InputStream is) throws IOException {
    BufferedReader br = new BufferedReader(new InputStreamReader(is));
    StringBuffer sb = new StringBuffer();
    String line;
    while ((line = br.readLine()) != null) {
      sb.append(line);
    }
    return sb.toString();
  }
}