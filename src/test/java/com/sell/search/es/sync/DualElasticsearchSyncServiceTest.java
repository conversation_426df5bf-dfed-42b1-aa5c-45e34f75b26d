package com.sell.search.es.sync;

import com.sell.search.es.abstraction.ElasticsearchClient;
import com.sell.search.es.abstraction.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DualElasticsearchSyncServiceTest {

    @Mock
    private ElasticsearchClient legacyClient;

    @Mock
    private ElasticsearchClient newClient;

    private DualElasticsearchSyncService dualSyncService;

    @BeforeEach
    void setUp() {
        dualSyncService = new DualElasticsearchSyncService(legacyClient, newClient, true);
    }

    @Test
    void testIndexSuccess() throws IOException {
        // Given
        IndexRequest request = IndexRequest.builder()
                .indexName("test-index")
                .documentType("_doc")
                .documentId("test-doc")
                .source(Map.of("name", "test"))
                .build();

        IndexResponse legacyResponse = IndexResponse.builder()
                .id("test-doc")
                .index("test-index")
                .result("CREATED")
                .build();

        IndexResponse newResponse = IndexResponse.builder()
                .id("test-doc")
                .index("test-index")
                .result("CREATED")
                .build();

        when(legacyClient.index(request)).thenReturn(legacyResponse);
        when(newClient.index(request)).thenReturn(newResponse);

        // When
        DualSyncResult<IndexResponse> result = dualSyncService.index(request);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getPrimaryResult()).isEqualTo(legacyResponse);
        assertThat(result.getLegacyResult()).isEqualTo(legacyResponse);
        assertThat(result.getNewResult()).isEqualTo(newResponse);

        verify(legacyClient).index(request);
        verify(newClient).index(request);
    }

    @Test
    void testIndexWithLegacyFailure() throws IOException {
        // Given
        IndexRequest request = IndexRequest.builder()
                .indexName("test-index")
                .documentType("_doc")
                .documentId("test-doc")
                .source(Map.of("name", "test"))
                .build();

        when(legacyClient.index(request)).thenThrow(new IOException("Legacy ES failed"));
        when(newClient.index(request)).thenReturn(IndexResponse.builder()
                .id("test-doc")
                .result("CREATED")
                .build());

        // When
        DualSyncResult<IndexResponse> result = dualSyncService.index(request);

        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getError()).contains("Legacy ES indexing failed");
    }

    @Test
    void testBulkSuccess() throws IOException {
        // Given
        List<IndexRequest> indexRequests = Arrays.asList(
                IndexRequest.builder()
                        .indexName("test-index")
                        .documentId("doc1")
                        .source(Map.of("name", "test1"))
                        .build(),
                IndexRequest.builder()
                        .indexName("test-index")
                        .documentId("doc2")
                        .source(Map.of("name", "test2"))
                        .build()
        );

        BulkRequest bulkRequest = BulkRequest.builder()
                .indexRequests(indexRequests)
                .build();

        BulkResponse legacyResponse = BulkResponse.builder()
                .hasFailures(false)
                .items(Arrays.asList(
                        BulkResponse.BulkItemResponse.builder().id("doc1").failed(false).build(),
                        BulkResponse.BulkItemResponse.builder().id("doc2").failed(false).build()
                ))
                .build();

        BulkResponse newResponse = BulkResponse.builder()
                .hasFailures(false)
                .items(Arrays.asList(
                        BulkResponse.BulkItemResponse.builder().id("doc1").failed(false).build(),
                        BulkResponse.BulkItemResponse.builder().id("doc2").failed(false).build()
                ))
                .build();

        when(legacyClient.bulk(bulkRequest)).thenReturn(legacyResponse);
        when(newClient.bulk(bulkRequest)).thenReturn(newResponse);

        // When
        DualSyncResult<BulkResponse> result = dualSyncService.bulk(bulkRequest);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getPrimaryResult().getItems()).hasSize(2);
        assertThat(result.getLegacyResult().getItems()).hasSize(2);
        assertThat(result.getNewResult().getItems()).hasSize(2);

        verify(legacyClient).bulk(bulkRequest);
        verify(newClient).bulk(bulkRequest);
    }

    @Test
    void testCreateIndexSuccess() throws IOException {
        // Given
        CreateIndexRequest request = CreateIndexRequest.builder()
                .indexName("test-index")
                .settings(Map.of("number_of_shards", 1))
                .mappings(Map.of("properties", Map.of("name", Map.of("type", "text"))))
                .build();

        when(legacyClient.createIndex(request)).thenReturn(true);
        when(newClient.createIndex(request)).thenReturn(true);

        // When
        DualSyncResult<Boolean> result = dualSyncService.createIndex(request);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getPrimaryResult()).isTrue();
        assertThat(result.getLegacyResult()).isTrue();
        assertThat(result.getNewResult()).isTrue();

        verify(legacyClient).createIndex(request);
        verify(newClient).createIndex(request);
    }

    @Test
    void testSearchFromPrimary() throws IOException {
        // Given
        SearchRequest request = SearchRequest.builder()
                .indices(new String[]{"test-index"})
                .from(0)
                .size(10)
                .build();

        SearchResponse expectedResponse = SearchResponse.builder()
                .totalHits(1)
                .hits(Arrays.asList(
                        SearchResponse.SearchHit.builder()
                                .id("doc1")
                                .source(Map.of("name", "test"))
                                .build()
                ))
                .build();

        when(legacyClient.search(request)).thenReturn(expectedResponse);

        // When
        SearchResponse result = dualSyncService.search(request);

        // Then
        assertThat(result).isEqualTo(expectedResponse);
        assertThat(result.getTotalHits()).isEqualTo(1);
        assertThat(result.getHits()).hasSize(1);

        verify(legacyClient).search(request);
        verify(newClient, never()).search(any()); // Should only search from primary
    }

    @Test
    void testIndexExistsFromPrimary() throws IOException {
        // Given
        String indexName = "test-index";
        when(legacyClient.indexExists(indexName)).thenReturn(true);

        // When
        boolean result = dualSyncService.indexExists(indexName);

        // Then
        assertThat(result).isTrue();

        verify(legacyClient).indexExists(indexName);
        verify(newClient, never()).indexExists(any()); // Should only check primary
    }

    @Test
    void testDeleteSuccess() throws IOException {
        // Given
        String indexName = "test-index";
        String documentType = "_doc";
        String documentId = "test-doc";

        when(legacyClient.delete(indexName, documentType, documentId)).thenReturn(true);
        when(newClient.delete(indexName, documentType, documentId)).thenReturn(true);

        // When
        DualSyncResult<Boolean> result = dualSyncService.delete(indexName, documentType, documentId);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getPrimaryResult()).isTrue();
        assertThat(result.getLegacyResult()).isTrue();
        assertThat(result.getNewResult()).isTrue();

        verify(legacyClient).delete(indexName, documentType, documentId);
        verify(newClient).delete(indexName, documentType, documentId);
    }

    @Test
    void testDualSyncDisabled() throws IOException {
        // Given
        DualElasticsearchSyncService disabledSyncService = 
            new DualElasticsearchSyncService(legacyClient, newClient, false);

        IndexRequest request = IndexRequest.builder()
                .indexName("test-index")
                .documentId("test-doc")
                .source(Map.of("name", "test"))
                .build();

        IndexResponse legacyResponse = IndexResponse.builder()
                .id("test-doc")
                .result("CREATED")
                .build();

        when(legacyClient.index(request)).thenReturn(legacyResponse);

        // When
        DualSyncResult<IndexResponse> result = disabledSyncService.index(request);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getPrimaryResult()).isEqualTo(legacyResponse);
        assertThat(result.getLegacyResult()).isEqualTo(legacyResponse);
        assertThat(result.getNewResult()).isNull(); // Should be null when disabled

        verify(legacyClient).index(request);
        verify(newClient, never()).index(any()); // Should not call new client when disabled
    }

    @Test
    void testPartialSuccess() throws IOException {
        // Given
        IndexRequest request = IndexRequest.builder()
                .indexName("test-index")
                .documentId("test-doc")
                .source(Map.of("name", "test"))
                .build();

        IndexResponse legacyResponse = IndexResponse.builder()
                .id("test-doc")
                .result("CREATED")
                .build();

        when(legacyClient.index(request)).thenReturn(legacyResponse);
        when(newClient.index(request)).thenThrow(new IOException("New ES failed"));

        // When
        DualSyncResult<IndexResponse> result = dualSyncService.index(request);

        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.isPartialSuccess()).isTrue();
        assertThat(result.getLegacyResult()).isEqualTo(legacyResponse);
        assertThat(result.getError()).contains("New ES indexing failed");
    }
}
