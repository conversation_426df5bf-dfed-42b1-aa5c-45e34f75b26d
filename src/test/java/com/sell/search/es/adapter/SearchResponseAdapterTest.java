package com.sell.search.es.adapter;

import com.sell.search.es.abstraction.model.SearchResponse;
import org.elasticsearch.action.search.SearchResponse as LegacySearchResponse;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SearchResponseAdapterTest {

    private SearchResponseAdapter adapter;

    @Mock
    private LegacySearchResponse legacySearchResponse;

    @Mock
    private SearchHits searchHits;

    @Mock
    private SearchHit searchHit1;

    @Mock
    private SearchHit searchHit2;

    @BeforeEach
    void setUp() {
        adapter = new SearchResponseAdapter();
    }

    @Test
    void testAdaptToGenericFormat() {
        // Given
        List<SearchResponse.SearchHit> hits = Arrays.asList(
                SearchResponse.SearchHit.builder()
                        .id("doc1")
                        .index("test-index")
                        .type("_doc")
                        .score(1.5f)
                        .source(Map.of("name", "Document 1", "content", "Content 1"))
                        .build(),
                SearchResponse.SearchHit.builder()
                        .id("doc2")
                        .index("test-index")
                        .type("_doc")
                        .score(1.2f)
                        .source(Map.of("name", "Document 2", "content", "Content 2"))
                        .build()
        );

        SearchResponse response = SearchResponse.builder()
                .totalHits(2L)
                .hits(hits)
                .timedOut(false)
                .took(150L)
                .aggregations(Map.of("avg_score", Map.of("value", 1.35)))
                .build();

        // When
        Map<String, Object> result = adapter.adaptToGenericFormat(response);

        // Then
        assertThat(result).containsEntry("took", 150L);
        assertThat(result).containsEntry("timed_out", false);
        assertThat(result).containsEntry("total_hits", 2L);

        Map<String, Object> hitsMap = (Map<String, Object>) result.get("hits");
        assertThat(hitsMap).containsEntry("total", 2L);
        assertThat(hitsMap).containsEntry("max_score", 1.5f);

        List<Map<String, Object>> hitsList = (List<Map<String, Object>>) hitsMap.get("hits");
        assertThat(hitsList).hasSize(2);

        Map<String, Object> hit1 = hitsList.get(0);
        assertThat(hit1).containsEntry("_id", "doc1");
        assertThat(hit1).containsEntry("_index", "test-index");
        assertThat(hit1).containsEntry("_type", "_doc");
        assertThat(hit1).containsEntry("_score", 1.5f);

        Map<String, Object> source1 = (Map<String, Object>) hit1.get("_source");
        assertThat(source1).containsEntry("name", "Document 1");
        assertThat(source1).containsEntry("content", "Content 1");

        assertThat(result).containsEntry("aggregations", Map.of("avg_score", Map.of("value", 1.35)));
    }

    @Test
    void testAdaptLegacyToGenericFormat() {
        // Given
        Map<String, Object> source1 = Map.of("name", "Legacy Doc 1", "type", "legacy");
        Map<String, Object> source2 = Map.of("name", "Legacy Doc 2", "type", "legacy");

        when(legacySearchResponse.getTook()).thenReturn(TimeValue.timeValueMillis(200));
        when(legacySearchResponse.isTimedOut()).thenReturn(false);
        when(legacySearchResponse.getHits()).thenReturn(searchHits);

        when(searchHits.getTotalHits()).thenReturn(2L);
        when(searchHits.getMaxScore()).thenReturn(2.0f);
        when(searchHits.getHits()).thenReturn(new SearchHit[]{searchHit1, searchHit2});

        when(searchHit1.getId()).thenReturn("legacy-doc-1");
        when(searchHit1.getIndex()).thenReturn("legacy-index");
        when(searchHit1.getType()).thenReturn("_doc");
        when(searchHit1.getScore()).thenReturn(2.0f);
        when(searchHit1.getSourceAsMap()).thenReturn(source1);

        when(searchHit2.getId()).thenReturn("legacy-doc-2");
        when(searchHit2.getIndex()).thenReturn("legacy-index");
        when(searchHit2.getType()).thenReturn("_doc");
        when(searchHit2.getScore()).thenReturn(1.8f);
        when(searchHit2.getSourceAsMap()).thenReturn(source2);

        when(legacySearchResponse.getAggregations()).thenReturn(null);

        // When
        Map<String, Object> result = adapter.adaptLegacyToGenericFormat(legacySearchResponse);

        // Then
        assertThat(result).containsEntry("took", 200L);
        assertThat(result).containsEntry("timed_out", false);
        assertThat(result).containsEntry("total_hits", 2L);

        Map<String, Object> hitsMap = (Map<String, Object>) result.get("hits");
        assertThat(hitsMap).containsEntry("total", 2L);
        assertThat(hitsMap).containsEntry("max_score", 2.0f);

        List<Map<String, Object>> hitsList = (List<Map<String, Object>>) hitsMap.get("hits");
        assertThat(hitsList).hasSize(2);

        Map<String, Object> hit1 = hitsList.get(0);
        assertThat(hit1).containsEntry("_id", "legacy-doc-1");
        assertThat(hit1).containsEntry("_index", "legacy-index");
        assertThat(hit1).containsEntry("_type", "_doc");
        assertThat(hit1).containsEntry("_score", 2.0f);

        Map<String, Object> hitSource1 = (Map<String, Object>) hit1.get("_source");
        assertThat(hitSource1).containsEntry("name", "Legacy Doc 1");
        assertThat(hitSource1).containsEntry("type", "legacy");
    }

    @Test
    void testNormalizeResponse() {
        // Given - ES 7+ format with total as object
        Map<String, Object> response = new HashMap<>();
        response.put("took", 100L);
        response.put("timed_out", false);

        Map<String, Object> hits = new HashMap<>();
        Map<String, Object> total = new HashMap<>();
        total.put("value", 5L);
        total.put("relation", "eq");
        hits.put("total", total);
        hits.put("max_score", 1.0f);
        hits.put("hits", Arrays.asList(
                Map.of("_id", "doc1", "_score", 1.0f, "_source", Map.of("name", "test"))
        ));

        response.put("hits", hits);

        // When
        Map<String, Object> result = adapter.normalizeResponse(response);

        // Then
        assertThat(result).containsEntry("took", 100L);
        assertThat(result).containsEntry("timed_out", false);

        Map<String, Object> normalizedHits = (Map<String, Object>) result.get("hits");
        assertThat(normalizedHits).containsEntry("total", 5L); // Should be normalized to number
        assertThat(normalizedHits).containsEntry("max_score", 1.0f);
    }

    @Test
    void testNormalizeResponseWithES6Format() {
        // Given - ES 6.x format with total as number
        Map<String, Object> response = new HashMap<>();
        response.put("took", 100L);
        response.put("timed_out", false);

        Map<String, Object> hits = new HashMap<>();
        hits.put("total", 3L); // Already a number in ES 6.x
        hits.put("max_score", 1.0f);
        hits.put("hits", Arrays.asList(
                Map.of("_id", "doc1", "_score", 1.0f, "_source", Map.of("name", "test"))
        ));

        response.put("hits", hits);

        // When
        Map<String, Object> result = adapter.normalizeResponse(response);

        // Then
        assertThat(result).containsEntry("took", 100L);
        assertThat(result).containsEntry("timed_out", false);

        Map<String, Object> normalizedHits = (Map<String, Object>) result.get("hits");
        assertThat(normalizedHits).containsEntry("total", 3L); // Should remain as number
        assertThat(normalizedHits).containsEntry("max_score", 1.0f);
    }

    @Test
    void testAdaptToGenericFormatWithEmptyHits() {
        // Given
        SearchResponse response = SearchResponse.builder()
                .totalHits(0L)
                .hits(Collections.emptyList())
                .timedOut(false)
                .took(50L)
                .build();

        // When
        Map<String, Object> result = adapter.adaptToGenericFormat(response);

        // Then
        assertThat(result).containsEntry("took", 50L);
        assertThat(result).containsEntry("timed_out", false);
        assertThat(result).containsEntry("total_hits", 0L);

        Map<String, Object> hitsMap = (Map<String, Object>) result.get("hits");
        assertThat(hitsMap).containsEntry("total", 0L);
        assertThat(hitsMap).containsEntry("max_score", null);

        List<Map<String, Object>> hitsList = (List<Map<String, Object>>) hitsMap.get("hits");
        assertThat(hitsList).isEmpty();
    }

    @Test
    void testAdaptToGenericFormatWithNullAggregations() {
        // Given
        SearchResponse response = SearchResponse.builder()
                .totalHits(1L)
                .hits(Arrays.asList(
                        SearchResponse.SearchHit.builder()
                                .id("doc1")
                                .index("test-index")
                                .type("_doc")
                                .score(1.0f)
                                .source(Map.of("name", "test"))
                                .build()
                ))
                .timedOut(false)
                .took(75L)
                .aggregations(null)
                .build();

        // When
        Map<String, Object> result = adapter.adaptToGenericFormat(response);

        // Then
        assertThat(result).containsEntry("took", 75L);
        assertThat(result).containsEntry("timed_out", false);
        assertThat(result).containsEntry("total_hits", 1L);
        assertThat(result).doesNotContainKey("aggregations");
    }
}
