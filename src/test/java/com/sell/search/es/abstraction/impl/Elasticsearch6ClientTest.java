package com.sell.search.es.abstraction.impl;

import com.sell.search.es.abstraction.model.*;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class Elasticsearch6ClientTest {

    @Mock
    private RestHighLevelClient restHighLevelClient;

    private Elasticsearch6Client elasticsearch6Client;

    @BeforeEach
    void setUp() {
        elasticsearch6Client = new Elasticsearch6Client(restHighLevelClient);
    }

    @Test
    void testIndex() throws IOException {
        // Given
        Map<String, Object> source = new HashMap<>();
        source.put("name", "test document");
        source.put("description", "test description");

        IndexRequest request = IndexRequest.builder()
                .indexName("test-index")
                .documentType("_doc")
                .documentId("test-doc-1")
                .source(source)
                .versionType("INTERNAL")
                .version(1L)
                .build();

        IndexResponse mockResponse = mock(IndexResponse.class);
        when(mockResponse.getId()).thenReturn("test-doc-1");
        when(mockResponse.getIndex()).thenReturn("test-index");
        when(mockResponse.getType()).thenReturn("_doc");
        when(mockResponse.getVersion()).thenReturn(1L);
        when(mockResponse.getResult()).thenReturn(org.elasticsearch.action.DocWriteResponse.Result.CREATED);

        when(restHighLevelClient.index(any(org.elasticsearch.action.index.IndexRequest.class), eq(RequestOptions.DEFAULT)))
                .thenReturn(mockResponse);

        // When
        com.sell.search.es.abstraction.model.IndexResponse result = elasticsearch6Client.index(request);

        // Then
        assertThat(result.getId()).isEqualTo("test-doc-1");
        assertThat(result.getIndex()).isEqualTo("test-index");
        assertThat(result.getType()).isEqualTo("_doc");
        assertThat(result.getVersion()).isEqualTo(1L);
        assertThat(result.getResult()).isEqualTo("CREATED");
        assertThat(result.isCreated()).isTrue();

        verify(restHighLevelClient).index(any(org.elasticsearch.action.index.IndexRequest.class), eq(RequestOptions.DEFAULT));
    }

    @Test
    void testIndexWithIOException() throws IOException {
        // Given
        IndexRequest request = IndexRequest.builder()
                .indexName("test-index")
                .documentType("_doc")
                .documentId("test-doc-1")
                .source(Map.of("name", "test"))
                .build();

        when(restHighLevelClient.index(any(org.elasticsearch.action.index.IndexRequest.class), eq(RequestOptions.DEFAULT)))
                .thenThrow(new IOException("Connection failed"));

        // When & Then
        assertThatThrownBy(() -> elasticsearch6Client.index(request))
                .isInstanceOf(IOException.class)
                .hasMessage("Connection failed");
    }

    @Test
    void testSearch() throws IOException {
        // Given
        SearchRequest request = SearchRequest.builder()
                .indices(new String[]{"test-index"})
                .from(0)
                .size(10)
                .build();

        SearchResponse mockResponse = mock(SearchResponse.class);
        SearchHits mockHits = mock(SearchHits.class);
        SearchHit mockHit = mock(SearchHit.class);

        when(mockResponse.getHits()).thenReturn(mockHits);
        when(mockResponse.isTimedOut()).thenReturn(false);
        when(mockResponse.getTook()).thenReturn(TimeValue.timeValueMillis(100));

        when(mockHits.getTotalHits()).thenReturn(1L);
        when(mockHits.getHits()).thenReturn(new SearchHit[]{mockHit});

        when(mockHit.getId()).thenReturn("test-doc-1");
        when(mockHit.getIndex()).thenReturn("test-index");
        when(mockHit.getType()).thenReturn("_doc");
        when(mockHit.getScore()).thenReturn(1.0f);
        when(mockHit.getSourceAsMap()).thenReturn(Map.of("name", "test document"));

        when(restHighLevelClient.search(any(org.elasticsearch.action.search.SearchRequest.class), eq(RequestOptions.DEFAULT)))
                .thenReturn(mockResponse);

        // When
        com.sell.search.es.abstraction.model.SearchResponse result = elasticsearch6Client.search(request);

        // Then
        assertThat(result.getTotalHits()).isEqualTo(1L);
        assertThat(result.getHits()).hasSize(1);
        assertThat(result.isTimedOut()).isFalse();
        assertThat(result.getTook()).isEqualTo(100L);

        com.sell.search.es.abstraction.model.SearchResponse.SearchHit hit = result.getHits().get(0);
        assertThat(hit.getId()).isEqualTo("test-doc-1");
        assertThat(hit.getIndex()).isEqualTo("test-index");
        assertThat(hit.getType()).isEqualTo("_doc");
        assertThat(hit.getScore()).isEqualTo(1.0f);
        assertThat(hit.getSource()).containsEntry("name", "test document");

        verify(restHighLevelClient).search(any(org.elasticsearch.action.search.SearchRequest.class), eq(RequestOptions.DEFAULT));
    }

    @Test
    void testCreateIndex() throws IOException {
        // Given
        Map<String, Object> settings = Map.of("number_of_shards", 1);
        Map<String, Object> mappings = Map.of("properties", Map.of("name", Map.of("type", "text")));

        CreateIndexRequest request = CreateIndexRequest.builder()
                .indexName("test-index")
                .settings(settings)
                .mappings(mappings)
                .documentType("_doc")
                .build();

        org.elasticsearch.action.admin.indices.create.CreateIndexResponse mockResponse = 
            mock(org.elasticsearch.action.admin.indices.create.CreateIndexResponse.class);
        when(mockResponse.isAcknowledged()).thenReturn(true);

        when(restHighLevelClient.indices().create(
                any(org.elasticsearch.action.admin.indices.create.CreateIndexRequest.class), 
                eq(RequestOptions.DEFAULT)))
                .thenReturn(mockResponse);

        // When
        boolean result = elasticsearch6Client.createIndex(request);

        // Then
        assertThat(result).isTrue();

        verify(restHighLevelClient.indices()).create(
                any(org.elasticsearch.action.admin.indices.create.CreateIndexRequest.class), 
                eq(RequestOptions.DEFAULT));
    }

    @Test
    void testIndexExists() throws IOException {
        // Given
        String indexName = "test-index";

        when(restHighLevelClient.indices().exists(
                any(org.elasticsearch.action.admin.indices.exists.indices.IndicesExistsRequest.class), 
                eq(RequestOptions.DEFAULT)))
                .thenReturn(true);

        // When
        boolean result = elasticsearch6Client.indexExists(indexName);

        // Then
        assertThat(result).isTrue();

        verify(restHighLevelClient.indices()).exists(
                any(org.elasticsearch.action.admin.indices.exists.indices.IndicesExistsRequest.class), 
                eq(RequestOptions.DEFAULT));
    }

    @Test
    void testDelete() throws IOException {
        // Given
        String indexName = "test-index";
        String documentType = "_doc";
        String documentId = "test-doc-1";

        org.elasticsearch.action.delete.DeleteResponse mockResponse = 
            mock(org.elasticsearch.action.delete.DeleteResponse.class);
        when(mockResponse.getResult()).thenReturn(org.elasticsearch.action.DocWriteResponse.Result.DELETED);

        when(restHighLevelClient.delete(
                any(org.elasticsearch.action.delete.DeleteRequest.class), 
                eq(RequestOptions.DEFAULT)))
                .thenReturn(mockResponse);

        // When
        boolean result = elasticsearch6Client.delete(indexName, documentType, documentId);

        // Then
        assertThat(result).isTrue();

        verify(restHighLevelClient).delete(
                any(org.elasticsearch.action.delete.DeleteRequest.class), 
                eq(RequestOptions.DEFAULT));
    }

    @Test
    void testGetVersion() {
        // When
        String version = elasticsearch6Client.getVersion();

        // Then
        assertThat(version).isEqualTo("6.8.17");
    }

    @Test
    void testClose() throws IOException {
        // When
        elasticsearch6Client.close();

        // Then
        verify(restHighLevelClient).close();
    }
}
