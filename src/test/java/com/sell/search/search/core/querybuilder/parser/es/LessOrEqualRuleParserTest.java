package com.sell.search.search.core.querybuilder.parser.es;

import static org.junit.Assert.assertEquals;

import com.sell.search.search.core.querybuilder.model.JsonRule;
import org.elasticsearch.index.query.AbstractQueryBuilder;
import org.json.JSONException;
import org.junit.Test;

public class LessOrEqualRuleParserTest{
  LessOrEqualRuleParser parser= new LessOrEqualRuleParser();
  @Test
  public void givenRuleForCustomFieldField_shouldReturnNestedQuery() throws JSONException {
    // given
    JsonRule rule = new JsonRule();
    rule.setField("customFieldValues.cfMyDate");
    rule.setType("date");
    rule.setFieldName("cfMyDate");
    rule.setValue("2023-02-11T07:00:00.000Z");
    // when
    AbstractQueryBuilder parse = parser.parse(rule, null);
    // then


    assertEquals(parse.toString(),"{\n"
        + "  \"range\" : {\n"
        + "    \"customFieldValues.cfMyDate\" : {\n"
        + "      \"from\" : null,\n"
        + "      \"to\" : \"2023-02-11T07:00:00.000Z\",\n"
        + "      \"include_lower\" : true,\n"
        + "      \"include_upper\" : true,\n"
        + "      \"boost\" : 1.0\n"
        + "    }\n"
        + "  }\n"
        + "}");
  }

}