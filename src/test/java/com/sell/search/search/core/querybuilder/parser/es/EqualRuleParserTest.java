package com.sell.search.search.core.querybuilder.parser.es;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import com.sell.search.search.core.querybuilder.model.JsonRule;
import com.sell.search.search.core.querybuilder.model.sql.Operation;
import org.elasticsearch.index.query.AbstractQueryBuilder;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Test;

public class EqualRuleParserTest {

  EqualRuleParser parser = new EqualRuleParser();
  @Test
  public void givenRuleForMultiPicklistValueField_shouldReturnNestedQuery() throws JSONException {
    // given
    JsonRule rule = new JsonRule();
    rule.setField("kylas_MULTI_PICKLIST_");
    rule.setType("long");
    rule.setFieldName("kylas_MULTI_PICKLIST_");
    rule.setValue(100);
    // when
    AbstractQueryBuilder parse = parser.parse(rule, null);
    // then

    JSONObject queryJson = new JSONObject(parse.toString());
    JSONObject valueObject =
        queryJson
            .getJSONObject("nested")
            .getJSONObject("query")
            .getJSONObject("term")
            .getJSONObject("kylas_MULTI_PICKLIST_.id");
    assertThat(valueObject.getString("value")).isEqualTo("100");
    assertThat(queryJson.getJSONObject("nested").getString("path")).isEqualTo("kylas_MULTI_PICKLIST_");
  }

  @Test
  public void givenRuleForCustomFieldField_shouldReturnNestedQuery() throws JSONException {
    // given
    JsonRule rule = new JsonRule();
    rule.setField("customFieldValues.cfMyPicklist");
    rule.setType("long");
    rule.setFieldName("cfMyPicklist");
    rule.setValue(100);
    // when
    AbstractQueryBuilder parse = parser.parse(rule, null);
    // then

    JSONObject queryJson = new JSONObject(parse.toString());
    JSONObject valueObject =
        queryJson
            .getJSONObject("term")
            .getJSONObject("customFieldValues.cfMyPicklist");
    assertThat(valueObject.getString("value")).isEqualTo("100");
  }

  @Test
  public void givenRuleForCustomTextField_shouldReturnNestedQuery() throws JSONException {
    // given
    JsonRule rule = new JsonRule();
    rule.setField("customFieldValues.cfMyText");
    rule.setType("string");
    rule.setFieldName("cfMyText");
    rule.setValue("MYTEXT");
    String expectedQueryString="jsonb_extract_path_text(custom_field,'cfMyText') = ?";
    // when
    Operation parse = new com.sell.search.search.core.querybuilder.parser.sql.EqualRuleParser().parse(rule, null);
    // then

    assertNotNull(parse);
    assertEquals("mytext", parse.getValue());
    assertEquals(expectedQueryString, parse.getOperate().toString());
  }
}