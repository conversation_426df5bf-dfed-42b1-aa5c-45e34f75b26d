package com.sell.search.global.search.api;

import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static java.util.Collections.emptyList;
import static org.mockito.BDDMockito.given;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.times;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.sell.search.core.client.InternalShareRuleService;
import com.sell.search.core.domain.Action;
import com.sell.search.core.domain.EntityType;
import com.sell.search.core.domain.PermissionAction;
import com.sell.search.core.dto.AccessSummary;
import com.sell.search.core.dto.AccessSummaryResponse;
import com.sell.search.core.dto.EntityAccessDTO;
import com.sell.search.entity.core.entity.Field;
import com.sell.search.global.search.api.request.SearchRequest;
import com.sell.search.global.search.api.response.SearchResponse;
import com.sell.search.security.UserFacade;
import com.sell.search.service.ElasticSearchService;
import com.sell.search.service.client.company.CompanyService;
import com.sell.search.service.client.company.response.FieldResponse;
import com.sell.search.service.client.config.EntityService;
import com.sell.search.service.client.deal.DealService;
import com.sell.search.service.client.task.TaskService;
import com.sell.search.util.FileUtil;
import io.zonky.test.db.AutoConfigureEmbeddedDatabase;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.json.JSONException;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@RunWith(SpringRunner.class)
@AutoConfigureEmbeddedDatabase
@AutoConfigureMockMvc
@TestPropertySource(
    properties = {
        "newElasticsearch.host=localhost",
        "newElasticsearch.port=7071"
    })
public class GlobalSearchFacadeTest {

  private static final String LEAD = "LEAD";
  private static final String DEAL = "DEAL";
  private static final String COMPANY = "COMPANY";
  private static final String CONTACT = "CONTACT";
  private static final String TASK = "TASK";

  @Autowired
  private ElasticSearchService elasticSearchService;

  @Rule
  public WireMockRule newEsWireMockRule = new WireMockRule(7071);

  @Before
  public void beforeEach() {
    newEsWireMockRule.resetAll();
  }

  @After
  public void tearDown() {
    newEsWireMockRule.stop();
  }

  @Autowired
  GlobalSearchFacade globalSearchFacade;

  @MockBean
  UserFacade userFacade;

  @MockBean
  InternalShareRuleService internalShareRuleService;

  @MockBean
  DealService dealService;

  @MockBean
  CompanyService companyService;

  @MockBean
  EntityService entityService;

  @MockBean
  TaskService taskService;

  @Test
  public void givenSearchRequestWithReadAllTrue_shouldReturnResponse() throws IOException, JSONException {
    //given
    Long loggedInUserId = 1L;
    Long tenantId = 2L;
    SearchRequest request = new SearchRequest("query", Collections.singletonList(LEAD));
    ArgumentCaptor<org.elasticsearch.action.search.SearchRequest> searchRequestArgumentCaptor = ArgumentCaptor.forClass(
        org.elasticsearch.action.search.SearchRequest.class);
    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.getUserId()).willReturn(loggedInUserId);
    given(userFacade.hasReadAllPermissionAction("lead")).willReturn(true);
    String leadGlobalResponse =
        FileUtil.getStringFrom("queries/es/response/global-search-lead-aggregated-results-tenant-2.json");

    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                WireMock.aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(leadGlobalResponse)));
    //when
    SearchResponse searchResult = globalSearchFacade.getSearchResult(request, 1L);
    //then
    WireMock.verify(postRequestedFor(urlEqualTo("/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
        .withRequestBody(equalToJson(FileUtil.getStringFrom("queries/es/request/global-search-es-query-request-tenantId2.json"))));

    String expectedResponse = new ObjectMapper().writeValueAsString(searchResult);
    JSONAssert.assertEquals(FileUtil.getStringFrom("queries/es/response/global-search-content-response-lead.json"),
        expectedResponse, JSONCompareMode.LENIENT);
  }

  @Test
  public void givenSearchRequestWithReadAllFalse_shouldResolveShareRuleAndReturnResponse() throws IOException, JSONException {
    //given
    Long loggedInUserId = 1L;
    Long tenantId = 2L;
    SearchRequest request = new SearchRequest("query", Collections.singletonList(LEAD));
    ArgumentCaptor<org.elasticsearch.action.search.SearchRequest> searchRequestArgumentCaptor = ArgumentCaptor.forClass(
        org.elasticsearch.action.search.SearchRequest.class);
    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.getUserId()).willReturn(loggedInUserId);
    given(userFacade.hasReadAllPermissionAction("lead")).willReturn(false);
    String leadGlobalResponse =
        FileUtil.getStringFrom("queries/es/response/global-search-lead-aggregated-results-tenant-2.json");
    given(internalShareRuleService.resolveAccessSummary(eq(com.sell.search.core.domain.EntityType.LEAD), eq(PermissionAction.READ))).willReturn(
        new EntityAccessDTO(
            Collections.singletonList(1L), Collections.singletonList(2L)
        ));
    given(entityService.getFields(EntityType.valueOf(LEAD))).willReturn(emptyList());

    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                WireMock.aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(leadGlobalResponse)));
    //when
    SearchResponse searchResult = globalSearchFacade.getSearchResult(request, 1L);
    //then
    WireMock.verify(postRequestedFor(urlEqualTo(
        "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
        .withRequestBody(equalToJson(FileUtil.getStringFrom("queries/es/request/global-search-es-query-read-all-false-query.json"))));
    Mockito.verify(internalShareRuleService, times(1))
        .resolveAccessSummary(eq(com.sell.search.core.domain.EntityType.LEAD), eq(PermissionAction.READ));
    String expectedResponse = new ObjectMapper().writeValueAsString(searchResult);
    JSONAssert.assertEquals(FileUtil.getStringFrom("queries/es/response/global-search-content-response-lead.json"),
        expectedResponse, JSONCompareMode.LENIENT);
  }

  @Test
  public void givenSearchRequestWithDealReadAllFalse_shouldResolveShareRuleAndReturnResponse() throws IOException, JSONException {
    //given
    Long loggedInUserId = 1L;
    Long tenantId = 2L;
    SearchRequest request = new SearchRequest("query", Collections.singletonList(DEAL));
    ArgumentCaptor<org.elasticsearch.action.search.SearchRequest> searchRequestArgumentCaptor = ArgumentCaptor.forClass(
        org.elasticsearch.action.search.SearchRequest.class);
    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.getUserId()).willReturn(loggedInUserId);
    given(userFacade.hasReadAllPermissionAction("deal")).willReturn(false);
    String dealGlobal =
        FileUtil.getStringFrom("queries/es/response/global-search-lead-aggregated-results-tenant-2.json");
    given(dealService.getAccessSummary("READ")).willReturn(
        new EntityAccessDTO(
            Collections.singletonList(1L), Collections.singletonList(2L)
        ));

    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                WireMock.aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(dealGlobal)));
    //when
    SearchResponse searchResult = globalSearchFacade.getSearchResult(request, 1L);
    //then
    WireMock.verify(postRequestedFor(urlEqualTo(
        "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
        .withRequestBody(equalToJson(FileUtil.getStringFrom("queries/es/request/global-search-es-query-read-all-false-query-deal.json"))));
    Mockito.verify(dealService, times(1))
        .getAccessSummary("READ");
    String expectedResponse = new ObjectMapper().writeValueAsString(searchResult);
    JSONAssert.assertEquals(FileUtil.getStringFrom("queries/es/response/global-search-content-response-lead.json"),
        expectedResponse, JSONCompareMode.LENIENT);
  }

  @Test
  public void givenSearchRequestWithMaskedFields_shouldReturnResponseWithMaskedValues() throws IOException, JSONException {
    //given
    Long loggedInUserId = 1L;
    Long tenantId = 2L;
    SearchRequest request = new SearchRequest("query", Collections.singletonList(LEAD));
    ArgumentCaptor<org.elasticsearch.action.search.SearchRequest> searchRequestArgumentCaptor = ArgumentCaptor.forClass(
        org.elasticsearch.action.search.SearchRequest.class);
    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.getUserId()).willReturn(loggedInUserId);
    given(userFacade.hasReadAllPermissionAction("lead")).willReturn(true);
    List<Field> fields = new ArrayList<>();
    com.sell.search.entity.core.entity.Field phoneNumberField = new com.sell.search.entity.core.entity.Field();
    phoneNumberField.setName("phoneNumbers");
    fields.add(phoneNumberField);
    given(entityService.getFieldMaskedFields(EntityType.LEAD)).willReturn(fields);
    String leadGlobalResponse =
        FileUtil.getStringFrom("queries/es/response/global-search-lead-aggregated-results-tenant-2.json");


    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                WireMock.aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(leadGlobalResponse)));
    //when
    SearchResponse searchResult = globalSearchFacade.getSearchResult(request, 1L);
    //then
    WireMock.verify(postRequestedFor(urlEqualTo(
        "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
        .withRequestBody(equalToJson(FileUtil.getStringFrom("queries/es/request/global-search-es-query-request-tenantId2.json"))));

    String expectedResponse = new ObjectMapper().writeValueAsString(searchResult);
    JSONAssert.assertEquals(FileUtil.getStringFrom("queries/es/response/global-masked-response-tenantId-2.json"),
        expectedResponse, JSONCompareMode.LENIENT);
  }

  @Test
  public void givenSearchRequestWithDoubleQuotes_shouldReturnResponseWithExactMatch() throws IOException, JSONException {
    //given
    Long loggedInUserId = 1L;
    Long tenantId = 2L;
    SearchRequest request = new SearchRequest("\"query\"", Collections.singletonList(LEAD));
    ArgumentCaptor<org.elasticsearch.action.search.SearchRequest> searchRequestArgumentCaptor = ArgumentCaptor.forClass(
        org.elasticsearch.action.search.SearchRequest.class);
    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.getUserId()).willReturn(loggedInUserId);
    given(userFacade.hasReadAllPermissionAction("lead")).willReturn(true);
    List<Field> fields = new ArrayList<>();
    com.sell.search.entity.core.entity.Field phoneNumberField = new com.sell.search.entity.core.entity.Field();
    phoneNumberField.setName("phoneNumbers");
    fields.add(phoneNumberField);
    String leadGlobalResponse =
        FileUtil.getStringFrom("queries/es/response/global-search-lead-aggregated-results-tenant-2.json");
    given(entityService.getFieldMaskedFields(EntityType.LEAD)).willReturn(fields);

    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                WireMock.aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(leadGlobalResponse)));
    //when
    SearchResponse searchResult = globalSearchFacade.getSearchResult(request, 1L);
    //then
     WireMock.verify(postRequestedFor(urlEqualTo(
        "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
        .withRequestBody(equalToJson(FileUtil.getStringFrom("queries/es/request/global-search-exact-match-es-query.json"))));

    String expectedResponse = new ObjectMapper().writeValueAsString(searchResult);
    JSONAssert.assertEquals(FileUtil.getStringFrom("queries/es/response/global-masked-response-tenantId-2.json"),
        expectedResponse, JSONCompareMode.LENIENT);
  }

  @Test
  public void givenSearchRequestWithReadAllTrueForLeadAndDeal_shouldReturnResponse() throws IOException, JSONException {
    //given
    Long loggedInUserId = 159L;
    Long tenantId = 82L;
    SearchRequest request = new SearchRequest("ikka", Arrays.asList(LEAD,DEAL));

    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.getUserId()).willReturn(loggedInUserId);
    given(userFacade.hasReadAllPermissionAction("lead")).willReturn(true);
    given(userFacade.hasReadAllPermissionAction("deal")).willReturn(true);
    String globalResponse =
        FileUtil.getStringFrom("queries/es/response/global-search-lead-deal-aggregated-results-tenant-82.json");

    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/82-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                WireMock.aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(globalResponse)));
    //when
    SearchResponse searchResult = globalSearchFacade.getSearchResult(request, 159L);
    //then
    WireMock.verify(postRequestedFor(urlEqualTo("/82-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
        .withRequestBody(equalToJson(
            FileUtil.getStringFrom("queries/es/request/global-search-es-lead-deal-query-request-tenantId82.json"),
            true,
            true
        )));

    String expectedResponse = new ObjectMapper().writeValueAsString(searchResult);
    JSONAssert.assertEquals(FileUtil.getStringFrom("queries/es/response/global-search-content-response-lead-deal.json"),
        expectedResponse, JSONCompareMode.LENIENT);
  }


  @Test
  public void givenSearchRequestWithLeadReadAllFalseAndDealReadAllTrue_shouldResolveShareRuleAndReturnResponse() throws IOException, JSONException {
    //given
    Long loggedInUserId = 1L;
    Long tenantId = 2L;
    SearchRequest request = new SearchRequest("query", Arrays.asList(LEAD,DEAL));
    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.getUserId()).willReturn(loggedInUserId);
    given(userFacade.hasReadAllPermissionAction("lead")).willReturn(false);
    given(userFacade.hasReadAllPermissionAction("deal")).willReturn(true);
    String globalResponse =
        FileUtil.getStringFrom("queries/es/response/global-search-lead-aggregated-results-tenant-2.json");
    given(internalShareRuleService.resolveAccessSummary(eq(com.sell.search.core.domain.EntityType.LEAD), eq(PermissionAction.READ))).willReturn(
        new EntityAccessDTO(
            Collections.singletonList(1L), Collections.singletonList(2L)
        ));
    given(entityService.getFields(EntityType.valueOf(LEAD))).willReturn(emptyList());

    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                WireMock.aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(globalResponse)));
    //when
    SearchResponse searchResult = globalSearchFacade.getSearchResult(request, 1L);
    //then
    WireMock.verify(postRequestedFor(urlEqualTo(
        "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
        .withRequestBody(equalToJson(FileUtil.getStringFrom("queries/es/request/global-search-es-lead-read-all-deal-read-query-request-tenantId82.json"),true,true)));
    Mockito.verify(internalShareRuleService, times(1))
        .resolveAccessSummary(eq(com.sell.search.core.domain.EntityType.LEAD), eq(PermissionAction.READ));
    String expectedResponse = new ObjectMapper().writeValueAsString(searchResult);
    JSONAssert.assertEquals(FileUtil.getStringFrom("queries/es/response/global-search-content-response-lead.json"),
        expectedResponse, JSONCompareMode.LENIENT);
  }

  @Test
  public void givenSearchRequestWithContactReadAllFalseAndLeadReadAllTrue_shouldResolveShareRuleAndReturnResponse()
      throws IOException, JSONException {
    //given
    Long loggedInUserId = 1L;
    Long tenantId = 2L;
    SearchRequest request = new SearchRequest("query", Arrays.asList(LEAD, CONTACT));
    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.getUserId()).willReturn(loggedInUserId);
    given(userFacade.hasReadAllPermissionAction("lead")).willReturn(true);
    given(userFacade.hasReadAllPermissionAction("contact")).willReturn(false);
    String globalResponse =
        FileUtil.getStringFrom("queries/es/response/global-search-lead-aggregated-results-tenant-2.json");
    given(internalShareRuleService.resolveAccessSummary(eq(com.sell.search.core.domain.EntityType.CONTACT), eq(PermissionAction.READ))).willReturn(
        new EntityAccessDTO(
            Collections.singletonList(1L), Collections.singletonList(2L)
        ));
    given(entityService.getFields(EntityType.valueOf(LEAD))).willReturn(emptyList());

    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                WireMock.aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(globalResponse)));
    //when
    SearchResponse searchResult = globalSearchFacade.getSearchResult(request, 1L);
    //then
    WireMock.verify(postRequestedFor(urlEqualTo(
        "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
        .withRequestBody(
            equalToJson(FileUtil.getStringFrom("queries/es/request/global-search-es-contact-read-and-lead-read-all-query-request-tenantId82.json"),
                true,
                true)));
    Mockito.verify(internalShareRuleService, times(1))
        .resolveAccessSummary(eq(com.sell.search.core.domain.EntityType.CONTACT), eq(PermissionAction.READ));
    String expectedResponse = new ObjectMapper().writeValueAsString(searchResult);
    JSONAssert.assertEquals(FileUtil.getStringFrom("queries/es/response/global-search-content-response-lead.json"),
        expectedResponse, JSONCompareMode.LENIENT);
  }


  @Test
  public void givenSearchRequestWithCompanyReadAllFalse_shouldResolveShareRuleAndReturnResponse() throws IOException, JSONException {
    //given
    Long loggedInUserId = 1L;
    Long tenantId = 2L;
    SearchRequest request = new SearchRequest("query", Collections.singletonList(COMPANY));
    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.getUserId()).willReturn(loggedInUserId);
    given(userFacade.hasReadAllPermissionAction("company")).willReturn(false);
    String companyGlobal =
        FileUtil.getStringFrom("queries/es/response/global-search-company-aggregated-results-tenant-2.json");
    given(companyService.getAccessSummary("READ")).willReturn(
        new EntityAccessDTO(
            Collections.singletonList(1L), Collections.singletonList(2L)
        ));

    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                WireMock.aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(companyGlobal)));
    //when
    SearchResponse searchResult = globalSearchFacade.getSearchResult(request, 1L);
    //then
    WireMock.verify(postRequestedFor(urlEqualTo(
        "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
        .withRequestBody(equalToJson(FileUtil.getStringFrom("queries/es/request/global-search-es-query-read-all-false-query-company.json"))));
    Mockito.verify(companyService, times(1))
        .getAccessSummary("READ");
    String expectedResponse = new ObjectMapper().writeValueAsString(searchResult);
    JSONAssert.assertEquals(FileUtil.getStringFrom("queries/es/response/global-search-content-response-company.json"),
        expectedResponse, JSONCompareMode.LENIENT);
  }


  @Test
  public void givenSearchRequestWithMaskedFieldsForCompany_shouldReturnMaskedResponse() throws IOException, JSONException {
    //given
    Long loggedInUserId = 1L;
    Long tenantId = 2L;
    SearchRequest request = new SearchRequest("query", Collections.singletonList(COMPANY));
    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.getUserId()).willReturn(loggedInUserId);
    given(userFacade.hasReadAllPermissionAction("company")).willReturn(false);
    String companyGlobal =
        FileUtil.getStringFrom("queries/es/response/global-search-company-aggregated-results-tenant-2.json");
    given(companyService.getAccessSummary("READ")).willReturn(
        new EntityAccessDTO(
            Collections.singletonList(1L), Collections.singletonList(2L)
        ));

    List<FieldResponse> fields = new ArrayList<>();
    FieldResponse phoneNumberField = new FieldResponse();
    phoneNumberField.setName("phoneNumbers");
    fields.add(phoneNumberField);
    given(companyService.getMaskedFields()).willReturn(fields);

    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                WireMock.aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(companyGlobal)));
    //when
    SearchResponse searchResult = globalSearchFacade.getSearchResult(request, 1L);
    //then
    WireMock.verify(postRequestedFor(urlEqualTo(
        "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
        .withRequestBody(equalToJson(FileUtil.getStringFrom("queries/es/request/global-search-es-query-read-all-false-query-company.json"))));
    Mockito.verify(companyService, times(1))
        .getAccessSummary("READ");
    String expectedResponse = new ObjectMapper().writeValueAsString(searchResult);
    JSONAssert.assertEquals(FileUtil.getStringFrom("queries/es/response/global-search-content-response-company-masked.json"),
        expectedResponse, JSONCompareMode.LENIENT);
  }

  @Test
  public void givenSearchRequestWithReadAllTrueForTask_shouldReturnResponse() throws IOException, JSONException {
    //given
    Long loggedInUserId = 1L;
    Long tenantId = 2L;
    SearchRequest request = new SearchRequest("query", Collections.singletonList(TASK));
    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.getUserId()).willReturn(loggedInUserId);
    given(userFacade.hasReadAllPermissionAction("task")).willReturn(false);
    String taskGlobalResponse =
        FileUtil.getStringFrom("queries/es/response/global-search-task-aggregated-results-tenant-2.json");
    given(taskService.resolveAccessSummary(any())).willReturn(new AccessSummaryResponse());
    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                WireMock.aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(taskGlobalResponse)));
    //when
    SearchResponse searchResult = globalSearchFacade.getSearchResult(request, 1L);
    //then
    WireMock.verify(postRequestedFor(urlEqualTo(
        "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
        .withRequestBody(equalToJson(FileUtil.getStringFrom("queries/es/request/global-search-es-query-request-for-task-global-search.json"))));

    String expectedResponse = new ObjectMapper().writeValueAsString(searchResult);
    JSONAssert.assertEquals(FileUtil.getStringFrom("queries/es/response/global-search-content-response-task.json"),
        expectedResponse, JSONCompareMode.LENIENT);
  }

  @Test
  public void givenSearchRequestWithReadAllFalseForTask_shouldReturnResponse() throws IOException, JSONException {
    //given
    Long loggedInUserId = 1L;
    Long tenantId = 2L;
    SearchRequest request = new SearchRequest("query", Collections.singletonList(TASK));
    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.getUserId()).willReturn(loggedInUserId);
    given(userFacade.hasReadAllPermissionAction("task")).willReturn(false);
    String taskGlobalResponse =
        FileUtil.getStringFrom("queries/es/response/global-search-task-aggregated-results-tenant-2.json");

    Map<Long, Action> accessDTOMap = new HashMap<>();
    accessDTOMap.put(1L, new Action());
    AccessSummary accessSummary = new AccessSummary(accessDTOMap, accessDTOMap);

    AccessSummaryResponse accessSummaryResponse = new AccessSummaryResponse(accessSummary, accessSummary, accessSummary, accessSummary);

    given(taskService.resolveAccessSummary(any())).willReturn(accessSummaryResponse);

    newEsWireMockRule.stubFor(
        WireMock.post(
                urlEqualTo(
                    "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                WireMock.aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(taskGlobalResponse)));
    //when
    SearchResponse searchResult = globalSearchFacade.getSearchResult(request, 1L);
    //then
    WireMock.verify(postRequestedFor(urlEqualTo(
        "/2-global-search/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
        .withRequestBody(equalToJson(FileUtil.getStringFrom("queries/es/request/global-search-es-query-request-for-task-read-false.json"))));

    String expectedResponse = new ObjectMapper().writeValueAsString(searchResult);
    JSONAssert.assertEquals(FileUtil.getStringFrom("queries/es/response/global-search-content-response-task.json"),
        expectedResponse, JSONCompareMode.LENIENT);
  }

}