package com.sell.search.query.operator;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;

import com.sell.search.search.core.querybuilder.model.IRule;
import com.sell.search.search.core.querybuilder.model.JsonRule;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.elasticsearch.index.query.AbstractQueryBuilder;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.NestedQueryBuilder;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Test;

public class CombinedCampaignActivitiesRuleParserTest {

  private final CombinedCampaignActivitiesRuleParser parser = new CombinedCampaignActivitiesRuleParser();

  private JsonRule createCampaignActivitiesRule(String operator, Object value) {
    JsonRule rule = new JsonRule();
    rule.setField("campaignActivities");
    rule.setOperator(operator);
    rule.setValue(value);
    rule.setId("campaignActivities");
    rule.setType("long");
    return rule;
  }

  private JsonRule createActivitiesRule(String operator, Object value) {
    JsonRule rule = new JsonRule();
    rule.setField("activities");
    rule.setOperator(operator);
    rule.setValue(value);
    rule.setId("activities");
    rule.setType("long");
    return rule;
  }

  private JsonRule createCombinedRule(JsonRule campaignRule, JsonRule activitiesRule) {
    Map<String, JsonRule> data = new HashMap<>();
    data.put("campaignActivities", campaignRule);
    data.put("activities", activitiesRule);

    JsonRule combinedRule = new JsonRule();
    combinedRule.setField("campaignActivities_with_activities");
    combinedRule.setOperator("combined");
    combinedRule.setNested(true);
    combinedRule.setData(data);

    return combinedRule;
  }

  // ========== EQUAL OPERATOR TESTS ==========

  @Test
  public void givenCombinedRuleWithEqualOperators_shouldReturnNestedQuery() throws JSONException {
    // given
    JsonRule campaignRule = createCampaignActivitiesRule("equal", 7409L);
    JsonRule activitiesRule = createActivitiesRule("equal", 46238L);
    JsonRule combinedRule = createCombinedRule(campaignRule, activitiesRule);

    // when
    AbstractQueryBuilder query = parser.parse(combinedRule, null);

    // then
    assertThat(query).isInstanceOf(NestedQueryBuilder.class);

    JSONObject queryJson = new JSONObject(query.toString());
    JSONObject nested = queryJson.getJSONObject("nested");

    assertThat(nested.getString("path")).isEqualTo("campaignActivities");

    // Verify the bool query structure
    JSONObject boolQuery = nested.getJSONObject("query").getJSONObject("bool");
    assertThat(boolQuery.has("must")).isTrue();

    JSONArray mustArray = boolQuery.getJSONArray("must");
    assertThat(mustArray.length()).isEqualTo(2);

    // Verify campaignActivities term query
    JSONObject campaignTerm = mustArray.getJSONObject(0).getJSONObject("term");
    assertThat(campaignTerm.has("campaignActivities.id")).isTrue();
    assertThat(campaignTerm.getJSONObject("campaignActivities.id").getLong("value")).isEqualTo(7409L);

    // Verify activities nested query
    JSONObject activitiesNested = mustArray.getJSONObject(1).getJSONObject("nested");
    assertThat(activitiesNested.getString("path")).isEqualTo("campaignActivities.activities");

    JSONObject activitiesTerm = activitiesNested.getJSONObject("query").getJSONObject("term");
    assertThat(activitiesTerm.has("campaignActivities.activities.id")).isTrue();
    assertThat(activitiesTerm.getJSONObject("campaignActivities.activities.id").getLong("value")).isEqualTo(46238L);
  }

  // ========== IN OPERATOR TESTS ==========

  @Test
  public void givenCombinedRuleWithInOperators_shouldReturnNestedQuery() throws JSONException {
    // given
    List<Long> campaignIds = Arrays.asList(7409L, 7410L, 7411L);
    List<Long> activityIds = Arrays.asList(46238L, 46239L, 46240L);

    JsonRule campaignRule = createCampaignActivitiesRule("in", campaignIds);
    JsonRule activitiesRule = createActivitiesRule("in", activityIds);
    JsonRule combinedRule = createCombinedRule(campaignRule, activitiesRule);

    // when
    AbstractQueryBuilder query = parser.parse(combinedRule, null);

    // then
    assertThat(query).isInstanceOf(NestedQueryBuilder.class);

    JSONObject queryJson = new JSONObject(query.toString());
    JSONObject nested = queryJson.getJSONObject("nested");

    assertThat(nested.getString("path")).isEqualTo("campaignActivities");

    // Verify the bool query structure
    JSONObject boolQuery = nested.getJSONObject("query").getJSONObject("bool");
    assertThat(boolQuery.has("must")).isTrue();

    JSONArray mustArray = boolQuery.getJSONArray("must");
    assertThat(mustArray.length()).isEqualTo(2);

    // Verify campaignActivities terms query
    JSONObject campaignTerms = mustArray.getJSONObject(0).getJSONObject("terms");
    assertThat(campaignTerms.has("campaignActivities.id")).isTrue();
    JSONArray campaignValues = campaignTerms.getJSONArray("campaignActivities.id");
    assertThat(campaignValues.length()).isEqualTo(3);
    assertThat(campaignValues.getLong(0)).isEqualTo(7409L);
    assertThat(campaignValues.getLong(1)).isEqualTo(7410L);
    assertThat(campaignValues.getLong(2)).isEqualTo(7411L);

    // Verify activities nested query
    JSONObject activitiesNested = mustArray.getJSONObject(1).getJSONObject("nested");
    assertThat(activitiesNested.getString("path")).isEqualTo("campaignActivities.activities");

    JSONObject activitiesTerms = activitiesNested.getJSONObject("query").getJSONObject("terms");
    assertThat(activitiesTerms.has("campaignActivities.activities.id")).isTrue();
    JSONArray activityValues = activitiesTerms.getJSONArray("campaignActivities.activities.id");
    assertThat(activityValues.length()).isEqualTo(3);
    assertThat(activityValues.getLong(0)).isEqualTo(46238L);
    assertThat(activityValues.getLong(1)).isEqualTo(46239L);
    assertThat(activityValues.getLong(2)).isEqualTo(46240L);
  }

  // ========== NOT_EQUAL OPERATOR TESTS ==========

  @Test
  public void givenCombinedRuleWithNotEqualOperators_shouldReturnNestedQuery() throws JSONException {
    // given
    JsonRule campaignRule = createCampaignActivitiesRule("not_equal", 7409L);
    JsonRule activitiesRule = createActivitiesRule("not_equal", 46238L);
    JsonRule combinedRule = createCombinedRule(campaignRule, activitiesRule);

    // when
    AbstractQueryBuilder query = parser.parse(combinedRule, null);

    // then
    assertThat(query).isInstanceOf(NestedQueryBuilder.class);

    JSONObject queryJson = new JSONObject(query.toString());
    JSONObject nested = queryJson.getJSONObject("nested");

    assertThat(nested.getString("path")).isEqualTo("campaignActivities");

    // Verify the bool query structure with mustNot clauses
    JSONObject boolQuery = nested.getJSONObject("query").getJSONObject("bool");
    assertThat(boolQuery.has("must_not")).isTrue();

    JSONArray mustNotArray = boolQuery.getJSONArray("must_not");
    assertThat(mustNotArray.length()).isEqualTo(2);

    // Verify campaignActivities mustNot term query
    JSONObject campaignTerm = mustNotArray.getJSONObject(0).getJSONObject("term");
    assertThat(campaignTerm.has("campaignActivities.id")).isTrue();
    assertThat(campaignTerm.getJSONObject("campaignActivities.id").getLong("value")).isEqualTo(7409L);

    // Verify activities mustNot nested query
    JSONObject activitiesNested = mustNotArray.getJSONObject(1).getJSONObject("nested");
    assertThat(activitiesNested.getString("path")).isEqualTo("campaignActivities.activities");

    JSONObject activitiesTerm = activitiesNested.getJSONObject("query").getJSONObject("term");
    assertThat(activitiesTerm.has("campaignActivities.activities.id")).isTrue();
    assertThat(activitiesTerm.getJSONObject("campaignActivities.activities.id").getLong("value")).isEqualTo(46238L);
  }

  // ========== NOT_IN OPERATOR TESTS ==========

  @Test
  public void givenCombinedRuleWithNotInOperators_shouldReturnNestedQuery() throws JSONException {
    // given
    List<Long> campaignIds = Arrays.asList(7409L, 7410L);
    List<Long> activityIds = Arrays.asList(46238L, 46239L);

    JsonRule campaignRule = createCampaignActivitiesRule("not_in", campaignIds);
    JsonRule activitiesRule = createActivitiesRule("not_in", activityIds);
    JsonRule combinedRule = createCombinedRule(campaignRule, activitiesRule);

    // when
    AbstractQueryBuilder query = parser.parse(combinedRule, null);

    // then
    assertThat(query).isInstanceOf(NestedQueryBuilder.class);

    JSONObject queryJson = new JSONObject(query.toString());
    JSONObject nested = queryJson.getJSONObject("nested");

    assertThat(nested.getString("path")).isEqualTo("campaignActivities");

    // Verify the bool query structure with mustNot clauses
    JSONObject boolQuery = nested.getJSONObject("query").getJSONObject("bool");
    assertThat(boolQuery.has("must_not")).isTrue();

    JSONArray mustNotArray = boolQuery.getJSONArray("must_not");
    assertThat(mustNotArray.length()).isEqualTo(2);
  }

  // ========== MIXED OPERATOR TESTS ==========

  @Test
  public void givenCombinedRuleWithEqualCampaignAndInActivities_shouldReturnNestedQuery() throws JSONException {
    // given
    JsonRule campaignRule = createCampaignActivitiesRule("equal", 7409L);
    JsonRule activitiesRule = createActivitiesRule("in", Arrays.asList(46238L, 46239L, 46240L));
    JsonRule combinedRule = createCombinedRule(campaignRule, activitiesRule);

    // when
    AbstractQueryBuilder query = parser.parse(combinedRule, null);

    // then
    assertThat(query).isInstanceOf(NestedQueryBuilder.class);

    JSONObject queryJson = new JSONObject(query.toString());
    JSONObject nested = queryJson.getJSONObject("nested");

    assertThat(nested.getString("path")).isEqualTo("campaignActivities");

    // Check that it contains must clauses for both conditions
    JSONObject boolQuery = nested.getJSONObject("query").getJSONObject("bool");
    assertThat(boolQuery.has("must")).isTrue();

    JSONArray mustArray = boolQuery.getJSONArray("must");
    assertThat(mustArray.length()).isEqualTo(2);

    // Verify campaignActivities equal term
    JSONObject campaignTerm = mustArray.getJSONObject(0).getJSONObject("term");
    assertThat(campaignTerm.getJSONObject("campaignActivities.id").getLong("value")).isEqualTo(7409L);

    // Verify activities IN terms
    JSONObject activitiesNested = mustArray.getJSONObject(1).getJSONObject("nested");
    assertThat(activitiesNested.getString("path")).isEqualTo("campaignActivities.activities");
    JSONObject activitiesTerms = activitiesNested.getJSONObject("query").getJSONObject("terms");
    JSONArray activityValues = activitiesTerms.getJSONArray("campaignActivities.activities.id");
    assertThat(activityValues.length()).isEqualTo(3);
  }

  @Test
  public void givenCombinedRuleWithInCampaignAndNotEqualActivities_shouldReturnNestedQuery() throws JSONException {
    // given
    JsonRule campaignRule = createCampaignActivitiesRule("in", Arrays.asList(7409L, 7410L));
    JsonRule activitiesRule = createActivitiesRule("not_equal", 46238L);
    JsonRule combinedRule = createCombinedRule(campaignRule, activitiesRule);

    // when
    AbstractQueryBuilder query = parser.parse(combinedRule, null);

    // then
    assertThat(query).isInstanceOf(NestedQueryBuilder.class);

    JSONObject queryJson = new JSONObject(query.toString());
    JSONObject nested = queryJson.getJSONObject("nested");

    assertThat(nested.getString("path")).isEqualTo("campaignActivities");

    // Check that it contains both must and mustNot clauses
    JSONObject boolQuery = nested.getJSONObject("query").getJSONObject("bool");
    assertThat(boolQuery.has("must")).isTrue();
    assertThat(boolQuery.has("must_not")).isTrue();

    // Verify must clause for campaignActivities IN
    JSONArray mustArray = boolQuery.getJSONArray("must");
    assertThat(mustArray.length()).isEqualTo(1);
    JSONObject campaignTerms = mustArray.getJSONObject(0).getJSONObject("terms");
    JSONArray campaignValues = campaignTerms.getJSONArray("campaignActivities.id");
    assertThat(campaignValues.length()).isEqualTo(2);

    // Verify mustNot clause for activities NOT_EQUAL
    JSONArray mustNotArray = boolQuery.getJSONArray("must_not");
    assertThat(mustNotArray.length()).isEqualTo(1);
    JSONObject activitiesNested = mustNotArray.getJSONObject(0).getJSONObject("nested");
    assertThat(activitiesNested.getString("path")).isEqualTo("campaignActivities.activities");
  }

  // ========== REAL-WORLD SCENARIO TESTS ==========

  @Test
  public void givenRealWorldScenario_campaignEmailAndCallActivities_shouldReturnCorrectQuery() throws JSONException {
    // given - Real scenario: Find leads in email campaign 12345 with call activities 98765, 98766
    JsonRule campaignRule = createCampaignActivitiesRule("equal", 12345L);
    JsonRule activitiesRule = createActivitiesRule("in", Arrays.asList(98765L, 98766L));
    JsonRule combinedRule = createCombinedRule(campaignRule, activitiesRule);

    // when
    AbstractQueryBuilder query = parser.parse(combinedRule, null);

    // then
    JSONObject queryJson = new JSONObject(query.toString());
    JSONObject nested = queryJson.getJSONObject("nested");
    JSONObject boolQuery = nested.getJSONObject("query").getJSONObject("bool");
    JSONArray mustArray = boolQuery.getJSONArray("must");

    // Verify campaign ID
    JSONObject campaignTerm = mustArray.getJSONObject(0).getJSONObject("term");
    assertThat(campaignTerm.getJSONObject("campaignActivities.id").getLong("value")).isEqualTo(12345L);

    // Verify activity IDs
    JSONObject activitiesNested = mustArray.getJSONObject(1).getJSONObject("nested");
    JSONObject activitiesTerms = activitiesNested.getJSONObject("query").getJSONObject("terms");
    JSONArray activityValues = activitiesTerms.getJSONArray("campaignActivities.activities.id");
    assertThat(activityValues.getLong(0)).isEqualTo(98765L);
    assertThat(activityValues.getLong(1)).isEqualTo(98766L);
  }

  @Test
  public void givenRealWorldScenario_excludeSpecificCampaignAndActivities_shouldReturnCorrectQuery() throws JSONException {
    // given - Real scenario: Exclude leads from campaigns 11111, 22222 and exclude activities 55555
    JsonRule campaignRule = createCampaignActivitiesRule("not_in", Arrays.asList(11111L, 22222L));
    JsonRule activitiesRule = createActivitiesRule("not_equal", 55555L);
    JsonRule combinedRule = createCombinedRule(campaignRule, activitiesRule);

    // when
    AbstractQueryBuilder query = parser.parse(combinedRule, null);

    // then
    JSONObject queryJson = new JSONObject(query.toString());
    JSONObject nested = queryJson.getJSONObject("nested");
    JSONObject boolQuery = nested.getJSONObject("query").getJSONObject("bool");

    assertThat(boolQuery.has("must_not")).isTrue();
    JSONArray mustNotArray = boolQuery.getJSONArray("must_not");
    assertThat(mustNotArray.length()).isEqualTo(2);

    // Verify excluded campaign IDs
    JSONObject campaignTerms = mustNotArray.getJSONObject(0).getJSONObject("terms");
    JSONArray campaignValues = campaignTerms.getJSONArray("campaignActivities.id");
    assertThat(campaignValues.getLong(0)).isEqualTo(11111L);
    assertThat(campaignValues.getLong(1)).isEqualTo(22222L);

    // Verify excluded activity ID
    JSONObject activitiesNested = mustNotArray.getJSONObject(1).getJSONObject("nested");
    JSONObject activitiesTerm = activitiesNested.getJSONObject("query").getJSONObject("term");
    assertThat(activitiesTerm.getJSONObject("campaignActivities.activities.id").getLong("value")).isEqualTo(55555L);
  }

  // ========== VALIDATION TESTS ==========

  @Test
  public void givenRuleWithValidOperatorAndField_canParse_shouldReturnTrue() {
    // given
    IRule rule = new JsonRule();
    rule.setField("campaignActivities_with_activities");
    rule.setOperator("combined_campaign_activities");

    // when
    boolean canParse = parser.canParse(rule);

    // then
    assertThat(canParse).isTrue();
  }

  @Test
  public void givenRuleWithInvalidOperator_canParse_shouldReturnFalse() {
    // given
    IRule rule = new JsonRule();
    rule.setField("campaignActivities_with_activities");
    rule.setOperator("equal");

    // when
    boolean canParse = parser.canParse(rule);

    // then
    assertThat(canParse).isFalse();
  }

  @Test
  public void givenRuleWithInvalidField_canParse_shouldReturnFalse() {
    // given
    IRule rule = new JsonRule();
    rule.setField("campaignActivities");
    rule.setOperator("combined");

    // when
    boolean canParse = parser.canParse(rule);

    // then
    assertThat(canParse).isFalse();
  }

  // ========== ERROR HANDLING TESTS ==========

  @Test
  public void givenRuleWithInvalidData_parse_shouldThrowException() {
    // given
    JsonRule rule = new JsonRule();
    rule.setField("campaignActivities_with_activities");
    rule.setOperator("combined");
    rule.setData("invalid_data_string");

    // when + then
    assertThatThrownBy(() -> parser.parse(rule, null))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessageContaining("Invalid campaign activity combined rule data");
  }

  @Test
  public void givenRuleWithMissingCampaignData_parse_shouldThrowException() {
    // given
    Map<String, JsonRule> data = new HashMap<>();
    data.put("activities", createActivitiesRule("equal", 46238L));
    // Missing campaignActivities data

    JsonRule rule = new JsonRule();
    rule.setField("campaignActivities_with_activities");
    rule.setOperator("combined");
    rule.setData(data);

    // when + then
    assertThatThrownBy(() -> parser.parse(rule, null))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessageContaining("Invalid campaign activity combined rule data");
  }

  @Test
  public void givenRuleWithMissingActivitiesData_parse_shouldThrowException() {
    // given
    Map<String, JsonRule> data = new HashMap<>();
    data.put("campaignActivities", createCampaignActivitiesRule("equal", 7409L));
    // Missing activities data

    JsonRule rule = new JsonRule();
    rule.setField("campaignActivities_with_activities");
    rule.setOperator("combined");
    rule.setData(data);

    // when + then
    assertThatThrownBy(() -> parser.parse(rule, null))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessageContaining("Invalid campaign activity combined rule data");
  }

  @Test
  public void givenRuleWithNullData_parse_shouldThrowException() {
    // given
    JsonRule rule = new JsonRule();
    rule.setField("campaignActivities_with_activities");
    rule.setOperator("combined");
    rule.setData(null);

    // when + then
    assertThatThrownBy(() -> parser.parse(rule, null))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessageContaining("Invalid campaign activity combined rule data");
  }

  @Test
  public void givenRuleWithEmptyData_parse_shouldThrowException() {
    // given
    Map<String, JsonRule> data = new HashMap<>();
    // Empty data map

    JsonRule rule = new JsonRule();
    rule.setField("campaignActivities_with_activities");
    rule.setOperator("combined");
    rule.setData(data);

    // when + then
    assertThatThrownBy(() -> parser.parse(rule, null))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessageContaining("Invalid campaign activity combined rule data");
  }

  @Test
  public void givenLargeValueArrays_shouldHandleCorrectly() throws JSONException {
    // given - Test with large arrays to ensure performance
    List<Long> largeCampaignIds = Arrays.asList(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L, 10L);
    List<Long> largeActivityIds = Arrays.asList(101L, 102L, 103L, 104L, 105L, 106L, 107L, 108L, 109L, 110L);

    JsonRule campaignRule = createCampaignActivitiesRule("in", largeCampaignIds);
    JsonRule activitiesRule = createActivitiesRule("in", largeActivityIds);
    JsonRule combinedRule = createCombinedRule(campaignRule, activitiesRule);

    // when
    AbstractQueryBuilder query = parser.parse(combinedRule, null);

    // then
    assertThat(query).isInstanceOf(NestedQueryBuilder.class);

    JSONObject queryJson = new JSONObject(query.toString());
    JSONObject nested = queryJson.getJSONObject("nested");
    JSONObject boolQuery = nested.getJSONObject("query").getJSONObject("bool");
    JSONArray mustArray = boolQuery.getJSONArray("must");

    // Verify both arrays are properly handled
    JSONObject campaignTerms = mustArray.getJSONObject(0).getJSONObject("terms");
    JSONArray campaignValues = campaignTerms.getJSONArray("campaignActivities.id");
    assertThat(campaignValues.length()).isEqualTo(10);

    JSONObject activitiesNested = mustArray.getJSONObject(1).getJSONObject("nested");
    JSONObject activitiesTerms = activitiesNested.getJSONObject("query").getJSONObject("terms");
    JSONArray activityValues = activitiesTerms.getJSONArray("campaignActivities.activities.id");
    assertThat(activityValues.length()).isEqualTo(10);
  }
}
