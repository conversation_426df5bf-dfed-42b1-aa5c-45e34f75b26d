{"bool": {"should": [{"query_string": {"query": "*<PERSON>*", "fields": ["name^1.0"], "type": "best_fields", "default_operator": "or", "max_determinized_states": 10000, "enable_position_increments": true, "fuzziness": "0", "fuzzy_prefix_length": 0, "fuzzy_max_expansions": 50, "phrase_slop": 0, "escape": false, "auto_generate_synonyms_phrase_query": true, "fuzzy_transpositions": true, "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}