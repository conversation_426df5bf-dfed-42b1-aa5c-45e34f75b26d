{"content": [{"entityType": "LEAD", "last": false, "totalPages": 8454, "totalElements": 42267, "sort": [{"direction": "DESC", "property": "updatedAt"}], "first": true, "numberOfElements": 5, "size": 5, "number": 0, "values": [{"emails": [{"value": "<EMAIL>"}, {"value": "<EMAIL>"}], "firstName": "Try", "lastName": "wF_2199", "companyName": null, "id": 332486, "ownerId": 7847, "phoneNumbers": [{"dialCode": "+91", "value": "**********"}, {"dialCode": "+91", "value": "**********"}]}, {"emails": null, "firstName": "Try", "lastName": "wF_2199", "companyName": null, "id": 332485, "ownerId": 7847, "phoneNumbers": null}, {"emails": null, "firstName": "Try", "lastName": "wF_2199", "companyName": null, "id": 332484, "ownerId": 7847, "phoneNumbers": null}, {"emails": null, "firstName": "Try", "lastName": "wF_2199", "companyName": null, "id": 332478, "ownerId": 7847, "phoneNumbers": null}, {"emails": null, "firstName": "Try", "lastName": "wF_2199", "companyName": null, "id": 332480, "ownerId": 7847, "phoneNumbers": null}]}, {"entityType": "DEAL", "last": false, "totalPages": 8454, "totalElements": 42267, "sort": [{"direction": "DESC", "property": "updatedAt"}], "first": true, "numberOfElements": 5, "size": 5, "number": 0, "values": [{"associatedContacts": [], "name": "Deal By Lead Id 204290", "company": null, "id": 23595, "ownerBy": {"id": 7, "name": "<PERSON>"}, "pipelineStage": {"name": "Closed Unqualified", "id": 7}, "products": [{"quantity": 1, "price": {"currencyId": 431, "value": 300.46}, "name": "CRM", "discount": {"type": "FIXED", "value": 300.46}, "id": 5}]}, {"associatedContacts": [{"name": "Hello hello again", "id": 9828}], "name": "Deal 23560", "company": null, "id": 23560, "ownerBy": {"id": 7, "name": "<PERSON>"}, "pipelineStage": {"name": "Closed Unqualified", "id": 7}, "products": [{"quantity": 1, "price": {"currencyId": 431, "value": 0}, "name": "CRM", "discount": {"type": "FIXED", "value": 0}, "id": 5}]}, {"associatedContacts": [{"name": "Hello hello again", "id": 9828}], "name": "my-deal with products", "company": null, "id": 23559, "ownerBy": {"id": 7, "name": "<PERSON>"}, "pipelineStage": {"name": "Closed Unqualified", "id": 7}, "products": [{"quantity": 1, "price": {"currencyId": 431, "value": 1500.23}, "name": "CRM", "discount": {"type": "FIXED", "value": 1500.23}, "id": 5}]}, {"associatedContacts": [{"name": "Hello hello again", "id": 9828}], "name": "my-deal2", "company": null, "id": 23553, "ownerBy": {"id": 7, "name": "<PERSON>"}, "pipelineStage": {"name": "Closed Unqualified", "id": 7}, "products": [{"quantity": 1, "price": {"currencyId": 431, "value": 300.46}, "name": "CRM", "discount": {"type": "FIXED", "value": 30}, "id": 5}]}, {"associatedContacts": [{"name": "Hello hello again", "id": 9828}], "name": "my-deal2", "company": null, "id": 23512, "ownerBy": {"id": 7, "name": "<PERSON>"}, "pipelineStage": {"name": "Closed Unqualified", "id": 7}, "products": [{"quantity": 1, "price": {"currencyId": 431, "value": 0}, "name": "CRM", "discount": {"type": "FIXED", "value": 0}, "id": 5}]}]}, {"entityType": "CONACT", "last": false, "totalPages": 8454, "totalElements": 42267, "sort": [{"direction": "DESC", "property": "updatedAt"}], "first": true, "numberOfElements": 5, "size": 5, "number": 0, "values": [{"id": 332486, "ownedBy": {"id": 7, "name": "tony <PERSON>"}, "firstName": "<PERSON>", "lastName": "<PERSON>", "department": "Product Engineering", "designation": "Sr.Engineer", "phoneNumbers": [{"dialCode": "+91", "value": "**********"}, {"dialCode": "+91", "value": "**********"}], "emails": [{"value": "<EMAIL>"}, {"value": "<EMAIL>"}]}, {"id": 332487, "ownedBy": {"id": 7, "name": "tony <PERSON>"}, "firstName": "<PERSON>", "lastName": "<PERSON>", "department": "Product Engineering", "designation": "Sr.Engineer", "phoneNumbers": null, "emails": [{"value": "<EMAIL>"}, {"value": "<EMAIL>"}]}, {"id": 332488, "ownedBy": {"id": 7, "name": "tony <PERSON>"}, "firstName": "<PERSON>", "lastName": "<PERSON>", "department": "Product Engineering", "designation": "Sr.Engineer", "phoneNumbers": [{"dialCode": "+91", "value": "**********"}, {"dialCode": "+91", "value": "**********"}], "emails": null}, {"id": 332489, "ownedBy": {"id": 7, "name": "tony <PERSON>"}, "firstName": "<PERSON>", "lastName": "<PERSON>", "department": "Product Engineering", "designation": null, "phoneNumbers": [{"dialCode": "+91", "value": "**********"}, {"dialCode": "+91", "value": "**********"}], "emails": [{"value": "<EMAIL>"}, {"value": "<EMAIL>"}]}, {"id": 332490, "ownedBy": {"id": 7, "name": "tony <PERSON>"}, "firstName": null, "lastName": "<PERSON>", "department": "Product Engineering", "designation": "Sr.Engineer", "phoneNumbers": [{"dialCode": "+91", "value": "**********"}, {"dialCode": "+91", "value": "**********"}], "emails": [{"value": "<EMAIL>"}, {"value": "<EMAIL>"}]}]}, {"entityType": "COMPANY", "last": true, "totalPages": 1, "totalElements": 1, "sort": [{"direction": "DESC", "property": "updatedAt"}], "first": true, "numberOfElements": 1, "size": 5, "number": 0, "values": [{"id": 332486, "ownedBy": {"id": 7, "name": "tony <PERSON>"}, "name": "K2V2 Technologies", "industry": {"id": 5, "name": "Software"}, "businessType": {"id": 111, "name": "Service Provider"}, "phoneNumbers": [{"dialCode": "+91", "value": "**********"}, {"dialCode": "+91", "value": "**********"}], "emails": [{"value": "<EMAIL>"}, {"value": "<EMAIL>"}]}]}], "last": false, "totalPages": 1382, "sort": [{"direction": "DESC", "property": "updatedAt"}], "first": true, "numberOfElements": 5, "size": 40, "number": 0}