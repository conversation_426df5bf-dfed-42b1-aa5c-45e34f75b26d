# SD-Search Service

A comprehensive search microservice built with Spring Boot that provides advanced search capabilities for CRM entities including Deals, Companies, Contacts, and Leads using Elasticsearch as the search engine.

## 📋 Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Technology Stack](#technology-stack)
- [Features](#features)
- [API Documentation](#api-documentation)
- [Database Schema](#database-schema)
- [Deployment](#deployment)
- [Development Setup](#development-setup)
- [Configuration](#configuration)
- [Monitoring](#monitoring)

## 🔍 Overview

SD-Search is a microservice that provides powerful search functionality for a CRM system. It indexes and searches across multiple entity types including:

- **Deals** - Sales opportunities with pipeline stages, values, and associated contacts
- **Companies** - Business entities with contact information, industry data, and revenue details
- **Contacts** - Individual contacts with personal and professional information
- **Leads** - Potential customers with qualification and conversion tracking

The service uses Elasticsearch for fast, scalable search operations and PostgreSQL for metadata and configuration storage.

## 🏗️ Architecture

### High-Level Architecture

The SD-Search service follows a microservices architecture pattern with clear separation of concerns:

```mermaid
graph TB
    subgraph "Client Layer"
        UI[Web UI]
        API[API Clients]
        MOBILE[Mobile Apps]
    end

    subgraph "API Gateway"
        GW[Gateway/Load Balancer]
    end

    subgraph "SD-Search Service"
        CTRL[Controllers]
        SVC[Search Service]
        GLOBAL[Global Search]
        LOOKUP[Lookup Service]

        subgraph "Core Components"
            QB[Query Builder]
            ES_CLIENT[Elasticsearch Client]
            CACHE[Memcached]
            SEC[Security/JWT]
        end
    end

    subgraph "External Services"
        IAM[IAM Service]
        ENTITY[Entity Service]
        COMPANY_SVC[Company Service]
        DEAL_SVC[Deal Service]
        PRODUCTIVITY[Productivity Service]
    end

    subgraph "Data Layer"
        ES[(Elasticsearch)]
        PG[(PostgreSQL)]
        RABBIT[RabbitMQ]
    end

    UI --> GW
    API --> GW
    MOBILE --> GW

    GW --> CTRL
    CTRL --> SVC
    CTRL --> GLOBAL
    CTRL --> LOOKUP

    SVC --> QB
    SVC --> ES_CLIENT
    SVC --> CACHE
    SVC --> SEC

    SVC --> IAM
    SVC --> ENTITY
    SVC --> COMPANY_SVC
    SVC --> DEAL_SVC
    SVC --> PRODUCTIVITY

    ES_CLIENT --> ES
    SVC --> PG
    SVC --> RABBIT
```

### Component Overview

- **Controllers**: REST API endpoints for search operations
- **Search Service**: Core business logic for search operations
- **Query Builder**: Elasticsearch query construction and optimization
- **Global Search**: Cross-entity search functionality
- **Lookup Service**: Fast entity lookup and autocomplete
- **Security**: JWT-based authentication and authorization

### Search Request Flow

```mermaid
sequenceDiagram
    participant Client
    participant Controller
    participant SearchService
    participant QueryBuilder
    participant Elasticsearch
    participant Cache
    participant ExternalAPI

    Client->>Controller: POST /v1/search/{entity}
    Controller->>SearchService: getSearchResults()

    SearchService->>ExternalAPI: getFields(entity)
    ExternalAPI-->>SearchService: Field definitions

    SearchService->>QueryBuilder: build(jsonRule, entityType)
    QueryBuilder-->>SearchService: SearchSourceBuilder

    SearchService->>Cache: checkCache(query)
    alt Cache Hit
        Cache-->>SearchService: Cached results
    else Cache Miss
        SearchService->>Elasticsearch: search(query)
        Elasticsearch-->>SearchService: Search results
        SearchService->>Cache: storeCache(results)
    end

    SearchService->>ExternalAPI: resolveIdNames(results)
    ExternalAPI-->>SearchService: Resolved entities

    SearchService-->>Controller: SearchResponse
    Controller-->>Client: JSON Response
```

## 🛠️ Technology Stack

### Backend Framework
- **Spring Boot 1.5.18** - Main application framework
- **Spring Security** - Authentication and authorization
- **Spring Data JPA** - Database abstraction layer
- **Spring Cloud OpenFeign** - Service-to-service communication

### Search & Data Storage
- **Elasticsearch 6.6.0** - Primary search engine
- **PostgreSQL** - Relational database for metadata
- **HikariCP** - Database connection pooling

### Caching & Messaging
- **Memcached** - Distributed caching layer
- **RabbitMQ** - Message queue for async processing

### Development & Testing
- **Maven** - Build and dependency management
- **JUnit & Mockito** - Unit testing framework
- **TestContainers** - Integration testing with containers
- **Flyway** - Database migration management

### Monitoring & Documentation
- **Micrometer + Prometheus** - Metrics collection
- **SpringDoc OpenAPI** - API documentation
- **Logback** - Structured logging

### Deployment & Infrastructure
- **Docker** - Containerization
- **Kubernetes/Helm** - Container orchestration
- **Jenkins** - CI/CD pipeline

## ✨ Features

### Core Search Capabilities
- **Advanced Query Builder** - Complex search queries with multiple filters and conditions
- **Full-Text Search** - Elasticsearch-powered text search across all entity fields
- **Faceted Search** - Filter by categories, dates, ranges, and custom fields
- **Auto-complete & Suggestions** - Real-time search suggestions and entity lookup
- **Global Search** - Cross-entity search across Deals, Companies, Contacts, and Leads

### Entity-Specific Features
- **Deal Search** - Pipeline stages, estimated values, closure dates, associated contacts
- **Company Search** - Industry, revenue, employee count, contact information
- **Contact Search** - Personal details, company associations, communication preferences
- **Lead Search** - Qualification status, source tracking, conversion metrics

### Advanced Functionality
- **Kanban View Support** - Specialized search for board-style interfaces
- **Pagination & Sorting** - Efficient result navigation with custom sorting
- **Field Masking** - Security-based field visibility control
- **Custom Fields** - Dynamic field support for extended entity properties
- **Share Rules** - Permission-based result filtering
- **Caching** - Memcached integration for improved performance

### Integration Features
- **Multi-tenant Support** - Isolated data access per tenant
- **External Service Integration** - Seamless communication with other microservices
- **Real-time Updates** - RabbitMQ-based event processing
- **Bulk Operations** - Efficient batch processing capabilities

## 📚 API Documentation

### Main Search Endpoints

#### Advanced Search
```http
POST /v1/search/{entity}
Content-Type: application/json

{
  "fields": ["name", "ownedBy", "estimatedValue"],
  "jsonRule": {
    "condition": "AND",
    "rules": [
      {
        "field": "name",
        "operator": "contains",
        "value": "acme"
      },
      {
        "field": "estimatedValue.value",
        "operator": "greater_than",
        "value": 10000
      }
    ]
  }
}
```

#### Free Text Search
```http
GET /v1/search/{entity}?text=search_term&view=integration
```

#### Kanban View Search
```http
POST /v1/search/{entity}?view=kanban&pipelineId=123
```

### Global Search Endpoints

#### Cross-Entity Search
```http
POST /v1/search/global-search
Content-Type: application/json

{
  "query": "acme corporation",
  "entities": ["DEAL", "COMPANY", "CONTACT"]
}
```

### Lookup Endpoints

#### Company Lookup
```http
GET /v1/search/companies/lookup?q=company_name
```

#### Entity Summaries
```http
GET /v1/summaries/users?id=1,2,3
GET /v1/summaries/deals?id=100,101,102
GET /v1/summaries/companies?id=50,51,52
```

### Supported Entities
- `deal` - Sales opportunities and pipeline management
- `company` - Business entities and organizations
- `contact` - Individual contacts and relationships
- `lead` - Potential customers and prospects
- `task` - Activities and to-do items
- `note` - Notes and documentation

## 🗄️ Database Schema

### Entity Relationship Diagram

```mermaid
erDiagram
    DEAL {
        bigint id PK
        varchar name
        date closedDate
        bigint accountId FK
        bigint pipeline
        bigint pipelineStage
        varchar pipelineStageReason
        varchar requirementCurrency
        double requirementBudget
        date expectedClosureOn
        date convertedAt
        bigint convertedBy
        bigint ownerId
        date createdAt
        date updatedAt
        bigint createdBy
        bigint updatedBy
    }

    COMPANY {
        bigint id PK
        varchar name
        jsonb logoUrls
        int numberOfEmployees
        double annualRevenue
        varchar website
        varchar industry
        varchar businessType
        jsonb phoneNumbers
        jsonb emails
        varchar timezone
        boolean dnd
        text address
        varchar city
        varchar state
        varchar zipcode
        varchar country
        text facebook
        text twitter
        text linkedIn
        bigint ownerId
        date createdAt
        date updatedAt
        bigint createdBy
        bigint updatedBy
    }

    LEAD {
        bigint id PK
        varchar firstName
        varchar lastName
        jsonb phoneNumbers
        bigint salutation
        jsonb emails
        varchar timezone
        varchar city
        varchar state
        varchar zipcode
        varchar country
        varchar department
        varchar designation
        varchar leadSource
        varchar leadStatus
        varchar industry
        varchar businessType
        date convertedAt
        bigint convertedBy
        bigint ownerId
        date createdAt
        date updatedAt
        bigint createdBy
        bigint updatedBy
    }

    CONTACT {
        bigint id PK
        varchar firstName
        varchar lastName
        jsonb phoneNumbers
        bigint salutation
        jsonb emails
        varchar timezone
        varchar city
        varchar state
        varchar zipcode
        varchar country
        varchar department
        varchar designation
        bigint companyId FK
        bigint ownerId
        date createdAt
        date updatedAt
        bigint createdBy
        bigint updatedBy
    }

    INDEX_MAPPING {
        bigint id PK
        varchar name
        jsonb mapping
        varchar status
        varchar target_index
    }

    DEAL ||--o{ COMPANY : "belongs to"
    CONTACT ||--o{ COMPANY : "works for"
    LEAD ||--o{ COMPANY : "potential customer"
```

### Key Database Tables

#### index_mapping
Stores Elasticsearch index mapping configurations for dynamic field management:
- **id**: Primary key
- **name**: Human-readable mapping name
- **mapping**: JSONB field containing Elasticsearch mapping definition
- **status**: Mapping status (INIT, ACTIVE, etc.)
- **target_index**: Target Elasticsearch index name

### Elasticsearch Index Structure

#### Deal Index Template
```json
{
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": "1",
    "analysis": {
      "normalizer": {
        "case_insensitive": {
          "filter": ["asciifolding", "lowercase"],
          "type": "custom"
        }
      }
    }
  },
  "mappings": {
    "properties": {
      "id": {"type": "long"},
      "name": {"type": "keyword", "normalizer": "case_insensitive"},
      "ownedBy": {
        "type": "nested",
        "properties": {
          "id": {"type": "long"},
          "name": {"type": "keyword", "normalizer": "case_insensitive"}
        }
      },
      "estimatedValue": {
        "type": "nested",
        "properties": {
          "currencyId": {"type": "long"},
          "value": {"type": "float"}
        }
      }
    }
  }
}
```

## 🚀 Deployment

### CI/CD Pipeline

```mermaid
graph LR
    subgraph "Source Control"
        GIT[Git Repository]
        PR[Pull Request]
        DEV[Dev Branch]
        MASTER[Master Branch]
    end

    subgraph "CI Pipeline"
        JENKINS[Jenkins]
        BUILD[Maven Build]
        TEST[Unit Tests]
        ITEST[Integration Tests]
        PACKAGE[Package JAR]
        DOCKER[Docker Build]
        REGISTRY[Docker Registry]
    end

    subgraph "Deployment Environments"
        QA[QA Environment]
        STAGE[Stage Environment]
        PROD[Production]
    end

    subgraph "Infrastructure"
        K8S[Kubernetes]
        HELM[Helm Charts]
        ES[Elasticsearch]
        PG[PostgreSQL]
        RABBIT[RabbitMQ]
    end

    GIT --> JENKINS
    PR --> JENKINS
    DEV --> JENKINS
    MASTER --> JENKINS

    JENKINS --> BUILD
    BUILD --> TEST
    TEST --> ITEST
    ITEST --> PACKAGE
    PACKAGE --> DOCKER
    DOCKER --> REGISTRY

    REGISTRY --> QA
    REGISTRY --> STAGE
    REGISTRY --> PROD

    QA --> K8S
    STAGE --> K8S
    PROD --> K8S

    K8S --> HELM
    HELM --> ES
    HELM --> PG
    HELM --> RABBIT
```

### Deployment Environments

#### QA Environment
- **Trigger**: Automatic deployment on `dev` branch commits
- **Purpose**: Development testing and feature validation
- **Resources**: Shared infrastructure with reduced capacity

#### Stage Environment
- **Trigger**: Manual approval for PR deployments to master
- **Purpose**: Pre-production testing and stakeholder review
- **Resources**: Production-like environment for final validation

#### Production Environment
- **Trigger**: Automatic deployment on `master` branch commits
- **Purpose**: Live customer-facing environment
- **Resources**: Full production capacity with high availability

### Infrastructure Components

#### Kubernetes Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sd-search
spec:
  replicas: 3
  selector:
    matchLabels:
      app: sd-search
  template:
    spec:
      containers:
      - name: sd-search
        image: nexus.sling-dev.com:8023/sling/sd-search:latest
        ports:
        - containerPort: 8083
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "PROD"
        - name: ELASTICSEARCH_HOST
          value: "elasticsearch-service"
        - name: POSTGRES_HOST
          value: "postgresql-service"
```

#### Docker Configuration
```dockerfile
FROM openjdk:8-jre-slim
RUN mkdir -p /usr/src/app/config/
COPY ./target/search-service-0.0.1-SNAPSHOT.jar /usr/src/app/app.jar
WORKDIR /usr/src/app
ENTRYPOINT ["java", "-jar", "app.jar"]
```

### Database Migrations
- **Flyway** manages database schema migrations
- **Versioned migrations** in `src/main/resources/db/migration/`
- **Automatic execution** during deployment process

## 💻 Development Setup

### Prerequisites
- **Java 8** or higher
- **Maven 3.6+** for build management
- **Docker & Docker Compose** for local infrastructure
- **Git** for version control

### Local Development Environment

#### 1. Clone the Repository
```bash
git clone <repository-url>
cd sd-search
```

#### 2. Start Infrastructure Services
```bash
# Start Elasticsearch, PostgreSQL, and RabbitMQ
docker-compose up -d elasticsearch-master
# Note: PostgreSQL and RabbitMQ services are commented out in docker-compose.yml
# You may need to start them separately or uncomment them
```

#### 3. Database Setup
```bash
# Create database
createdb search

# Run migrations (if flyway is enabled)
mvn flyway:migrate
```

#### 4. Build and Run
```bash
# Compile and run tests
mvn clean compile test

# Run integration tests
mvn clean verify -P integration-test

# Start the application
mvn spring-boot:run
```

#### 5. Verify Installation
```bash
# Check health endpoint
curl http://localhost:8083/actuator/health

# Check API documentation
curl http://localhost:8083/v2/api-docs
```

### IDE Setup
- **IntelliJ IDEA** or **Eclipse** recommended
- **Lombok plugin** required for annotation processing
- **MapStruct plugin** for mapper generation

### Testing
```bash
# Run unit tests only
mvn test

# Run integration tests only
mvn clean verify -P integration-test

# Run all tests
mvn clean verify
```

## ⚙️ Configuration

### Application Properties

#### Core Configuration
```properties
# Server Configuration
server.port=8083
server.max-http-header-size=36000

# Application Identity
spring.application.name=search
spring.profiles.active=DEV

# Database Configuration
spring.datasource.url=***************************************
spring.datasource.username=postgres
spring.datasource.password=password
spring.jpa.hibernate.ddl-auto=validate

# Elasticsearch Configuration
newElasticsearch.host=localhost
newElasticsearch.port=9400

# RabbitMQ Configuration
core.rabbitmq.host=localhost
core.rabbitmq.virtualHost=sling-sales
core.rabbitmq.port=5672
core.rabbitmq.username=test
core.rabbitmq.password=test
```

#### External Service Configuration
```properties
# Microservice Endpoints
client.entity.basePath=http://localhost:8086
client.iam.basePath=http://localhost:8081
client.company.basePath=http://sd-company
client.deal.basePath=http://sd-deal
client.productivity.basePath=http://sd-productivity

# Security
jwt.secret-key=your-secret-key

# Caching
memcached.host=localhost
memcached.memPort=11211
spring.cache.type=none
```

#### Search Configuration
```properties
# Pagination
max.page.size=1000

# Lookup Fields
search.lookup.fields.lead=firstName,lastName
search.lookup.fields.deal=name
search.lookup.fields.contact=firstName,lastName

# Health Check
health.tenantId=82
```

### Environment-Specific Configurations

#### Development (DEV)
- Local database and Elasticsearch
- Reduced security constraints
- Debug logging enabled
- Hot reload capabilities

#### QA Environment
- Shared test infrastructure
- Integration with test data
- Performance monitoring
- Automated testing

#### Production (PROD)
- High availability setup
- Enhanced security
- Comprehensive monitoring
- Backup and disaster recovery

## 📊 Monitoring

### Health Checks
```bash
# Application health
GET /actuator/health

# Detailed health information
GET /actuator/health?show-details=always

# Metrics endpoint
GET /actuator/metrics

# Prometheus metrics
GET /actuator/prometheus
```

### Key Metrics
- **Search Response Time** - Average query execution time
- **Elasticsearch Performance** - Index size, query latency, cluster health
- **Database Connections** - HikariCP pool metrics
- **Memory Usage** - JVM heap and non-heap memory
- **Cache Hit Ratio** - Memcached effectiveness
- **API Request Rate** - Requests per second by endpoint

### Logging Configuration
```xml
<!-- logback-spring.xml -->
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.contrib.jackson.JacksonJsonEncoder"/>
    </appender>

    <logger name="com.sell.search" level="INFO"/>
    <logger name="org.elasticsearch.client" level="WARN"/>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
```

### Alerting
- **High Error Rate** - >5% 5xx responses
- **Slow Response Time** - >2s average response time
- **Elasticsearch Down** - Cluster health red/yellow
- **Database Connection Issues** - Connection pool exhaustion
- **Memory Leaks** - Sustained high memory usage

## 🔧 Elasticsearch Templates

### Creating Index Templates

#### Deal Index Template
```bash
curl -H 'Content-Type: application/json' -XPOST \
  "http://<elasticsearch-host>:<port>/_template/deal-index-template" \
  -d '{
    "index_patterns": ["*-deal"],
    "settings": {
      "number_of_shards": 2,
      "number_of_replicas": "1",
      "analysis": {
        "normalizer": {
          "case_insensitive": {
            "filter": ["asciifolding", "lowercase"],
            "type": "custom"
          }
        }
      }
    },
    "mappings": {
      "properties": {
        "id": {"type": "long"},
        "name": {"type": "keyword", "normalizer": "case_insensitive"},
        "ownedBy": {
          "type": "nested",
          "properties": {
            "id": {"type": "long"},
            "name": {"type": "keyword", "normalizer": "case_insensitive"}
          }
        },
        "estimatedValue": {
          "type": "nested",
          "properties": {
            "currencyId": {"type": "long"},
            "value": {"type": "float"}
          }
        },
        "estimatedClosureOn": {"type": "date"},
        "associatedContacts": {
          "type": "nested",
          "properties": {
            "id": {"type": "long"},
            "name": {"type": "keyword", "normalizer": "case_insensitive"}
          }
        },
        "company": {
          "type": "nested",
          "properties": {
            "id": {"type": "long"},
            "name": {"type": "keyword", "normalizer": "case_insensitive"}
          }
        },
        "pipeline": {
          "type": "nested",
          "properties": {
            "id": {"type": "long"},
            "name": {"type": "keyword", "normalizer": "case_insensitive"}
          }
        },
        "pipelineStage": {
          "type": "nested",
          "properties": {
            "id": {"type": "long"},
            "name": {"type": "keyword", "normalizer": "case_insensitive"}
          }
        }
      }
    }
  }'
```

#### Company Index Template
```bash
curl -H 'Content-Type: application/json' -XPOST \
  "http://<elasticsearch-host>:<port>/_template/company-index-template" \
  -d '{
    "index_patterns": ["*-company"],
    "settings": {
      "number_of_shards": 2,
      "number_of_replicas": "1",
      "analysis": {
        "normalizer": {
          "case_insensitive": {
            "filter": ["asciifolding", "lowercase"],
            "type": "custom"
          }
        }
      }
    },
    "mappings": {
      "properties": {
        "id": {"type": "long"},
        "tenantId": {"type": "long"},
        "ownerId": {"type": "long"},
        "version": {"type": "integer"},
        "name": {"type": "keyword", "normalizer": "case_insensitive"},
        "website": {"type": "text"},
        "numberOfEmployees": {
          "type": "nested",
          "properties": {
            "id": {"type": "long"},
            "name": {"type": "keyword", "normalizer": "case_insensitive"}
          }
        },
        "annualRevenue": {
          "type": "nested",
          "properties": {
            "currencyId": {"type": "long"},
            "value": {"type": "float"}
          }
        },
        "industry": {
          "type": "nested",
          "properties": {
            "id": {"type": "long"},
            "name": {"type": "keyword", "normalizer": "case_insensitive"}
          }
        },
        "businessType": {
          "type": "nested",
          "properties": {
            "id": {"type": "long"},
            "name": {"type": "keyword", "normalizer": "case_insensitive"}
          }
        },
        "emails": {
          "type": "nested",
          "properties": {
            "id": {"type": "long"},
            "type": {"type": "keyword", "normalizer": "case_insensitive"},
            "value": {"type": "keyword", "normalizer": "case_insensitive"},
            "primary": {"type": "boolean"}
          }
        },
        "phoneNumbers": {
          "type": "nested",
          "properties": {
            "id": {"type": "long"},
            "code": {"type": "keyword", "normalizer": "case_insensitive"},
            "dialCode": {"type": "text"},
            "value": {"type": "keyword"},
            "primary": {"type": "boolean"}
          }
        }
      }
    }
  }'

## 🔍 Query Examples

### Advanced Search Query Structure
```json
{
  "fields": ["name", "ownedBy", "company", "estimatedValue"],
  "jsonRule": {
    "condition": "AND",
    "rules": [
      {
        "field": "name",
        "operator": "contains",
        "value": "enterprise"
      },
      {
        "condition": "OR",
        "rules": [
          {
            "field": "estimatedValue.value",
            "operator": "greater_than",
            "value": 50000
          },
          {
            "field": "pipelineStage.name",
            "operator": "equals",
            "value": "Qualified"
          }
        ]
      }
    ]
  }
}
```

### Supported Query Operators
- **Text Operations**: `contains`, `starts_with`, `ends_with`, `equals`, `not_equals`
- **Numeric Operations**: `greater_than`, `less_than`, `greater_than_or_equal`, `less_than_or_equal`, `between`
- **Date Operations**: `before`, `after`, `between_dates`, `last_n_days`, `next_n_days`
- **List Operations**: `in`, `not_in`, `is_empty`, `is_not_empty`
- **Boolean Operations**: `is_true`, `is_false`

## 🚨 Troubleshooting

### Common Issues

#### Elasticsearch Connection Issues
```bash
# Check Elasticsearch health
curl http://localhost:9200/_cluster/health

# Check index status
curl http://localhost:9200/_cat/indices?v

# Verify index mappings
curl http://localhost:9200/{tenant-id}-deal/_mapping
```

#### Database Connection Problems
```bash
# Check PostgreSQL connection
psql -h localhost -U postgres -d search -c "SELECT 1;"

# Monitor connection pool
curl http://localhost:8083/actuator/metrics/hikaricp.connections.active
```

#### Performance Issues
```bash
# Check JVM memory usage
curl http://localhost:8083/actuator/metrics/jvm.memory.used

# Monitor garbage collection
curl http://localhost:8083/actuator/metrics/jvm.gc.pause

# Check cache hit rates
curl http://localhost:8083/actuator/metrics/cache.gets
```

### Debug Logging
```properties
# Enable debug logging for search operations
logging.level.com.sell.search.service.SearchService=DEBUG

# Enable Elasticsearch query logging
logging.level.org.elasticsearch.client=TRACE

# Enable SQL logging
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
```

## ⚡ Performance Optimization

### Elasticsearch Optimization
- **Index Sharding**: Optimize shard count based on data volume
- **Replica Configuration**: Balance availability vs. performance
- **Query Optimization**: Use filters instead of queries when possible
- **Bulk Operations**: Batch index updates for better throughput

### Caching Strategy
- **Memcached**: Cache frequently accessed lookup data
- **Query Result Caching**: Cache search results for common queries
- **Field Metadata Caching**: Cache entity field definitions

### Database Optimization
- **Connection Pooling**: Tune HikariCP settings for optimal performance
- **Query Optimization**: Use appropriate indexes and query patterns
- **Batch Processing**: Minimize database round trips

## 📋 Operational Procedures

### Index Management
```bash
# Create new index
curl -X PUT "localhost:9200/{tenant-id}-deal"

# Update index mapping
curl -X PUT "localhost:9200/{tenant-id}-deal/_mapping" \
  -H 'Content-Type: application/json' \
  -d @mapping.json

# Reindex data
curl -X POST "localhost:9200/_reindex" \
  -H 'Content-Type: application/json' \
  -d '{
    "source": {"index": "old-index"},
    "dest": {"index": "new-index"}
  }'
```

### Backup Procedures
```bash
# Database backup
pg_dump -h localhost -U postgres search > search_backup.sql

# Elasticsearch snapshot
curl -X PUT "localhost:9200/_snapshot/backup_repo/snapshot_1"
```

### Scaling Considerations
- **Horizontal Scaling**: Add more application instances behind load balancer
- **Elasticsearch Scaling**: Add more nodes to the cluster
- **Database Scaling**: Consider read replicas for read-heavy workloads
- **Cache Scaling**: Distribute cache across multiple Memcached instances

## 📚 Additional Resources

### Documentation Links
- [Spring Boot Documentation](https://spring.io/projects/spring-boot)
- [Elasticsearch Guide](https://www.elastic.co/guide/en/elasticsearch/reference/6.6/index.html)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Docker Documentation](https://docs.docker.com/)

### Development Tools
- **Postman Collection**: Import API endpoints for testing
- **Elasticsearch Head**: Web interface for Elasticsearch management
- **pgAdmin**: PostgreSQL administration tool
- **Kibana**: Elasticsearch data visualization (if available)

### Support and Maintenance
- **Log Monitoring**: Centralized logging with ELK stack
- **Error Tracking**: Application error monitoring and alerting
- **Performance Monitoring**: APM tools for application performance
- **Security Scanning**: Regular vulnerability assessments

---

## 📄 License

This project is proprietary software developed for internal use.

## 🤝 Contributing

Please follow the established development workflow:
1. Create feature branch from `dev`
2. Implement changes with appropriate tests
3. Submit pull request for code review
4. Deploy to QA environment for testing
5. Merge to `dev` after approval

For questions or support, contact the development team.
```
