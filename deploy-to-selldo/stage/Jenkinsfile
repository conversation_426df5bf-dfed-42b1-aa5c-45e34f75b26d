podTemplate(
    containers: [
        containerTemplate(name: 'helm', alwaysPullImage: true, image: 'nexus.sling-dev.com:8023/lachlanevenson/k8s-helm:v3.4.2', command: 'cat',
            ttyEnabled: true),
        containerTemplate(name: 'curl', alwaysPullImage: true, image: 'nexus.sling-dev.com:8023/sling/jenkins/curl', command: 'cat', ttyEnabled: true)
    ],
    imagePullSecrets: ['registry-credentials']) {
  properties([parameters(
      [string(name: 'dockerImageTag', defaultValue: 'latest', description: 'Docker image tag to deploy'),
       string(name: 'branchName', defaultValue: 'master', description: 'Branch being deployed'),
       string(name: 'triggeredByJob', defaultValue: 'manual', description: 'Information of which job triggered the build')])]
  )

  currentBuild.description = "branch ${params.branchName}-${params.dockerImageTag}"
  node(POD_LABEL) {
    try {
      if (params.branchName != 'master') {
        stage('Approval for Sell Do Stage Deployment') {
                userInput = input(id: 'confirm', message: 'Do you wish to deploy to sell do Stage environment?',
                    parameters: [[$class: 'BooleanParameterDefinition', defaultValue: false, description: 'This will deploy the current build in sell do STAGE environment', name: 'confirm']])
        }
      }
      container('helm') {
        withCredentials([[$class       : 'FileBinding',
                          credentialsId: 'selldo-stage-kubeconfig',
                          variable     : 'KUBECONFIG'],
                         [$class       : 'StringBinding',
                          credentialsId: 'sd-charts-github-api-token',
                          variable     : 'API_TOKEN']]) {
          stage('Add Helm repository') {
            sh script: "helm repo add stable 'https://charts.helm.sh/stable'",
                label: 'Add stable helm repo'
            sh script: "helm repo add sd-charts 'https://${API_TOKEN}@raw.githubusercontent.com/amuratech/sd-charts/master/'",
                label: 'Add helm repo'
            sh script: 'helm repo list', label: 'List available helm repos'
          }
           withCredentials([
                           [$class       : 'StringBinding',
                            credentialsId: 'selldo-stage-application-namespace',
                            variable     : 'APP_NAMESPACE'],
                           [$class       : 'StringBinding',
                            credentialsId: 'selldo-stage-env-postgres-password',
                            variable     : 'POSTGRES_PASSWORD'],
                           [$class       : 'StringBinding',
                            credentialsId: 'selldo-stage-env-postgres-username',
                            variable     : 'POSTGRES_USERNAME'],
                           [$class       : 'StringBinding',
                            credentialsId: 'selldo-stage-env-rabbitmq-password',
                            variable     : 'RABBITMQ_PASSWORD'],
                           [$class       : 'StringBinding',
                            credentialsId: 'selldo-stage-postgres-hostname',
                            variable     : 'POSTGRES_HOST'],
                           [$class       : 'StringBinding',
                            credentialsId: 'selldo-stage-env-rabbitmq-username',
                            variable     : 'RABBITMQ_USERNAME']]) {
            stage('Deploy') {
              echo "Deploying docker release -> nexus.sling-dev.com/8023/sling/sd-search:${params.dockerImageTag}"
              sh script: "helm upgrade --install sd-search sd-charts/sd-search " +
                  "--set  "+
                 "appConfig.postgres.username=${POSTGRES_USERNAME},"+
                 "appConfig.postgres.password=${POSTGRES_PASSWORD},"+
                 "appConfig.postgres.hostname=${POSTGRES_HOST},"+
                 "appConfig.rabbitmq.username=${RABBITMQ_USERNAME},"+
                 "appConfig.rabbitmq.password=${RABBITMQ_PASSWORD},"+
                 "image.tag=${params.dockerImageTag}," +
                 "namespace=${APP_NAMESPACE}," +
                 "appConfig.activeProfile=STAGE," +
                 "deployment.annotations.buildNumber=${currentBuild.number} " +
                 "--wait",
                  label: 'Install helm release'
            }
          }
        }
      }
      container('curl') {
        withCredentials([[$class      : 'StringBinding',
                         credentialsId: 'selldo-stage-api-host',
                         variable     : 'SELL_DO_STAGE_API_HOST']]) {
          stage('Refresh Gateway routes') {
            sh script: "curl -X " +
                        "POST http://${SELL_DO_STAGE_API_HOST}/actuator/gateway/refresh " +
                        "-H 'Accept: application/json' " +
                        "-H 'Host: ${SELL_DO_STAGE_API_HOST}' " +
                        "-H 'cache-control: no-cache'",
               label: 'Force refresh routes cache'
          }
        }
      }
      try {
            stage('Approval for Sell Do PROD Deployment') {
                userInput = input(id: 'confirm', message: 'Do you wish to deploy to sell do PROD environment?',
                    parameters: [[$class: 'BooleanParameterDefinition', defaultValue: false, description: 'This will deploy to sell do PROD environment', name: 'confirm']])
            }
            stage('Start Sell Do PROD Deployment') {
            build job: './deploy-to-prod',
                parameters: [[$class: 'StringParameterValue', name: 'dockerImageTag', value: params.dockerImageTag],
                             [$class: 'StringParameterValue', name: 'triggeredByJob', value: "deploy-to-prod : #${BUILD_NUMBER}"],
                             [$class: 'StringParameterValue', name: 'branchName', value: "${params.branchName}-${params.dockerImageTag}"]],
                wait: false
            }
        }
        catch (err) {
          def user = err.getCauses()[0].getUser()
          userInput = false
          echo "Aborted by: [${user}]"
        }
    }
    catch (err) {
      def user = err.getCauses()[0].getUser()
      userInput = false
      echo "Aborted by: [${user}]"
    }
  }
}
