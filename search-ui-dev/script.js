
var rules_basic = {
  condition: 'AND',
  rules: [{
    id: 'price',
    operator: 'less',
    value: 10.25
  }, {
    condition: 'OR',
    rules: [{
      id: 'category',
      operator: 'equal',
      value: 2
    }, {
      id: 'category',
      operator: 'equal',
      value: 1
    }]
  }]
};


var stringFields = [
'firstName', 
'city',
'lastName',
'state',
'country',
'companyName',
'companyCity',
'companyState',
'companyCountry',
'requirementName',
'address'
]



var getStringOperators = function(){
  return ['equal', 'not_equal', 'in', 'not_in', 'contains', 'not_contains', 'is_not_null', 'is_null']
}

var numberFields =[
'companyAnnualRevenue'
]

var getNumberOperators = function(){
  return ['equal', 'not_equal', 'greater', 'greater_or_equal', 'less', 'less_or_equal', 'between','not_between' , 
  'in', 'not_in', 'is_not_null', 'is_null' ]
}


var dateFields = [
  'expectedClosureOn'
]

var getDateOperators = function(){
  return ['equal', 'not_equal', 'greater', 'greater_or_equal', 'less', 'less_or_equal', 'between','not_between' , 
  'in', 'not_in', 'is_not_null', 'is_null' ]
}

var getStringFilter = function(fieldName){
  return {
    id: fieldName,
    label: fieldName,
    type: 'string',
    input : 'text',
    operators: getStringOperators()
  }
}


var getNumberFilter = function(fieldName){
  return {
    id: fieldName,
    label: fieldName,
    type: 'double',
    input : 'number',
    operators: getNumberOperators()
  }
}

var getDateFilter = function(fieldName){
  return {
    id: fieldName,
    label: fieldName,
    type: 'date',
    input : 'text',
    operators: getDateOperators()
  }
}


var getStringFilters = function(fields){
  var result = []
 for (var f of fields) {
   result.push(getStringFilter(f))
 }
 return result
}


var getNumberFilters = function(fields){
  var result = []
 for (var f of fields) {
   result.push(getNumberFilter(f))
 }
 return result
}


var getDateFilters = function(fields){
  var result = []
 for (var f of fields) {
   result.push(getDateFilter(f))
 }
 return result
}


var getAllFilters = function(stringFields, numberFields, dateFields){
 return [...stringFields, ...numberFields, ...dateFields]
}

//operators: ['equal', 'not_equal', 'in', 'not_in', 'is_null', 'is_not_null']

$('#builder-basic').queryBuilder({
  plugins: ['bt-tooltip-errors'],
  
  filters: getAllFilters(getStringFilters(stringFields), getNumberFilters(numberFields), getDateFilters(dateFields))//, 

  //rules: rules_basic
});

$('#btn-reset').on('click', function() {
  $('#builder-basic').queryBuilder('reset');
});

$('#btn-set').on('click', function() {
  $('#builder-basic').queryBuilder('setRules', rules_basic);
});

$('#btn-get').on('click', function() {
  var result = $('#builder-basic').queryBuilder('getRules');
  
  if (!$.isEmptyObject(result)) {
    $("#modal-body").text(JSON.stringify(result, null, 2))
    $("#myModal").modal()
  }
});
