<html>
   <head>
      <script src="https://code.jquery.com/jquery-1.11.2.min.js"></script>
      <script src="https://cdn.jsdelivr.net/npm/jquery-extendext@0.1.2/jQuery.extendext.min.js"></script>
      <script src="https://cdn.jsdelivr.net/npm/jQuery-QueryBuilder@2.5.2/dist/js/query-builder.min.js" ></script>
      <script src="query-builder.standalone.js"></script>
      <script src="https://netdna.bootstrapcdn.com/bootstrap/3.3.1/js/bootstrap.min.js"></script> 
      <!--<script src="script.js"></script>-->
      <link href="https://netdna.bootstrapcdn.com/bootstrap/3.3.1/css/bootstrap.min.css" rel="stylesheet"/>
      <link href="https://querybuilder.js.org/node_modules/bootstrap-datepicker/dist/css/bootstrap-datepicker3.min.css" rel="stylesheet"/>
      <link href="https://querybuilder.js.org/node_modules/bootstrap-slider/dist/css/bootstrap-slider.min.css" rel="stylesheet"/>
      <link href="https://querybuilder.js.org/node_modules/selectize/dist/css/selectize.bootstrap3.css" rel="stylesheet"/>
      <link href="https://querybuilder.js.org/node_modules/bootstrap-select/dist/css/bootstrap-select.min.css" rel="stylesheet"/>
      <link href="https://querybuilder.js.org/node_modules/awesome-bootstrap-checkbox/awesome-bootstrap-checkbox.css" rel="stylesheet"/>
      <link href="https://querybuilder.js.org/node_modules/jQuery-QueryBuilder/dist/css/query-builder.default.min.css" rel="stylesheet"/>
   </head>
   <body>
   <!-- Modal -->
  <div class="modal fade" id="myModal" role="dialog">
    <div class="modal-dialog modal-lg">
    
      <!-- Modal content-->
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal">&times;</button>
          <h4 class="modal-title">Modal Header</h4>
        </div>
        <div class="modal-body" >
          <pre id="modal-body"> Some text in the modal.</pre>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        </div>
      </div>
      
    </div>
  </div>

      <div class="container bs-docs-container">
         <div class="row">
            <div class="col-md-9" role="main">
               <section class="bs-docs-section clearfix">
                  <h1 id="basic" class="page-header">
                     Basic
                  </h1>
                  <div class="col-md-12">
                     <div id="builder-basic"></div>
                     <div class="btn-group">
                        <button class="btn btn-warning reset" data-target="basic" id="btn-reset">Reset</button>
                        <button class="btn btn-success set-json" data-target="basic" id="btn-set">Set rules</button>
                        <button class="btn btn-primary parse-json" data-target="basic" id="btn-get" >Get rules</button>
                     </div>
                  </div>
               </section>
            </div>
         </div>
      </div>
      <script src="script.js"></script>
   </body>
</html>